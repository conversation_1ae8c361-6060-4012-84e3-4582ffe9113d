/**
 * Simple Frame Selection Test
 * Tests frame selection functionality without external dependencies
 */

const fs = require('fs');
const path = require('path');

// Simple DOM mock
global.window = {
  RoyalPortraitData: {
    frames: [
      { id: 'classic-gold', name: 'Classic Gold', base_price: 15.0, filename: 'frame-classic-gold.png' },
      { id: 'modern-silver', name: 'Modern Silver', base_price: 18.0, filename: 'frame-modern-silver.png' },
    ],
  },
};

global.document = {
  createElement: () => ({ className: '', setAttribute: () => {}, innerHTML: '' }),
  querySelector: () => null,
  querySelectorAll: () => [],
};

global.console = {
  log: () => {},
  warn: () => {},
  error: () => {},
};

// Load and extract only the FrameLibrary class
const scriptPath = path.join(__dirname, 'assets', 'royal-portrait-complete.js');
const scriptContent = fs.readFileSync(scriptPath, 'utf8');

// Extract only the FrameLibrary class definition
const frameLibraryMatch = scriptContent.match(/class FrameLibrary \{[\s\S]*?\n\}/);
if (!frameLibraryMatch) {
  throw new Error('FrameLibrary class not found in script');
}

// Execute only the FrameLibrary class
eval(frameLibraryMatch[0]);

// Test functions
function runTests() {
  console.log('🧪 Running Simple Frame Selection Tests...\n');

  let passed = 0;
  let failed = 0;

  function test(name, testFn) {
    try {
      testFn();
      console.log(`✅ ${name}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      failed++;
    }
  }

  // Test 1: FrameLibrary class exists
  test('FrameLibrary class should exist', () => {
    if (typeof FrameLibrary === 'undefined') {
      throw new Error('FrameLibrary class not found');
    }
  });

  // Test 2: FrameLibrary can be instantiated
  test('FrameLibrary should be instantiable', () => {
    const frameLib = new FrameLibrary();
    if (!frameLib) {
      throw new Error('Failed to create FrameLibrary instance');
    }
  });

  // Test 3: Frame data loading
  test('FrameLibrary should load frame data', async () => {
    const frameLib = new FrameLibrary();
    const frames = await frameLib.loadFrames();

    if (!frames || frames.length === 0) {
      throw new Error('No frames loaded');
    }

    if (frames.length !== 2) {
      throw new Error(`Expected 2 frames, got ${frames.length}`);
    }
  });

  // Test 4: getFrames method (test compatibility)
  test('FrameLibrary should have getFrames method', async () => {
    const frameLib = new FrameLibrary();
    await frameLib.loadFrames();

    const frames = frameLib.getFrames();
    if (!frames || frames.length === 0) {
      throw new Error('getFrames returned no frames');
    }
  });

  // Test 5: loadFrameData method (test compatibility)
  test('FrameLibrary should have loadFrameData method', async () => {
    const frameLib = new FrameLibrary();

    const mockData = [{ id: 'test-frame', name: 'Test Frame', base_price: 10.0 }];

    const frames = await frameLib.loadFrameData(mockData);
    if (!frames || frames.length !== 1) {
      throw new Error('loadFrameData failed to load mock data');
    }
  });

  // Test 6: getFramePrice method (test compatibility)
  test('FrameLibrary should calculate frame prices', async () => {
    const frameLib = new FrameLibrary();
    await frameLib.loadFrames();

    const price = frameLib.getFramePrice('classic-gold');
    if (typeof price !== 'number') {
      throw new Error('Frame price should be a number');
    }

    if (price !== 15.0) {
      throw new Error(`Expected price 15.00, got ${price}`);
    }
  });

  // Test 7: Frame price with size
  test('FrameLibrary should calculate frame prices with size', async () => {
    const frameLib = new FrameLibrary();
    await frameLib.loadFrames();

    // Test with null size (should not throw error)
    const priceNoSize = frameLib.getFramePrice('classic-gold', null);
    if (typeof priceNoSize !== 'number') {
      throw new Error('Frame price with null size should be a number');
    }

    // Test with invalid size (should return base price)
    const priceInvalidSize = frameLib.getFramePrice('classic-gold', 'invalid-size');
    if (priceInvalidSize !== 15.0) {
      throw new Error(`Expected base price 15.00 for invalid size, got ${priceInvalidSize}`);
    }
  });

  // Test 8: Frame validation
  test('FrameLibrary should validate frames', async () => {
    const frameLib = new FrameLibrary();
    await frameLib.loadFrames();

    const frame = frameLib.getFrameById('classic-gold');
    const isValid = frameLib.validateFrame(frame);

    if (!isValid) {
      throw new Error('Valid frame should pass validation');
    }

    const invalidFrame = { id: 'test' }; // Missing required fields
    const isInvalid = frameLib.validateFrame(invalidFrame);

    if (isInvalid) {
      throw new Error('Invalid frame should fail validation');
    }
  });

  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);

  if (failed > 0) {
    console.log('\n🔍 Issues found that need to be addressed');
    process.exit(1);
  } else {
    console.log('\n🎉 All frame selection tests passed!');
    process.exit(0);
  }
}

// Run the tests
runTests();
