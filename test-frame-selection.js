/**
 * Frame Selection Test Runner
 * Simple test runner to check frame selection functionality without Jest
 */

const fs = require('fs');
const path = require('path');

// Mock DOM environment
const { JSDOM } = require('jsdom');
const dom = new JSDOM(`
  <!DOCTYPE html>
  <html>
    <body>
      <div id="portrait-builder-container">
        <div data-frame-grid></div>
        <div data-frame-toggle>
          <button data-frame-toggle-option="frame" class="frame-toggle__option--active"></button>
          <button data-frame-toggle-option="no-frame"></button>
        </div>
        <canvas id="portrait-canvas"></canvas>
        <div data-step="4">
          <div data-step-message></div>
        </div>
      </div>
    </body>
  </html>
`);

global.window = dom.window;
global.document = dom.window.document;
global.HTMLElement = dom.window.HTMLElement;
global.HTMLCanvasElement = dom.window.HTMLCanvasElement;
global.requestAnimationFrame = (cb) => setTimeout(cb, 16);
global.performance = { now: () => Date.now() };

// Mock canvas context
const mockCanvasContext = {
  drawImage: () => {},
  clearRect: () => {},
  fillRect: () => {},
  strokeRect: () => {},
  save: () => {},
  restore: () => {},
  translate: () => {},
  scale: () => {},
  rotate: () => {},
  globalAlpha: 1,
  fillStyle: '#000000',
  strokeStyle: '#000000',
  lineWidth: 1,
  canvas: { width: 400, height: 400 }
};

// Mock HTMLCanvasElement.getContext
global.HTMLCanvasElement.prototype.getContext = () => mockCanvasContext;

// Load the main script
const scriptPath = path.join(__dirname, 'assets', 'royal-portrait-complete.js');
const scriptContent = fs.readFileSync(scriptPath, 'utf8');

// Execute the script in our mock environment
eval(scriptContent);

// Test functions
function runTests() {
  console.log('🧪 Running Frame Selection Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  function test(name, testFn) {
    try {
      testFn();
      console.log(`✅ ${name}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      failed++;
    }
  }
  
  // Test 1: Frame Library Initialization
  test('Frame Library should initialize', () => {
    if (typeof window.FrameLibrary === 'undefined') {
      throw new Error('FrameLibrary not found in global scope');
    }
    
    const frameLib = new window.FrameLibrary();
    if (!frameLib) {
      throw new Error('Failed to create FrameLibrary instance');
    }
  });
  
  // Test 2: Frame Data Loading
  test('Frame Library should load frame data', () => {
    const frameLib = new window.FrameLibrary();
    
    // Mock frame data
    const mockFrameData = [
      { id: 'classic-gold', name: 'Classic Gold', price: 15.00, image: 'frame-classic-gold.png' },
      { id: 'modern-silver', name: 'Modern Silver', price: 18.00, image: 'frame-modern-silver.png' }
    ];
    
    frameLib.loadFrameData(mockFrameData);
    
    const frames = frameLib.getFrames();
    if (!frames || frames.length === 0) {
      throw new Error('Frame data not loaded properly');
    }
    
    if (frames.length !== mockFrameData.length) {
      throw new Error(`Expected ${mockFrameData.length} frames, got ${frames.length}`);
    }
  });
  
  // Test 3: Frame Price Calculation
  test('Frame Library should calculate prices correctly', () => {
    const frameLib = new window.FrameLibrary();
    
    const mockFrameData = [
      { id: 'classic-gold', name: 'Classic Gold', price: 15.00, image: 'frame-classic-gold.png' }
    ];
    
    frameLib.loadFrameData(mockFrameData);
    
    const price = frameLib.getFramePrice('classic-gold', '8x10');
    if (typeof price !== 'number') {
      throw new Error('Frame price should be a number');
    }
    
    if (price <= 0) {
      throw new Error('Frame price should be positive');
    }
  });
  
  // Test 4: Frame Selection Interface
  test('Frame Selection should create interface elements', () => {
    if (typeof window.FrameSelection === 'undefined') {
      throw new Error('FrameSelection not found in global scope');
    }
    
    const frameSelection = new window.FrameSelection();
    if (!frameSelection) {
      throw new Error('Failed to create FrameSelection instance');
    }
    
    // Check if required methods exist
    const requiredMethods = ['init', 'createFrameCard', 'selectFrame', 'updatePreview'];
    for (const method of requiredMethods) {
      if (typeof frameSelection[method] !== 'function') {
        throw new Error(`Missing required method: ${method}`);
      }
    }
  });
  
  // Test 5: Canvas Integration
  test('Frame Selection should integrate with canvas', () => {
    const frameSelection = new window.FrameSelection();
    
    // Mock canvas element
    const canvas = document.getElementById('portrait-canvas');
    if (!canvas) {
      throw new Error('Canvas element not found');
    }
    
    // Test canvas integration
    frameSelection.init();
    
    // This should not throw an error
    frameSelection.updatePreview('classic-gold');
  });
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed > 0) {
    console.log('\n🔍 Issues found that need to be addressed:');
    process.exit(1);
  } else {
    console.log('\n🎉 All tests passed!');
    process.exit(0);
  }
}

// Run the tests
runTests();
