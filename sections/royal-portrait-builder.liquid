{{ 'royal-portrait-builder.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<!-- Portrait Builder Configuration -->
<script>
  window.portraitBuilderConfig = {
    productId: 'royal-portrait-custom',
    variants: [
      ['small-classic-gold', '44000000000001'],
      ['small-ornate-silver', '44000000000002'],
      ['small-modern-black', '44000000000003'],
      ['medium-classic-gold', '44000000000004'],
      ['medium-ornate-silver', '44000000000005'],
      ['medium-modern-black', '44000000000006'],
      ['large-classic-gold', '44000000000007'],
      ['large-ornate-silver', '44000000000008'],
      ['large-modern-black', '44000000000009'],
    ],
  };
</script>

<!-- TEMPORARY: Canvas debugging instrumentation -->
<script src="{{ 'canvas-debugging-instrumentation.js' | asset_url }}"></script>
<!-- Phase 2: Canvas State Protection -->
<script src="{{ 'canvas-state-protector.js' | asset_url }}"></script>
<script src="{{ 'royal-portrait-complete.js' | asset_url }}" defer="defer"></script>

<div class="royal-portrait-builder section-{{ section.id }}-padding color-{{ section.settings.color_scheme }} gradient">
  <div class="page-width">
    {%- if section.settings.heading != blank -%}
      <div class="royal-portrait-builder__header">
        <h2 class="royal-portrait-builder__heading {{ section.settings.heading_size }}">
          {{ section.settings.heading }}
        </h2>
        {%- if section.settings.description != blank -%}
          <div class="royal-portrait-builder__description rte">
            {{ section.settings.description }}
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}

    <div class="royal-portrait-builder__steps" data-portrait-steps>
      <!-- Step 1: Choose Your Pet -->
      <div class="step-container" data-step="1" data-step-active="true">
        <div class="step-header">
          <h3 class="step-title">
            {{ 'portrait_builder.choose_pet.label' | t | default: 'Choose Your Pet' }}
          </h3>
          <p class="step-description">Select your pet's breed or upload your own photo to create your royal portrait</p>
          <div class="step-requirement" data-step-message>
            <p class="step-requirement__message">Choose a breed or upload a photo to get started</p>
          </div>
        </div>

        <!-- Breed Selection Content -->
        <div class="step-content step-content--active" data-step-content="breed-selection">
          <div class="breed-picker" data-breed-picker>
            <!-- Popular Breeds Carousel -->
            <div class="popular-breeds-section">
              <h4 class="popular-breeds__title">Popular Breeds</h4>
              <div class="popular-breeds-carousel" data-popular-carousel>
                <div class="popular-breeds-carousel__container" data-carousel-container>
                  <!-- Popular breeds will be populated by JavaScript -->
                </div>
                <button
                  type="button"
                  class="carousel-nav carousel-nav--prev"
                  data-carousel-prev
                  aria-label="Previous breeds"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="15,18 9,12 15,6"></polyline>
                  </svg>
                </button>
                <button
                  type="button"
                  class="carousel-nav carousel-nav--next"
                  data-carousel-next
                  aria-label="Next breeds"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9,18 15,12 9,6"></polyline>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Search and Full Grid -->
            <div class="breed-picker__search-section">
              <div class="breed-picker__search">
                <input
                  type="text"
                  class="breed-picker__search-input field__input"
                  placeholder="{{ 'portrait_builder.breed_picker.search_placeholder' | t | default: 'Search all breeds...' }}"
                  data-breed-search
                  autocomplete="off"
                >
              </div>

              <!-- Breed Category Filters -->
              <div class="breed-categories-container" data-breed-categories>
                <!-- Category tabs will be populated by JavaScript -->
              </div>

              <!-- Size Filters -->
              <div class="size-filters-container" data-size-filters>
                <!-- Size filters will be populated by JavaScript -->
              </div>

              <!-- OR Separator -->
              <div class="pet-selection-separator">
                <div class="pet-selection-separator__line"></div>
                <span class="pet-selection-separator__text">OR</span>
                <div class="pet-selection-separator__line"></div>
              </div>

              <!-- Photo Upload Section -->
              <div class="photo-upload-inline" data-photo-upload>
                <div class="photo-upload__dropzone" data-dropzone>
                  <div class="photo-upload__icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                      <circle cx="12" cy="13" r="4"></circle>
                    </svg>
                  </div>
                  <div class="photo-upload__text">
                    <p class="photo-upload__primary">
                      {{ 'portrait_builder.upload_photo.drag_drop' | t | default: "Add Your Pet's Photo" }}
                    </p>
                    <p class="photo-upload__secondary">
                      {{
                        'portrait_builder.upload_photo.supported_formats'
                        | t
                        | default: 'Drag & drop or click to browse'
                      }}
                    </p>
                  </div>
                  <button type="button" class="button button--secondary photo-upload__browse" data-browse-button>
                    {{ 'portrait_builder.upload_photo.browse' | t | default: 'Choose Photo' }}
                  </button>
                  <input type="file" class="photo-upload__input" data-file-input accept="image/jpeg,image/png" hidden>
                </div>
                <div class="photo-upload__preview" data-upload-preview style="display: none;">
                  <img
                    class="photo-upload__preview-image"
                    data-preview-image
                    alt="Uploaded photo preview"
                    width="150"
                    height="150"
                    loading="lazy"
                  >
                  <button type="button" class="photo-upload__remove button button--tertiary" data-remove-upload>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
                <div class="photo-upload__status" data-upload-status></div>
              </div>

              <div class="breed-picker__see-all">
                <button
                  type="button"
                  class="button button--secondary button--small"
                  data-see-all-breeds
                  style="display: none;"
                >
                  {{ 'portrait_builder.breed_picker.see_all' | t | default: 'See All Breeds' }}
                </button>
              </div>
              <div class="breed-picker__results" data-breed-results>
                <div class="breed-picker__loading" data-breed-loading style="display: none;">
                  {% render 'loading-spinner' %}
                </div>
                <div class="breed-picker__grid" data-breed-grid></div>
                <div class="breed-picker__no-results" data-breed-no-results style="display: none;">
                  {{ 'portrait_builder.breed_picker.no_results' | t | default: 'No breeds found' }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Progressive Preview Canvas -->
        <div class="progressive-preview" data-progressive-preview>
          <div class="progressive-preview__header">
            <h4 class="progressive-preview__title">Your Portrait Preview</h4>
            <p class="progressive-preview__description">
              Watch your royal portrait come to life as you make selections
            </p>
          </div>
          <div class="progressive-preview__canvas-container">
            <canvas
              class="progressive-preview__canvas"
              data-progressive-canvas
              width="300"
              height="300"
            ></canvas>
            <div class="progressive-preview__placeholder" data-preview-placeholder>
              <div class="progressive-preview__icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <p class="progressive-preview__placeholder-text">Your portrait will appear here</p>
            </div>
          </div>
        </div>

        <div class="step-navigation">
          <button type="button" class="button button--secondary" data-step-back disabled>Back</button>
          <button type="button" class="button button--primary button--disabled" data-step-next disabled>
            Choose Your Pet to Continue
          </button>
        </div>
      </div>

      <!-- Step 2: Costume Selection -->
      <div class="step-container" data-step="2" data-step-active="false" style="display: none;">
        <div class="step-header">
          <h3 class="step-title">
            {{ 'portrait_builder.costume_selector.label' | t | default: 'Choose a Royal Costume' }}
          </h3>
          <p class="step-description">Add a royal costume to make your pet's portrait truly majestic (optional)</p>
          <div class="step-message" data-step-message></div>
        </div>
        <div class="step-content" data-step-content="costume-selection">
          <div class="costume-selector" data-costume-selector>
            <div class="costume-options">
              <div class="costume-option costume-option--none" data-costume-id="none">
                <div class="costume-option__preview">
                  <div class="costume-option__no-costume">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <circle cx="12" cy="12" r="10"/>
                      <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"/>
                    </svg>
                  </div>
                </div>
                <h4 class="costume-option__name">No Costume</h4>
                <p class="costume-option__description">Keep your pet's natural look</p>
                <div class="costume-option__price">Free</div>
              </div>

              <div class="costume-option" data-costume-id="crown">
                <div class="costume-option__preview">
                  <img
                    src="{{ 'costume-crown.png' | asset_url }}"
                    alt="Royal Crown"
                    loading="lazy"
                    width="80"
                    height="80"
                  >
                </div>
                <h4 class="costume-option__name">Royal Crown</h4>
                <p class="costume-option__description">A majestic golden crown</p>
                <div class="costume-option__price">+$5.00</div>
              </div>

              <div class="costume-option" data-costume-id="cape">
                <div class="costume-option__preview">
                  <img
                    src="{{ 'costume-cape.png' | asset_url }}"
                    alt="Royal Cape"
                    loading="lazy"
                    width="80"
                    height="80"
                  >
                </div>
                <h4 class="costume-option__name">Royal Cape</h4>
                <p class="costume-option__description">An elegant royal cape</p>
                <div class="costume-option__price">+$7.00</div>
              </div>
            </div>
          </div>
        </div>
        <div class="step-navigation">
          <button type="button" class="button button--secondary" data-step-back>Back</button>
          <button type="button" class="button button--tertiary" data-skip-costume>Skip Costume - Continue</button>
          <button type="button" class="button button--primary" data-step-next>Continue</button>
        </div>
      </div>

      <!-- Step 3: Background Selection -->
      <div class="step-container" data-step="3" data-step-active="false" style="display: none;">
        <div class="step-header">
          <h3 class="step-title">
            {{ 'portrait_builder.background_selector.label' | t | default: 'Choose Background' }}
          </h3>
          <p class="step-description">Select a background color or pattern for your royal portrait</p>
          <div class="step-message" data-step-message></div>
        </div>
        <div class="step-content" data-step-content="background-selection">
          <div class="background-selector" data-background-selector>
            <div class="background-options">
              <div
                class="background-option background-option--selected"
                data-background-type="color"
                data-background-value="#ffffff"
              >
                <div
                  class="background-option__preview"
                  style="background-color: #ffffff; border: 2px solid #ddd;"
                ></div>
                <h4 class="background-option__name">Classic White</h4>
              </div>

              <div class="background-option" data-background-type="color" data-background-value="#f8f4e6">
                <div class="background-option__preview" style="background-color: #f8f4e6;"></div>
                <h4 class="background-option__name">Royal Cream</h4>
              </div>

              <div class="background-option" data-background-type="color" data-background-value="#1a1a2e">
                <div class="background-option__preview" style="background-color: #1a1a2e;"></div>
                <h4 class="background-option__name">Royal Navy</h4>
              </div>

              <div class="background-option" data-background-type="color" data-background-value="#8b0000">
                <div class="background-option__preview" style="background-color: #8b0000;"></div>
                <h4 class="background-option__name">Royal Burgundy</h4>
              </div>

              <div class="background-option" data-background-type="color" data-background-value="#006400">
                <div class="background-option__preview" style="background-color: #006400;"></div>
                <h4 class="background-option__name">Royal Green</h4>
              </div>

              <div class="background-option" data-background-type="color" data-background-value="#4b0082">
                <div class="background-option__preview" style="background-color: #4b0082;"></div>
                <h4 class="background-option__name">Royal Purple</h4>
              </div>
            </div>
          </div>
        </div>
        <div class="step-navigation">
          <button type="button" class="button button--secondary" data-step-back>Back</button>
          <button type="button" class="button button--primary" data-step-next>Continue</button>
        </div>
      </div>

      <!-- Step 4: Frame Selection -->
      <div class="step-container" data-step="4" data-step-active="false" style="display: none;">
        <div class="step-header">
          <h3 class="step-title">{{ 'portrait_builder.frame_selector.label' | t | default: 'Select Frame Style' }}</h3>
          <p class="step-description">Choose a frame that complements your royal portrait</p>
          <div class="step-message" data-step-message></div>
        </div>
        <div class="step-content" data-step-content="frame-selection">
          <div class="frame-selector" data-frame-selector>
            <!-- Frame/No Frame Toggle -->
            <div class="frame-toggle" data-frame-toggle>
              <button
                type="button"
                class="frame-toggle__option frame-toggle__option--active"
                data-frame-toggle-option="frame"
              >
                <span class="frame-toggle__checkmark">✓</span>
                <span class="frame-toggle__text">FRAME</span>
              </button>
              <button type="button" class="frame-toggle__option" data-frame-toggle-option="no-frame">
                <span class="frame-toggle__text">NO FRAME</span>
              </button>
            </div>

            <!-- Frame Styles Section -->
            <div class="frame-styles" data-frame-styles>
              <div class="frame-styles__section">
                <h4 class="frame-styles__title">Classic styles:</h4>
                <div class="frame-selector__grid" data-frame-grid></div>
              </div>
            </div>
          </div>
        </div>
        <div class="step-navigation">
          <button type="button" class="button button--secondary" data-step-back>Back</button>
          <button type="button" class="button button--primary button--disabled" data-step-next disabled>
            Select a Frame to Continue
          </button>
        </div>
      </div>

      <!-- Step 5: Size Selection -->
      <div class="step-container" data-step="5" data-step-active="false" style="display: none;">
        <div class="step-header">
          <h3 class="step-title">{{ 'portrait_builder.size_selector.label' | t | default: 'Choose Size' }}</h3>
          <p class="step-description">Select the perfect size for your royal portrait</p>
          <div class="step-message" data-step-message></div>
        </div>
        <div class="step-content" data-step-content="size-selection">
          <div class="size-selector" data-size-selector>
            <div class="size-selector__options" data-size-options></div>
          </div>
        </div>
        <div class="step-navigation">
          <button type="button" class="button button--secondary" data-step-back>Back</button>
          <button type="button" class="button button--primary button--disabled" data-step-next disabled>
            Select a Size to Continue
          </button>
        </div>
      </div>

      <!-- Step 6: Final Review & Add to Cart -->
      <div class="step-container" data-step="6" data-step-active="false" style="display: none;">
        <div class="step-header">
          <h3 class="step-title">Review & Order</h3>
          <p class="step-description">Review your royal portrait and add it to your cart</p>
          <div class="step-message" data-step-message></div>
        </div>
        <div class="step-content" data-step-content="final-review">
          <div class="final-review">
            <div class="final-review__preview">
              <div class="portrait-canvas-container">
                <canvas
                  class="portrait-canvas"
                  data-portrait-canvas
                  width="500"
                  height="500"
                ></canvas>
                <div class="portrait-canvas__loading" data-canvas-loading style="display: none;">
                  {% render 'loading-spinner' %}
                  <p>{{ 'portrait_builder.preview.loading' | t | default: 'Loading preview...' }}</p>
                </div>
                <div class="portrait-canvas__error" data-canvas-error style="display: none;">
                  <p>{{ 'portrait_builder.preview.error' | t | default: 'Error loading preview' }}</p>
                </div>
              </div>
            </div>
            <div class="final-review__details">
              <div class="royal-portrait-builder__pricing" data-pricing-display>
                <div class="pricing__total" data-total-price>
                  {{ 'portrait_builder.pricing.total' | t: price: '$0.00' | default: 'Total: $0.00' }}
                </div>
                <div class="pricing__breakdown" data-price-breakdown></div>
              </div>
              <div class="selection-summary" data-selection-summary>
                <!-- Selection summary will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>
        <div class="step-navigation">
          <button type="button" class="button button--secondary" data-step-back>Back</button>
          <button type="button" class="button button--secondary" data-reset-builder>Start Over</button>
          <button type="button" class="button button--primary" data-add-to-cart disabled>
            {{ 'portrait_builder.actions.add_to_cart' | t | default: 'Add to Cart' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Hidden form for cart submission -->
<form class="royal-portrait-form" data-portrait-form style="display: none;">
  <input type="hidden" name="id" data-variant-id>
  <input type="hidden" name="properties[Portrait_Breed]" data-breed-property>
  <input type="hidden" name="properties[Portrait_Frame]" data-frame-property>
  <input type="hidden" name="properties[Portrait_Size]" data-size-property>
  <input type="hidden" name="properties[Portrait_Preview]" data-preview-property>
  <input type="hidden" name="properties[Pet_Photo]" data-photo-property>
</form>

{% schema %}
{
  "name": "Royal Portrait Builder",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Create Your Royal Pet Portrait",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "Small"
        },
        {
          "value": "h1",
          "label": "Medium"
        },
        {
          "value": "h0",
          "label": "Large"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "scheme-1",
          "label": "Scheme 1"
        },
        {
          "value": "scheme-2",
          "label": "Scheme 2"
        },
        {
          "value": "scheme-3",
          "label": "Scheme 3"
        }
      ],
      "default": "scheme-1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Royal Portrait Builder"
    }
  ]
}
{% endschema %}
