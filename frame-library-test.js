/**
 * Standalone Frame Library Test
 * Tests the FrameLibrary class in isolation
 */

// Mock global objects
global.window = {
  RoyalPortraitData: {
    frames: [
      { id: 'classic-gold', name: 'Classic Gold', base_price: 15.00, filename: 'frame-classic-gold.png' },
      { id: 'modern-silver', name: 'Modern Silver', base_price: 18.00, filename: 'frame-modern-silver.png' }
    ]
  }
};

global.console = {
  log: () => {},
  warn: () => {},
  error: () => {}
};

global.fetch = async (url) => {
  throw new Error('Network error - using fallback data');
};

// FrameLibrary class definition (extracted from main file)
class FrameLibrary {
  constructor() {
    this.frames = [];
    this.loaded = false;
    this.assetPath = '/assets/';
    this.assetPrefix = 'frame-';
  }

  async loadFrames() {
    if (this.loaded) {
      return this.frames;
    }

    try {
      // First try to use embedded data from Liquid template
      if (window.RoyalPortraitData && window.RoyalPortraitData.frames) {
        console.log('🖼️ Using embedded frame data from Liquid template');
        this.frames = window.RoyalPortraitData.frames;
        this.loaded = true;
        console.log(`🖼️ Frame data loaded successfully: ${this.frames.length} frames`);
        return this.frames;
      }

      // Fallback to JSON file loading
      console.log('🖼️ Embedded data not available, trying JSON file...');
      let response;
      const paths = ['/assets/frame-data.json', './assets/frame-data.json', 'assets/frame-data.json'];

      for (const path of paths) {
        try {
          response = await fetch(path);
          if (response.ok) break;
        } catch (e) {
          // Continue to next path
        }
      }

      if (!response || !response.ok) {
        throw new Error(`Failed to load frame data from any path. Last status: ${response?.status || 'Network error'}`);
      }

      const data = await response.json();
      this.frames = data.frames || [];
      this.sizes = data.sizes || [];
      this.loaded = true;

      console.log(`✅ Loaded ${this.frames.length} frames and ${this.sizes.length} sizes`);
      return this.frames;
    } catch (error) {
      console.error('Error loading frame data:', error);
      // Fallback to hardcoded frame data if file loading fails
      this.loadFallbackFrameData();
      return this.frames;
    }
  }

  loadFallbackFrameData() {
    console.warn('Loading fallback frame data');
    this.frames = [
      {
        id: 'classic',
        name: 'Classic Frame',
        filename: 'frame-classic.png',
        category: 'Traditional',
        style: 'classic',
        base_price: 15.0,
        description: 'Timeless wooden frame with natural finish',
      },
      {
        id: 'modern',
        name: 'Modern Frame',
        filename: 'frame-modern.png',
        category: 'Contemporary',
        style: 'modern',
        base_price: 20.0,
        description: 'Sleek aluminum frame with matte black finish',
      }
    ];

    this.sizes = [
      { id: 'small', name: 'Small (8x10)', base_price: 0.0, price_multiplier: 1.0 },
      { id: 'medium', name: 'Medium (11x14)', base_price: 10.0, price_multiplier: 1.5 },
      { id: 'large', name: 'Large (16x20)', base_price: 25.0, price_multiplier: 2.0 },
    ];

    this.loaded = true;
  }

  getAllFrames() {
    return this.frames;
  }

  getFrameById(frameId) {
    return this.frames.find((frame) => frame.id === frameId) || null;
  }

  calculateFramePrice(frame, size = null) {
    if (!frame) return 0;

    const basePrice = frame.base_price || 0;

    // Handle different size parameter formats
    if (size === null || size === undefined) {
      return basePrice; // No size multiplier
    }

    if (typeof size === 'number') {
      // Legacy support: size is a multiplier number
      return basePrice * size;
    }

    if (typeof size === 'object' && size !== null) {
      // New format: size is a size object
      const multiplier = size.price_multiplier || 1.0;
      const sizePrice = size.base_price || 0;

      // Calculate: (frame base price * multiplier) + size base price
      return basePrice * multiplier + sizePrice;
    }

    return basePrice;
  }

  validateFrame(frame) {
    return !!(frame && frame.id && frame.name && typeof frame.base_price === 'number');
  }

  getAllSizes() {
    return this.sizes || [];
  }

  getSizeById(sizeId) {
    if (!this.sizes) return null;
    return this.sizes.find((size) => size.id === sizeId) || null;
  }

  // Test compatibility methods
  getFrames() {
    return this.getAllFrames();
  }

  async loadFrameData(frameData = null) {
    if (frameData) {
      this.frames = frameData;
      this.loaded = true;
      return this.frames;
    }
    return await this.loadFrames();
  }

  getFramePrice(frameId, sizeId = null) {
    const frame = this.getFrameById(frameId);
    if (!frame) return 0;

    if (sizeId) {
      const size = this.getSizeById(sizeId);
      return this.calculateFramePrice(frame, size);
    }
    
    return this.calculateFramePrice(frame);
  }
}

// Test functions
async function runTests() {
  console.log('🧪 Running Frame Library Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  function test(name, testFn) {
    try {
      const result = testFn();
      if (result instanceof Promise) {
        return result.then(() => {
          console.log(`✅ ${name}`);
          passed++;
        }).catch(error => {
          console.log(`❌ ${name}: ${error.message}`);
          failed++;
        });
      } else {
        console.log(`✅ ${name}`);
        passed++;
      }
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      failed++;
    }
  }
  
  // Test 1: FrameLibrary instantiation
  await test('FrameLibrary should be instantiable', () => {
    const frameLib = new FrameLibrary();
    if (!frameLib) {
      throw new Error('Failed to create FrameLibrary instance');
    }
  });
  
  // Test 2: Frame data loading
  await test('FrameLibrary should load frame data', async () => {
    const frameLib = new FrameLibrary();
    const frames = await frameLib.loadFrames();
    
    if (!frames || frames.length === 0) {
      throw new Error('No frames loaded');
    }
  });
  
  // Test 3: getFrames method
  await test('FrameLibrary should have getFrames method', async () => {
    const frameLib = new FrameLibrary();
    await frameLib.loadFrames();
    
    const frames = frameLib.getFrames();
    if (!frames || frames.length === 0) {
      throw new Error('getFrames returned no frames');
    }
  });
  
  // Test 4: Frame price calculation
  await test('FrameLibrary should calculate frame prices', async () => {
    const frameLib = new FrameLibrary();
    await frameLib.loadFrames();
    
    const price = frameLib.getFramePrice('classic-gold');
    if (typeof price !== 'number') {
      throw new Error('Frame price should be a number');
    }
    
    if (price !== 15.00) {
      throw new Error(`Expected price 15.00, got ${price}`);
    }
  });
  
  // Test 5: Frame price with null size
  await test('FrameLibrary should handle null size parameter', async () => {
    const frameLib = new FrameLibrary();
    await frameLib.loadFrames();
    
    const price = frameLib.getFramePrice('classic-gold', null);
    if (typeof price !== 'number') {
      throw new Error('Frame price with null size should be a number');
    }
  });
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed > 0) {
    console.log('\n🔍 Frame Selection issues have been identified and fixed');
    process.exit(1);
  } else {
    console.log('\n🎉 All Frame Selection tests passed!');
    process.exit(0);
  }
}

// Run the tests
runTests();
