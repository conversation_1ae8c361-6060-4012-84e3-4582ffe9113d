<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas State Protector Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .canvas-container {
            border: 2px solid #333;
            display: inline-block;
            margin: 10px;
        }
        canvas {
            display: block;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007cba;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .metrics {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🛡️ Canvas State Protector Test</h1>
    <p>This page tests the Canvas State Protector implementation for the Royal Portrait Builder canvas clearing regression fix.</p>

    <div class="test-container">
        <h2>Test Canvas</h2>
        <div class="canvas-container">
            <canvas id="testCanvas" width="400" height="400"></canvas>
        </div>
        
        <div class="controls">
            <button onclick="drawTestContent()">Draw Test Content</button>
            <button onclick="addLayers()">Add Layers</button>
            <button class="danger" onclick="clearCanvas()">Clear Canvas (Test Protection)</button>
            <button onclick="showMetrics()">Show Performance Metrics</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <div class="metrics" id="metricsDisplay" style="display: none;">
        <h3>Performance Metrics</h3>
        <div id="metricsContent"></div>
    </div>

    <div class="log" id="logOutput">
        <strong>Console Log Output:</strong><br>
    </div>

    <!-- Load the Canvas State Protector -->
    <script src="assets/canvas-state-protector.js"></script>
    
    <script>
        // Mock PortraitCanvas class for testing
        class MockPortraitCanvas {
            constructor(canvasElement) {
                this.canvas = canvasElement;
                this.ctx = canvasElement.getContext('2d');
                this.layers = {
                    background: null,
                    breed: null,
                    costume: null,
                    frame: null
                };
            }
        }

        // Initialize test
        let testCanvas;
        let canvasProtector;
        let logElement;

        // Override console.log to capture output
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;

        function logToPage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logOutput');
            const color = type === 'warn' ? 'orange' : type === 'error' ? 'red' : 'black';
            logElement.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToPage(args.join(' '), 'log');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToPage(args.join(' '), 'warn');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };

        // Initialize when page loads
        window.addEventListener('load', function() {
            logElement = document.getElementById('logOutput');
            
            // Create mock canvas instance
            const canvasElement = document.getElementById('testCanvas');
            testCanvas = new MockPortraitCanvas(canvasElement);
            
            // Initialize canvas protector
            if (window.CanvasStateProtector) {
                canvasProtector = new window.CanvasStateProtector(testCanvas, {
                    interval: 100,
                    backupInterval: 500
                });
                console.log('✅ Canvas State Protector initialized successfully');
            } else {
                console.error('❌ CanvasStateProtector not available');
            }
            
            // Draw initial content
            drawTestContent();
        });

        function drawTestContent() {
            const ctx = testCanvas.ctx;
            
            // Clear and draw background
            ctx.fillStyle = '#f0f8ff';
            ctx.fillRect(0, 0, 400, 400);
            
            // Draw a simple pet silhouette
            ctx.fillStyle = '#8B4513';
            ctx.beginPath();
            ctx.ellipse(200, 180, 80, 60, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw ears
            ctx.beginPath();
            ctx.ellipse(160, 140, 25, 35, -0.3, 0, 2 * Math.PI);
            ctx.ellipse(240, 140, 25, 35, 0.3, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw eyes
            ctx.fillStyle = '#000';
            ctx.beginPath();
            ctx.arc(180, 170, 8, 0, 2 * Math.PI);
            ctx.arc(220, 170, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw nose
            ctx.beginPath();
            ctx.arc(200, 190, 5, 0, 2 * Math.PI);
            ctx.fill();
            
            console.log('🎨 Test content drawn on canvas');
        }

        function addLayers() {
            // Simulate adding layers
            testCanvas.layers.breed = { id: 'golden-retriever', name: 'Golden Retriever' };
            testCanvas.layers.costume = { id: 'royal-crown', name: 'Royal Crown' };
            testCanvas.layers.frame = { id: 'ornate', name: 'Ornate Frame' };
            
            // Draw frame overlay
            const ctx = testCanvas.ctx;
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 8;
            ctx.strokeRect(10, 10, 380, 380);
            
            console.log('🎭 Layers added to canvas', testCanvas.layers);
        }

        function clearCanvas() {
            console.log('🚨 MANUAL CANVAS CLEAR - Testing protection system...');
            
            // Clear the canvas (this should trigger protection)
            testCanvas.ctx.clearRect(0, 0, 400, 400);
            testCanvas.ctx.fillStyle = '#ffffff';
            testCanvas.ctx.fillRect(0, 0, 400, 400);
            
            // Clear layers (simulate the bug)
            testCanvas.layers = {
                background: null,
                breed: null,
                costume: null,
                frame: null
            };
            
            console.log('🧹 Canvas and layers cleared manually');
        }

        function showMetrics() {
            if (canvasProtector) {
                const metrics = canvasProtector.getPerformanceMetrics();
                const metricsDisplay = document.getElementById('metricsDisplay');
                const metricsContent = document.getElementById('metricsContent');
                
                metricsContent.innerHTML = `
                    <p><strong>Monitoring:</strong> ${metrics.monitoringCount} checks, avg ${metrics.averageMonitoringTime.toFixed(2)}ms</p>
                    <p><strong>Backups:</strong> ${metrics.backupCount} backups, avg ${metrics.averageBackupTime.toFixed(2)}ms</p>
                    <p><strong>Restorations:</strong> ${metrics.restorationCount} restorations, avg ${metrics.averageRestorationTime.toFixed(2)}ms</p>
                    <p><strong>Total Time:</strong> Monitoring ${metrics.monitoringTime.toFixed(2)}ms, Backup ${metrics.backupTime.toFixed(2)}ms, Restoration ${metrics.restorationTime.toFixed(2)}ms</p>
                `;
                
                metricsDisplay.style.display = 'block';
                console.log('📊 Performance metrics displayed', metrics);
            }
        }

        function clearLog() {
            document.getElementById('logOutput').innerHTML = '<strong>Console Log Output:</strong><br>';
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (canvasProtector) {
                canvasProtector.destroy();
            }
        });
    </script>
</body>
</html>
