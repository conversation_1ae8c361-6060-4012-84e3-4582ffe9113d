<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Canvas Protection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        canvas {
            border: 2px solid #333;
            display: block;
            margin: 10px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007cba;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        button.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🛡️ Canvas State Protector - Simple Test</h1>
    <p>Testing the Canvas State Protector implementation for Task 2.1</p>

    <div class="test-section">
        <h2>Canvas Protection Test</h2>
        <canvas id="testCanvas" width="400" height="300"></canvas>
        
        <div>
            <button onclick="initializeTest()">Initialize Protection</button>
            <button onclick="drawContent()">Draw Test Content</button>
            <button onclick="clearCanvas()" class="danger">Clear Canvas (Test Protection)</button>
            <button onclick="showStatus()">Show Status</button>
        </div>
        
        <div id="statusDisplay"></div>
    </div>

    <div class="log" id="logOutput">
        <strong>Test Log:</strong><br>
    </div>

    <script>
        // Inline Canvas State Protector for testing
        class CanvasStateProtector {
            constructor(canvas, options = {}) {
                this.canvas = canvas;
                this.monitoringInterval = options.interval || 100;
                this.backupInterval = options.backupInterval || 500;
                this.layerBackup = null;
                this.isProtectionEnabled = true;
                this.clearingDetected = false;
                this.performanceMetrics = {
                    monitoringTime: 0,
                    backupTime: 0,
                    restorationTime: 0,
                    monitoringCount: 0,
                    backupCount: 0,
                    restorationCount: 0
                };
                
                this.log('🛡️ Canvas State Protector initialized');
                this.startMonitoring();
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('logOutput');
                logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logElement.scrollTop = logElement.scrollHeight;
                console.log(message);
            }

            startMonitoring() {
                this.log('🛡️ Starting canvas state monitoring...');
                
                this.monitoringTimer = setInterval(() => {
                    if (this.isProtectionEnabled) {
                        this.checkCanvasState();
                    }
                }, this.monitoringInterval);

                this.backupTimer = setInterval(() => {
                    if (this.isProtectionEnabled && this.hasImportantLayers()) {
                        this.backupCanvasState();
                    }
                }, this.backupInterval);
            }

            checkCanvasState() {
                const startTime = performance.now();
                
                try {
                    const hasLayers = this.hasImportantLayers();
                    const isCanvasCleared = this.isCanvasCleared();
                    
                    if (!hasLayers && this.layerBackup && isCanvasCleared && !this.clearingDetected) {
                        this.log('🚨 Canvas clearing detected, initiating restoration');
                        this.clearingDetected = true;
                        this.restoreCanvasState();
                    } else if (hasLayers && this.clearingDetected) {
                        this.clearingDetected = false;
                        this.log('🛡️ Canvas state normalized');
                    }
                    
                    this.performanceMetrics.monitoringTime += performance.now() - startTime;
                    this.performanceMetrics.monitoringCount++;
                    
                } catch (error) {
                    this.log('🛡️ Error during canvas state check: ' + error.message);
                }
            }

            hasImportantLayers() {
                return this.canvas.layers && (
                    this.canvas.layers.breed ||
                    this.canvas.layers.costume ||
                    this.canvas.layers.frame ||
                    this.canvas.layers.background
                );
            }

            isCanvasCleared() {
                if (!this.canvas.canvas) return true;
                
                try {
                    const ctx = this.canvas.canvas.getContext('2d');
                    const imageData = ctx.getImageData(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
                    const data = imageData.data;
                    
                    let nonWhitePixels = 0;
                    const totalPixels = data.length / 4;
                    
                    for (let i = 0; i < data.length; i += 4) {
                        const r = data[i];
                        const g = data[i + 1];
                        const b = data[i + 2];
                        const a = data[i + 3];
                        
                        if (!(r > 250 && g > 250 && b > 250) && a > 0) {
                            nonWhitePixels++;
                        }
                    }
                    
                    return nonWhitePixels < totalPixels * 0.01;
                    
                } catch (error) {
                    this.log('🛡️ Error checking canvas state: ' + error.message);
                    return false;
                }
            }

            backupCanvasState() {
                const startTime = performance.now();
                
                try {
                    this.layerBackup = {
                        layers: JSON.parse(JSON.stringify(this.canvas.layers)),
                        canvasData: this.canvas.canvas.toDataURL(),
                        timestamp: Date.now()
                    };
                    
                    this.log('💾 Canvas state backed up');
                    
                    this.performanceMetrics.backupTime += performance.now() - startTime;
                    this.performanceMetrics.backupCount++;
                    
                } catch (error) {
                    this.log('🛡️ Error backing up canvas state: ' + error.message);
                }
            }

            async restoreCanvasState() {
                if (!this.layerBackup) {
                    this.log('🛡️ No backup available for restoration');
                    return;
                }

                const startTime = performance.now();
                
                try {
                    this.log('🔄 Restoring canvas state from backup');
                    
                    this.canvas.layers = { ...this.layerBackup.layers };
                    
                    const img = new Image();
                    img.onload = () => {
                        const ctx = this.canvas.canvas.getContext('2d');
                        ctx.clearRect(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
                        ctx.drawImage(img, 0, 0);
                        this.log('✅ Canvas state restored successfully');
                        
                        this.performanceMetrics.restorationTime += performance.now() - startTime;
                        this.performanceMetrics.restorationCount++;
                        
                        setTimeout(() => {
                            this.clearingDetected = false;
                        }, 200);
                    };
                    
                    img.onerror = (error) => {
                        this.log('❌ Failed to load backup image for restoration');
                        this.clearingDetected = false;
                    };
                    
                    img.src = this.layerBackup.canvasData;
                    
                } catch (error) {
                    this.log('❌ Failed to restore canvas state: ' + error.message);
                    this.clearingDetected = false;
                }
            }

            getPerformanceMetrics() {
                return {
                    ...this.performanceMetrics,
                    averageMonitoringTime: this.performanceMetrics.monitoringCount > 0 
                        ? this.performanceMetrics.monitoringTime / this.performanceMetrics.monitoringCount 
                        : 0,
                    averageBackupTime: this.performanceMetrics.backupCount > 0 
                        ? this.performanceMetrics.backupTime / this.performanceMetrics.backupCount 
                        : 0,
                    averageRestorationTime: this.performanceMetrics.restorationCount > 0 
                        ? this.performanceMetrics.restorationTime / this.performanceMetrics.restorationCount 
                        : 0
                };
            }

            destroy() {
                if (this.monitoringTimer) clearInterval(this.monitoringTimer);
                if (this.backupTimer) clearInterval(this.backupTimer);
                this.isProtectionEnabled = false;
                this.log('🛡️ Canvas State Protector destroyed');
            }
        }

        // Test variables
        let testCanvas;
        let protector;

        function initializeTest() {
            const canvas = document.getElementById('testCanvas');
            
            // Mock PortraitCanvas
            testCanvas = {
                canvas: canvas,
                ctx: canvas.getContext('2d'),
                layers: { background: null, breed: null, costume: null, frame: null }
            };

            // Initialize protector
            protector = new CanvasStateProtector(testCanvas, {
                interval: 100,
                backupInterval: 500
            });

            updateStatus('✅ Canvas State Protector initialized successfully', 'success');
        }

        function drawContent() {
            if (!testCanvas) {
                updateStatus('❌ Please initialize the test first', 'error');
                return;
            }

            const ctx = testCanvas.ctx;
            
            // Clear and draw background
            ctx.fillStyle = '#e6f3ff';
            ctx.fillRect(0, 0, 400, 300);
            
            // Draw test content
            ctx.fillStyle = '#8B4513';
            ctx.beginPath();
            ctx.ellipse(200, 150, 80, 60, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add layers
            testCanvas.layers.breed = { id: 'golden-retriever' };
            testCanvas.layers.costume = { id: 'royal-crown' };
            
            // Draw frame
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 4;
            ctx.strokeRect(10, 10, 380, 280);
            
            updateStatus('✅ Test content drawn with layers added', 'success');
        }

        function clearCanvas() {
            if (!testCanvas) {
                updateStatus('❌ Please initialize the test first', 'error');
                return;
            }

            // Clear canvas
            testCanvas.ctx.clearRect(0, 0, 400, 300);
            testCanvas.ctx.fillStyle = '#ffffff';
            testCanvas.ctx.fillRect(0, 0, 400, 300);
            
            // Clear layers
            testCanvas.layers = { background: null, breed: null, costume: null, frame: null };
            
            updateStatus('🧹 Canvas cleared - protection should restore automatically', 'success');
        }

        function showStatus() {
            if (!protector) {
                updateStatus('❌ Protector not initialized', 'error');
                return;
            }

            const metrics = protector.getPerformanceMetrics();
            const status = `
                📊 Performance Metrics:<br>
                • Monitoring: ${metrics.monitoringCount} checks, avg ${metrics.averageMonitoringTime.toFixed(2)}ms<br>
                • Backups: ${metrics.backupCount} backups, avg ${metrics.averageBackupTime.toFixed(2)}ms<br>
                • Restorations: ${metrics.restorationCount} restorations, avg ${metrics.averageRestorationTime.toFixed(2)}ms
            `;
            updateStatus(status, 'success');
        }

        function updateStatus(message, type) {
            const statusElement = document.getElementById('statusDisplay');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Initialize on page load
        window.addEventListener('load', function() {
            updateStatus('Ready to test. Click "Initialize Protection" to begin.', 'success');
        });
    </script>
</body>
</html>
