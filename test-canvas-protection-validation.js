/**
 * Canvas State Protector Validation Test
 * Tests all acceptance criteria for Task 2.1: Implement Canvas State Protector Class
 */

console.log('🧪 Starting Canvas State Protector Validation Tests...');

// Test configuration
const TEST_CONFIG = {
    monitoringInterval: 100,
    backupInterval: 500,
    testTimeout: 2000
};

// Mock PortraitCanvas for testing
class MockPortraitCanvas {
    constructor(canvasElement) {
        this.canvas = canvasElement;
        this.ctx = canvasElement.getContext('2d');
        this.layers = {
            background: null,
            breed: null,
            costume: null,
            frame: null
        };
    }
}

// Test results tracking
const testResults = {
    passed: 0,
    failed: 0,
    tests: []
};

function logTest(testName, passed, message) {
    const result = { testName, passed, message, timestamp: new Date().toISOString() };
    testResults.tests.push(result);
    
    if (passed) {
        testResults.passed++;
        console.log(`✅ ${testName}: ${message}`);
    } else {
        testResults.failed++;
        console.error(`❌ ${testName}: ${message}`);
    }
}

// Acceptance Criteria Tests
async function runValidationTests() {
    console.log('🛡️ Running Canvas State Protector Validation Tests...');
    
    // Create test canvas
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 400;
    document.body.appendChild(canvas);
    
    const mockCanvas = new MockPortraitCanvas(canvas);
    
    // Test 1: CanvasStateProtector class implemented
    try {
        const protector = new window.CanvasStateProtector(mockCanvas, TEST_CONFIG);
        logTest('Class Implementation', true, 'CanvasStateProtector class successfully instantiated');
        
        // Test 2: Class integrated with main Portrait Builder app
        const hasRequiredMethods = [
            'checkCanvasState',
            'hasImportantLayers', 
            'isCanvasCleared',
            'backupCanvasState',
            'restoreCanvasState',
            'getPerformanceMetrics',
            'enable',
            'disable',
            'destroy'
        ].every(method => typeof protector[method] === 'function');
        
        logTest('Integration Methods', hasRequiredMethods, 
            hasRequiredMethods ? 'All required methods available for integration' : 'Missing required methods');
        
        // Test 3: Protection system monitors canvas state every 100ms
        const monitoringTest = await new Promise((resolve) => {
            let monitoringCount = 0;
            const originalCheck = protector.checkCanvasState;
            
            protector.checkCanvasState = function() {
                monitoringCount++;
                return originalCheck.call(this);
            };
            
            setTimeout(() => {
                const expectedCalls = Math.floor(1000 / TEST_CONFIG.monitoringInterval);
                const tolerance = 2; // Allow some variance
                const withinRange = Math.abs(monitoringCount - expectedCalls) <= tolerance;
                resolve(withinRange);
            }, 1000);
        });
        
        logTest('Monitoring Frequency', monitoringTest, 
            monitoringTest ? 'Canvas state monitored approximately every 100ms' : 'Monitoring frequency incorrect');
        
        // Test 4: Canvas state is backed up every 500ms
        const backupTest = await new Promise((resolve) => {
            let backupCount = 0;
            const originalBackup = protector.backupCanvasState;
            
            // Add some layers to trigger backups
            mockCanvas.layers.breed = { id: 'test-breed' };
            mockCanvas.layers.costume = { id: 'test-costume' };
            
            protector.backupCanvasState = function() {
                backupCount++;
                return originalBackup.call(this);
            };
            
            setTimeout(() => {
                const expectedBackups = Math.floor(1500 / TEST_CONFIG.backupInterval);
                const tolerance = 1;
                const withinRange = Math.abs(backupCount - expectedBackups) <= tolerance;
                resolve(withinRange);
            }, 1500);
        });
        
        logTest('Backup Frequency', backupTest, 
            backupTest ? 'Canvas state backed up approximately every 500ms' : 'Backup frequency incorrect');
        
        // Test 5: Automatic restoration works when clearing is detected
        const restorationTest = await new Promise((resolve) => {
            // Draw test content
            const ctx = mockCanvas.ctx;
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(50, 50, 100, 100);
            
            // Add layers
            mockCanvas.layers.breed = { id: 'golden-retriever' };
            mockCanvas.layers.frame = { id: 'ornate' };
            
            // Wait for backup
            setTimeout(() => {
                // Clear canvas and layers (simulate the bug)
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                mockCanvas.layers = { background: null, breed: null, costume: null, frame: null };
                
                // Wait for restoration
                setTimeout(() => {
                    // Check if restoration occurred
                    const imageData = ctx.getImageData(50, 50, 100, 100);
                    const hasRedPixels = Array.from(imageData.data).some((value, index) => 
                        index % 4 === 0 && value > 200); // Check for red pixels
                    
                    resolve(hasRedPixels);
                }, 300);
            }, 600);
        });
        
        logTest('Automatic Restoration', restorationTest, 
            restorationTest ? 'Canvas automatically restored after clearing detected' : 'Automatic restoration failed');
        
        // Test 6: Console logs show protection system activity
        const loggingTest = await new Promise((resolve) => {
            let logCount = 0;
            const originalLog = console.log;
            
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('🛡️') || message.includes('💾') || message.includes('🔄')) {
                    logCount++;
                }
                return originalLog.apply(console, args);
            };
            
            // Trigger some protection activity
            mockCanvas.layers.breed = { id: 'test-breed' };
            
            setTimeout(() => {
                console.log = originalLog;
                resolve(logCount > 0);
            }, 1000);
        });
        
        logTest('Console Logging', loggingTest, 
            loggingTest ? 'Protection system activity logged to console' : 'No protection system logs detected');
        
        // Test 7: Performance metrics collection
        const metrics = protector.getPerformanceMetrics();
        const hasMetrics = metrics && 
            typeof metrics.monitoringCount === 'number' &&
            typeof metrics.backupCount === 'number' &&
            typeof metrics.averageMonitoringTime === 'number';
        
        logTest('Performance Metrics', hasMetrics, 
            hasMetrics ? 'Performance metrics collected and accessible' : 'Performance metrics not available');
        
        // Test 8: Enable/Disable functionality
        protector.disable();
        const disabledState = !protector.isProtectionEnabled;
        protector.enable();
        const enabledState = protector.isProtectionEnabled;
        
        logTest('Enable/Disable Control', disabledState && enabledState, 
            (disabledState && enabledState) ? 'Protection can be enabled and disabled' : 'Enable/disable functionality failed');
        
        // Cleanup
        protector.destroy();
        document.body.removeChild(canvas);
        
    } catch (error) {
        logTest('Class Implementation', false, `Failed to instantiate CanvasStateProtector: ${error.message}`);
    }
    
    // Generate test report
    generateTestReport();
}

function generateTestReport() {
    console.log('\n🧪 Canvas State Protector Validation Test Report');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${testResults.tests.length}`);
    console.log(`Passed: ${testResults.passed}`);
    console.log(`Failed: ${testResults.failed}`);
    console.log(`Success Rate: ${((testResults.passed / testResults.tests.length) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));
    
    // Detailed results
    testResults.tests.forEach(test => {
        const status = test.passed ? '✅' : '❌';
        console.log(`${status} ${test.testName}: ${test.message}`);
    });
    
    // Acceptance criteria summary
    console.log('\n📋 Acceptance Criteria Summary:');
    const criteria = [
        'CanvasStateProtector class implemented',
        'Class integrated with main Portrait Builder app', 
        'Protection system monitors canvas state every 100ms',
        'Canvas state is backed up every 500ms',
        'Automatic restoration works when clearing is detected',
        'Console logs show protection system activity'
    ];
    
    criteria.forEach((criterion, index) => {
        const test = testResults.tests[index];
        const status = test && test.passed ? '✅' : '❌';
        console.log(`${status} ${criterion}`);
    });
    
    const allPassed = testResults.failed === 0;
    console.log(`\n🎯 Task 2.1 Status: ${allPassed ? '✅ COMPLETE' : '❌ NEEDS WORK'}`);
    
    return allPassed;
}

// Export for use in other contexts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runValidationTests, generateTestReport };
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        setTimeout(runValidationTests, 1000);
    });
}
