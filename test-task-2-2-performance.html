<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task 2.2 Performance Optimization Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            background: #28a745;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-section {
            background: white;
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metrics-display {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .performance-grade {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }
        .grade-A { background: #d4edda; color: #155724; }
        .grade-B { background: #d1ecf1; color: #0c5460; }
        .grade-C { background: #fff3cd; color: #856404; }
        .grade-D { background: #f8d7da; color: #721c24; }
        canvas {
            border: 2px solid #333;
            display: block;
            margin: 10px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007cba;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #005a87; }
        button.success { background: #28a745; }
        button.warning { background: #ffc107; color: #212529; }
        button.danger { background: #dc3545; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
        .adaptive-status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        .status-active { background: #28a745; color: white; }
        .status-idle { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚡ Task 2.2: Performance Optimization Test</h1>
        <p>Testing adaptive monitoring, memory tracking, and frame selection impact measurement</p>
    </div>

    <div class="test-section">
        <h2>📊 Real-time Performance Dashboard</h2>
        <canvas id="testCanvas" width="400" height="300"></canvas>
        
        <div>
            <button onclick="initializeProtector()" class="success">Initialize Protector</button>
            <button onclick="drawTestContent()">Draw Content</button>
            <button onclick="simulateUserActivity()" class="warning">Simulate User Activity</button>
            <button onclick="simulateFrameSelection()" class="warning">Test Frame Selection Impact</button>
            <button onclick="triggerCanvasClearing()" class="danger">Trigger Canvas Clearing</button>
            <button onclick="updateMetrics()">Update Metrics</button>
        </div>
        
        <div id="metricsDisplay" class="metrics-display">
            <strong>Performance Metrics:</strong><br>
            Click "Initialize Protector" to begin monitoring...
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Task 2.2 Acceptance Criteria Validation</h2>
        <div id="acceptanceCriteria">
            <h3>Acceptance Criteria Status:</h3>
            <ul id="criteriaList">
                <li>⏳ Performance monitoring added to protector class</li>
                <li>⏳ Adaptive monitoring frequency implemented</li>
                <li>⏳ Frame selection overhead measured and under 50ms</li>
                <li>⏳ Memory usage impact documented</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 Test Log</h2>
        <div class="log" id="testLog">
            <strong>Test Activity Log:</strong><br>
        </div>
    </div>

    <script src="assets/canvas-state-protector.js"></script>
    
    <script>
        let testCanvas;
        let protector;
        let metricsUpdateInterval;

        // Mock PortraitCanvas
        class MockPortraitCanvas {
            constructor(canvasElement) {
                this.canvas = canvasElement;
                this.ctx = canvasElement.getContext('2d');
                this.layers = { background: null, breed: null, costume: null, frame: null };
            }
        }

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function initializeProtector() {
            const canvas = document.getElementById('testCanvas');
            testCanvas = new MockPortraitCanvas(canvas);

            if (window.CanvasStateProtector) {
                protector = new window.CanvasStateProtector(testCanvas, {
                    interval: 100,
                    backupInterval: 2000  // Optimized for memory usage
                });
                
                log('✅ Canvas State Protector initialized with performance optimization');
                
                // Start real-time metrics updates
                metricsUpdateInterval = setInterval(updateMetrics, 1000);
                
                // Update acceptance criteria
                updateAcceptanceCriteria();
            } else {
                log('❌ CanvasStateProtector not available');
            }
        }

        function drawTestContent() {
            if (!testCanvas) {
                log('❌ Please initialize protector first');
                return;
            }

            const ctx = testCanvas.ctx;
            
            // Clear and draw background
            ctx.fillStyle = '#e6f3ff';
            ctx.fillRect(0, 0, 400, 300);
            
            // Draw test pet
            ctx.fillStyle = '#8B4513';
            ctx.beginPath();
            ctx.ellipse(200, 150, 80, 60, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add layers
            testCanvas.layers.breed = { id: 'golden-retriever' };
            testCanvas.layers.costume = { id: 'royal-crown' };
            
            // Draw frame
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 4;
            ctx.strokeRect(10, 10, 380, 280);
            
            log('🎨 Test content drawn with layers');
        }

        function simulateUserActivity() {
            if (!protector) {
                log('❌ Protector not initialized');
                return;
            }

            log('🎯 Simulating user activity for adaptive monitoring test...');
            
            // Simulate mouse movements and clicks
            const canvas = document.getElementById('testCanvas');
            const events = ['mousemove', 'click', 'keydown'];
            
            let eventCount = 0;
            const simulateEvent = () => {
                if (eventCount < 10) {
                    const eventType = events[eventCount % events.length];
                    const event = new Event(eventType);
                    canvas.parentElement.dispatchEvent(event);
                    eventCount++;
                    setTimeout(simulateEvent, 200);
                } else {
                    log('✅ User activity simulation complete');
                }
            };
            
            simulateEvent();
        }

        function simulateFrameSelection() {
            if (!protector) {
                log('❌ Protector not initialized');
                return;
            }

            log('🖼️ Testing frame selection impact measurement...');
            
            // Use the measureFrameSelectionImpact method
            const result = protector.measureFrameSelectionImpact(() => {
                // Simulate frame selection work
                const start = performance.now();
                while (performance.now() - start < 25) {
                    // Simulate 25ms of work
                    Math.random() * 1000;
                }
                return 'frame-selected';
            });
            
            log(`📊 Frame selection impact measured: ${protector.getPerformanceMetrics().frameSelectionImpact.toFixed(2)}ms`);
        }

        function triggerCanvasClearing() {
            if (!testCanvas) {
                log('❌ Please initialize protector first');
                return;
            }

            log('🚨 Triggering canvas clearing to test restoration...');
            
            // Clear canvas
            testCanvas.ctx.clearRect(0, 0, 400, 300);
            testCanvas.ctx.fillStyle = '#ffffff';
            testCanvas.ctx.fillRect(0, 0, 400, 300);
            
            // Clear layers
            testCanvas.layers = { background: null, breed: null, costume: null, frame: null };
            
            log('🧹 Canvas cleared - protection should restore automatically');
        }

        function updateMetrics() {
            if (!protector) return;

            const metrics = protector.getPerformanceMetrics();
            const metricsDisplay = document.getElementById('metricsDisplay');
            
            const adaptiveStatus = metrics.adaptiveStatus.isIdle ? 'idle' : 'active';
            const statusClass = metrics.adaptiveStatus.isIdle ? 'status-idle' : 'status-active';
            
            metricsDisplay.innerHTML = `
                <strong>📊 Performance Metrics (Task 2.2):</strong><br><br>
                
                <strong>Performance Grade:</strong> 
                <span class="performance-grade grade-${metrics.performanceGrade}">${metrics.performanceGrade}</span>
                ${metrics.isPerformanceOptimal ? '✅ Optimal' : '⚠️ Needs attention'}<br><br>
                
                <strong>Monitoring:</strong> ${metrics.monitoringCount} checks, avg ${metrics.averageMonitoringTime.toFixed(2)}ms<br>
                <strong>Backups:</strong> ${metrics.backupCount} backups, avg ${metrics.averageBackupTime.toFixed(2)}ms<br>
                <strong>Restorations:</strong> ${metrics.restorationCount} restorations<br><br>
                
                <strong>Adaptive Monitoring:</strong><br>
                • Status: <span class="adaptive-status ${statusClass}">${adaptiveStatus.toUpperCase()}</span><br>
                • Current Interval: ${metrics.currentMonitoringInterval}ms<br>
                • Base Interval: ${metrics.adaptiveStatus.baseInterval}ms<br>
                • Adjustments Made: ${metrics.adaptiveStatus.adjustmentCount}<br><br>
                
                <strong>Performance Impact:</strong><br>
                • Frame Selection: ${metrics.frameSelectionImpact.toFixed(2)}ms (limit: 50ms)<br>
                • Memory Usage: ${metrics.memoryUsageMB.toFixed(2)}MB<br>
                • Peak Memory: ${metrics.peakMemoryMB.toFixed(2)}MB<br>
            `;
        }

        function updateAcceptanceCriteria() {
            if (!protector) return;

            const metrics = protector.getPerformanceMetrics();
            const criteriaList = document.getElementById('criteriaList');
            
            const criteria = [
                {
                    text: 'Performance monitoring added to protector class',
                    passed: metrics.memoryUsageMB !== undefined && metrics.performanceGrade !== undefined
                },
                {
                    text: 'Adaptive monitoring frequency implemented',
                    passed: metrics.adaptiveStatus.adjustmentCount !== undefined && metrics.currentMonitoringInterval !== undefined
                },
                {
                    text: 'Frame selection overhead measured and under 50ms',
                    passed: metrics.frameSelectionImpact < 50
                },
                {
                    text: 'Memory usage impact documented',
                    passed: metrics.memoryUsageMB !== undefined && metrics.peakMemoryMB !== undefined
                }
            ];
            
            criteriaList.innerHTML = criteria.map(criterion => 
                `<li>${criterion.passed ? '✅' : '⏳'} ${criterion.text}</li>`
            ).join('');
        }

        // Auto-update metrics every second
        window.addEventListener('load', () => {
            log('🚀 Task 2.2 Performance Optimization Test ready');
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (metricsUpdateInterval) clearInterval(metricsUpdateInterval);
            if (protector) protector.destroy();
        });
    </script>
</body>
</html>
