# Frame Selection Canvas Clearing - Prioritized Fix Implementation Plan

**Date:** 2025-07-20  
**Based on:** frame-selection-canvas-clearing-analysis.md  
**Status:** 🚨 CRITICAL - Immediate implementation required

---

## CRITICAL FIX #1: Prevent Canvas Clearing During Frame Operations

### **Problem:** 
`hideCanvas()` and `hideProgressiveCanvas()` methods automatically call `clearCanvas()`, destroying all layers during step navigation triggered by frame selection.

### **Root Cause:**
```javascript
// Line 2186: hideCanvas() always clears canvas
hideCanvas() {
  // ...
  this.clearCanvas(); // ← DESTROYS ALL LAYERS
}

// Line 2225: hideProgressiveCanvas() also clears canvas  
hideProgressiveCanvas() {
  // ...
  this.clearCanvas(); // ← DESTROYS ALL LAYERS
}
```

### **Solution: Add Layer Preservation Logic**

**File:** `assets/royal-portrait-complete.js`  
**Lines:** 2181-2189 and 2220-2232

```javascript
// FIXED: hideCanvas() with layer preservation
hideCanvas() {
  if (this.canvas) {
    this.canvas.style.display = 'none';
    this.isVisible = false;
    this.renderingEnabled = false;
    
    // CRITICAL FIX: Only clear canvas if no important layers exist
    const hasImportantLayers = this.layers.breed || this.layers.costume || 
                              this.layers.frame || this.layers.background;
    
    if (!hasImportantLayers) {
      this.clearCanvas();
      console.log('🎨 Canvas hidden and cleared (no important layers)');
    } else {
      console.log('🎨 Canvas hidden but layers preserved');
    }
  }
}

// FIXED: hideProgressiveCanvas() with layer preservation
hideProgressiveCanvas() {
  if (this.canvas) {
    this.canvas.style.display = 'none';
    this.isVisible = false;
    this.renderingEnabled = false;
    
    // CRITICAL FIX: Only clear canvas if no important layers exist
    const hasImportantLayers = this.layers.breed || this.layers.costume || 
                              this.layers.frame || this.layers.background;
    
    if (!hasImportantLayers) {
      this.clearCanvas();
      this.toggleProgressiveCanvasPlaceholder(true);
      console.log('🎨 Progressive canvas hidden and cleared (no important layers)');
    } else {
      // Keep placeholder hidden when layers exist
      this.toggleProgressiveCanvasPlaceholder(false);
      console.log('🎨 Progressive canvas hidden but layers preserved');
    }
  }
}
```

---

## CRITICAL FIX #2: Decouple Navigation Updates from Canvas Clearing

### **Problem:**
`updateNavigationButtons()` automatically triggers `updateCanvasVisibility()` which clears canvases during frame selection.

### **Root Cause:**
```javascript
// Line 4255: Navigation update triggers canvas clearing
this.updateCanvasVisibility(); // ← Called during navigation updates
```

### **Solution: Conditional Canvas Visibility Updates**

**File:** `assets/royal-portrait-complete.js`  
**Method:** `renderStep()` around line 4253

```javascript
// FIXED: Conditional canvas visibility updates
renderStep() {
  // ... existing step rendering logic ...

  // CRITICAL FIX: Only update canvas visibility when actually changing steps
  // Don't update during navigation button updates within the same step
  if (this.shouldUpdateCanvasVisibility()) {
    this.updateCanvasVisibility();
  }

  this.updateNavigationButtons();
  console.log(`📍 Rendered step ${this.currentStep}`);
}

// NEW: Method to determine if canvas visibility should be updated
shouldUpdateCanvasVisibility() {
  // Only update canvas visibility when:
  // 1. Actually changing steps (not just updating buttons)
  // 2. Not during frame selection operations
  // 3. Not during other canvas-sensitive operations
  
  const isFrameSelectionInProgress = this.portraitApp && 
    this.portraitApp.frameSelector && 
    this.portraitApp.frameSelector.isSelectionInProgress;
    
  return !isFrameSelectionInProgress;
}
```

---

## CRITICAL FIX #3: Add Frame Selection Operation Locking

### **Problem:**
Race conditions between frame selection and step navigation cause canvas clearing during frame operations.

### **Solution: Operation Locking Mechanism**

**File:** `assets/royal-portrait-complete.js`  
**Method:** `selectFrame()` around line 16578

```javascript
// FIXED: selectFrame() with operation locking
async selectFrame(frameId) {
  try {
    // CRITICAL FIX: Lock frame selection operations
    if (this.isSelectionInProgress) {
      console.warn('Frame selection already in progress, ignoring duplicate request');
      return;
    }
    
    this.isSelectionInProgress = true;
    
    const frame = this.frameLibrary.getFrameById(frameId);
    if (!frame) {
      console.error('Frame not found:', frameId);
      return;
    }

    this.state.selectedFrame = frame;

    // CRITICAL FIX: Use coordinated canvas update system for batched rendering
    await this.updateCanvasLayers({
      frame: frame,
      updateMain: true,
      updateProgressive: true,
    });

    // Update UI with enhanced visual feedback
    this.updateFrameSelectionUI(frame);
    this.showFrameSelectionSuccess(frame);

    // Update dynamic pricing
    if (this.dynamicPricing) {
      this.dynamicPricing.updatePricing();
    }

    this.updateAddToCartButton();

    // CRITICAL FIX: Update step navigator WITHOUT triggering canvas operations
    if (this.stepNavigator) {
      this.stepNavigator.updateNavigationButtonsOnly(); // New method without canvas updates
    }

    // Save current state
    this.saveCurrentState();

    // Analytics
    if (window.PortraitAnalyticsInstance) {
      window.PortraitAnalyticsInstance.trackFrameSelected(frame);
      window.PortraitAnalyticsInstance.trackShopifyFunnelProgression(4, 'frame_selection', {
        frameId: frame.id,
        frameName: frame.name,
        framePrice: frame.base_price,
        frameStyle: frame.style || 'classic',
      });
    }

    console.log('Frame selected:', frame);
  } catch (error) {
    console.error('Error selecting frame:', error);
  } finally {
    // CRITICAL FIX: Always unlock frame selection
    this.isSelectionInProgress = false;
  }
}
```

---

## CRITICAL FIX #4: Add Navigation Button Update Without Canvas Operations

### **Problem:**
No way to update navigation buttons without triggering canvas visibility changes.

### **Solution: Separate Navigation Button Updates**

**File:** `assets/royal-portrait-complete.js`  
**Method:** New method in `StepNavigator` class

```javascript
// NEW: Update navigation buttons without canvas operations
updateNavigationButtonsOnly() {
  const currentStepContainer = document.querySelector(`[data-step="${this.currentStep}"]`);
  if (!currentStepContainer) return;

  const backButton = currentStepContainer.querySelector('[data-step-back]');
  const nextButton = currentStepContainer.querySelector('[data-step-next]');

  // Update back button
  if (backButton) {
    backButton.disabled = this.currentStep === 1;
  }

  // Update next button
  if (nextButton) {
    const canProceed = this.canProceedToNext();
    nextButton.disabled = !canProceed;
    nextButton.classList.toggle('button--disabled', !canProceed);
    this.updateNextButtonText(nextButton);
  }

  console.log(`🔄 Navigation buttons updated for step ${this.currentStep} (no canvas operations)`);
}

// MODIFIED: Original method now explicitly includes canvas operations
updateNavigationButtons() {
  // Update button states
  this.updateNavigationButtonsOnly();
  
  // Update canvas visibility (only when appropriate)
  if (this.shouldUpdateCanvasVisibility()) {
    this.updateCanvasVisibility();
  }
}
```

---

## CRITICAL FIX #5: Enhanced Canvas Layer State Validation

### **Problem:**
No validation to ensure frame layers persist through canvas operations.

### **Solution: Layer State Validation and Recovery**

**File:** `assets/royal-portrait-complete.js`  
**Method:** New validation methods

```javascript
// NEW: Validate canvas layer state
validateCanvasLayers() {
  const expectedLayers = {
    breed: this.portraitApp.state.selectedBreed,
    costume: this.portraitApp.state.selectedCostume,
    frame: this.portraitApp.state.selectedFrame,
    background: this.portraitApp.state.selectedBackground
  };

  const mainCanvasLayers = this.portraitApp.canvas ? this.portraitApp.canvas.layers : {};
  const progressiveCanvasLayers = this.portraitApp.progressiveCanvas ? 
    this.portraitApp.progressiveCanvas.layers : {};

  console.log('🔍 LAYER VALIDATION:', {
    expected: expectedLayers,
    mainCanvas: mainCanvasLayers,
    progressiveCanvas: progressiveCanvasLayers
  });

  return {
    mainCanvasValid: this.validateLayersMatch(expectedLayers, mainCanvasLayers),
    progressiveCanvasValid: this.validateLayersMatch(expectedLayers, progressiveCanvasLayers)
  };
}

// NEW: Restore missing canvas layers
async restoreCanvasLayers() {
  console.log('🔧 LAYER RESTORATION: Restoring missing canvas layers...');
  
  const state = this.portraitApp.state;
  
  // Restore main canvas layers
  if (this.portraitApp.canvas) {
    if (state.selectedBreed && !this.portraitApp.canvas.layers.breed) {
      await this.portraitApp.canvas.setBreedLayer(state.selectedBreed);
    }
    if (state.selectedCostume && !this.portraitApp.canvas.layers.costume) {
      await this.portraitApp.canvas.setCostumeLayer(state.selectedCostume);
    }
    if (state.selectedFrame && !this.portraitApp.canvas.layers.frame) {
      await this.portraitApp.canvas.setFrameLayer(state.selectedFrame);
    }
    if (state.selectedBackground && !this.portraitApp.canvas.layers.background) {
      await this.portraitApp.canvas.setBackgroundLayer(
        state.selectedBackground.type, 
        state.selectedBackground.value
      );
    }
  }

  // Restore progressive canvas layers
  if (this.portraitApp.progressiveCanvas) {
    if (state.selectedBreed && !this.portraitApp.progressiveCanvas.layers.breed) {
      await this.portraitApp.progressiveCanvas.setBreedLayer(state.selectedBreed);
    }
    if (state.selectedCostume && !this.portraitApp.progressiveCanvas.layers.costume) {
      await this.portraitApp.progressiveCanvas.setCostumeLayer(state.selectedCostume);
    }
    if (state.selectedFrame && !this.portraitApp.progressiveCanvas.layers.frame) {
      await this.portraitApp.progressiveCanvas.setFrameLayer(state.selectedFrame);
    }
    if (state.selectedBackground && !this.portraitApp.progressiveCanvas.layers.background) {
      await this.portraitApp.progressiveCanvas.setBackgroundLayer(
        state.selectedBackground.type, 
        state.selectedBackground.value
      );
    }
  }

  console.log('✅ LAYER RESTORATION: Canvas layers restored successfully');
}
```

---

## Implementation Order

### **Phase 1 (Immediate - Critical):**
1. **Fix #1:** Implement layer preservation in `hideCanvas()` methods
2. **Fix #3:** Add frame selection operation locking
3. **Fix #4:** Create `updateNavigationButtonsOnly()` method

### **Phase 2 (High Priority):**
4. **Fix #2:** Implement conditional canvas visibility updates
5. **Fix #5:** Add layer state validation and recovery

### **Phase 3 (Validation):**
6. Test all fixes with comprehensive frame selection scenarios
7. Validate layer persistence across all operations
8. Confirm no regression in other functionality

---

## Testing Checklist

- [ ] Frame selection preserves all existing layers
- [ ] Step navigation doesn't clear canvas during frame operations
- [ ] Progressive canvas and main canvas remain synchronized
- [ ] No race conditions during rapid frame selection
- [ ] Layer state validation catches and recovers from any layer loss
- [ ] All existing functionality remains intact

---

This implementation plan addresses all critical failure points identified in the analysis and provides a systematic approach to resolving the canvas clearing regression.
