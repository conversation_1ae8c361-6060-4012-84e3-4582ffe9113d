Canvas Clearing Execution Traces - Royal Portrait Builder
=======================================================

Date: 2025-07-20
Purpose: Systematic testing of frame selection to identify canvas clearing patterns
Instrumentation: canvas-debugging-instrumentation.js v1.0

Test Environment:
- Browser: Chrome/Firefox/Safari (to be specified per test)
- Local development server: http://localhost:8080
- Debug mode: DEBUG_CANVAS_CLEARING = true

Test Scenarios:
1. Frame selection with different breeds selected
2. Frame selection with different costumes selected
3. Rapid frame selection (multiple frames quickly)
4. Frame selection after page reload
5. Frame selection edge cases

=======================================================

TEST SCENARIO 1: Frame Selection with Different Breeds
------------------------------------------------------

Test 1.1: Golden Retriever + Classic Frame
------------------------------------------
[Timestamp: To be filled during testing]
[Browser: To be specified]

Steps:
1. Load Royal Portrait Builder
2. Select Golden Retriever breed
3. Select Classic frame
4. Observe console logs

Expected Logs:
- 🖼️ FRAME SELECTION STARTED
- 🎨 Canvas state BEFORE frame selection
- 🎨 CANVAS LAYERS UPDATE STARTED
- 🖼️ FRAME LAYER UPDATE - setFrameLayer()
- 🎨 Canvas state AFTER frame selection

Actual Logs:
[To be filled during testing]

Analysis:
[To be filled during testing]

Test 1.2: Labrador + Ornate Frame
----------------------------------
[Similar format for each test]

=======================================================

TEST SCENARIO 2: Frame Selection with Different Costumes
--------------------------------------------------------

Test 2.1: Golden Retriever + Royal Crown + Classic Frame
--------------------------------------------------------
[To be filled during testing]

Test 2.2: Labrador + Knight Armor + Modern Frame
------------------------------------------------
[To be filled during testing]

=======================================================

TEST SCENARIO 3: Rapid Frame Selection
--------------------------------------

Test 3.1: Quick succession - Classic → Ornate → Modern
------------------------------------------------------
[To be filled during testing]

Test 3.2: Rapid clicking same frame multiple times
--------------------------------------------------
[To be filled during testing]

=======================================================

TEST SCENARIO 4: Frame Selection After Page Reload
--------------------------------------------------

Test 4.1: Fresh page load + immediate frame selection
----------------------------------------------------
[To be filled during testing]

Test 4.2: Page reload with localStorage data + frame selection
-------------------------------------------------------------
[To be filled during testing]

=======================================================

INSTRUMENTATION SETUP COMPLETED
===============================

✅ Canvas Clearing Instrumentation Active:
- clearRect() method instrumented
- fillRect() method instrumented (white background detection)
- PortraitCanvas.clearCanvas() method instrumented
- PortraitCanvas.setFrameLayer() method instrumented
- PortraitCanvas.showCanvas() method instrumented
- PortraitCanvas.hideCanvas() method instrumented

✅ Frame Selection Workflow Instrumentation Active:
- RoyalPortraitBuilderApp.selectFrame() method instrumented
- RoyalPortraitBuilderApp.updateCanvasLayers() method instrumented
- Timing and canvas state logging implemented

✅ Navigation and Event Instrumentation Active:
- updateNavigationButtons() method instrumented
- updateNavigationButtonsOnly() method instrumented
- updateCanvasVisibility() method instrumented
- DOM event handlers instrumented for Portrait Builder elements

TESTING METHODOLOGY ESTABLISHED
===============================

Testing Script Created: docs/systematic-frame-testing-script.js
- Automated testing scenarios for different breed/costume/frame combinations
- Rapid frame selection testing
- Console output capture and analysis
- Performance timing measurement

PRELIMINARY CODE ANALYSIS
=========================

Based on code examination, potential canvas clearing triggers identified:

1. Canvas Visibility Updates (HIGH PRIORITY):
   - updateCanvasVisibility() method calls showCanvas()/hideCanvas()
   - hideCanvas() method may clear layers (line 2186-2197 in royal-portrait-complete.js)
   - Called during navigation updates and step transitions

2. Frame Selection Race Conditions (MEDIUM PRIORITY):
   - selectFrame() method has race condition protection (line 16637-16640)
   - Multiple async operations in updateCanvasLayers() (line 15585-15629)
   - Potential timing issues between main and progressive canvas updates

3. Canvas Rendering Operations (MEDIUM PRIORITY):
   - renderOffscreen() method clears canvas (line 3364, 3370)
   - drawLayers() method may trigger clearing during redraw
   - Multiple canvas contexts being managed simultaneously

PATTERN ANALYSIS (PRELIMINARY)
=============================

Common Call Stack Patterns (Based on Code Structure):
1. selectFrame() → updateCanvasLayers() → setFrameLayer() → [potential clearing]
2. updateNavigationButtons() → updateCanvasVisibility() → hideCanvas() → [clearing]
3. Frame hover/preview operations → progressive canvas updates → [clearing]

Timing Patterns (Predicted):
- Canvas clearing likely occurs during step transitions
- Race conditions possible during rapid frame selection
- Async operations may cause state inconsistencies

ROOT CAUSE HYPOTHESIS (PRELIMINARY)
==================================

Primary Hypothesis: Canvas Visibility Management
The hideCanvas() method (line 2186) includes logic to clear canvas when no important layers exist:

```javascript
// CRITICAL FIX: Only clear canvas if no important layers exist
const hasImportantLayers = this.layers.breed || this.layers.costume || this.layers.frame || this.layers.background;

if (!hasImportantLayers) {
  this.clearCanvas();
  console.log('🎨 Canvas hidden and cleared (no important layers)');
} else {
  console.log('🎨 Canvas hidden but layers preserved');
}
```

This suggests the clearing is intentional but may be triggered incorrectly during frame selection.

Secondary Hypothesis: Navigation State Management
The updateCanvasVisibility() method is called during frame selection navigation updates, which may incorrectly determine that layers should be cleared.

NEXT STEPS FOR LIVE TESTING
===========================

1. Load Royal Portrait Builder in browser with instrumentation active
2. Run systematic testing script: await runSystematicFrameTests()
3. Capture console output for each test scenario
4. Analyze actual call stacks and timing patterns
5. Identify the specific method causing canvas clearing
6. Implement targeted fix based on empirical evidence

TESTING CHECKLIST
=================

□ Test with Golden Retriever + Classic Frame
□ Test with Labrador + Ornate Frame
□ Test with German Shepherd + Modern Frame
□ Test with costume combinations
□ Test rapid frame selection
□ Test after page reload
□ Capture and analyze all console logs
□ Identify most common clearing trigger
□ Document call stack patterns
□ Form definitive root cause hypothesis
