/**
 * Canvas Clearing Issue - Manual Testing Script
 * 
 * TASK 3.3: Manual testing script to reproduce original canvas clearing issue and validate fix
 * 
 * This script can be run in the browser console to test the canvas clearing fix.
 * It simulates the exact conditions that caused the original issue and validates
 * that the dimension-aware layer preservation is working correctly.
 */

(function() {
  'use strict';

  console.log('🧪 CANVAS CLEARING MANUAL TEST SCRIPT');
  console.log('=====================================');
  console.log('This script tests the dimension-aware layer preservation fix.');
  console.log('');

  // Test configuration
  const TEST_CONFIG = {
    ORIGINAL_DIMENSIONS: { width: 500, height: 500 },
    CHANGED_DIMENSIONS: { width: 300, height: 300 },
    TOLERANCE: 2,
    PERFORMANCE_THRESHOLD: 5, // ms
  };

  // Test results tracking
  const testResults = {
    passed: 0,
    failed: 0,
    tests: []
  };

  /**
   * Utility function to log test results
   */
  function logTest(testName, passed, details = '') {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status}: ${testName}`);
    if (details) {
      console.log(`   ${details}`);
    }
    
    testResults.tests.push({ name: testName, passed, details });
    if (passed) {
      testResults.passed++;
    } else {
      testResults.failed++;
    }
  }

  /**
   * Test 1: Verify Portrait Builder is available
   */
  function testPortraitBuilderAvailability() {
    console.log('\n📋 Test 1: Portrait Builder Availability');
    console.log('----------------------------------------');
    
    const portraitApp = window.portraitBuilderApp || window.RoyalPortraitBuilderApp;
    const hasPortraitApp = !!portraitApp;
    
    logTest('Portrait Builder App Available', hasPortraitApp, 
      hasPortraitApp ? 'Found portrait builder instance' : 'Portrait builder not found');
    
    if (hasPortraitApp && portraitApp.canvas) {
      logTest('Main Canvas Available', true, 'Main canvas instance found');
    } else {
      logTest('Main Canvas Available', false, 'Main canvas not found');
    }
    
    if (hasPortraitApp && portraitApp.progressiveCanvas) {
      logTest('Progressive Canvas Available', true, 'Progressive canvas instance found');
    } else {
      logTest('Progressive Canvas Available', false, 'Progressive canvas not found');
    }
    
    return portraitApp;
  }

  /**
   * Test 2: Verify dimension tracking is implemented
   */
  function testDimensionTrackingImplementation(portraitApp) {
    console.log('\n📋 Test 2: Dimension Tracking Implementation');
    console.log('--------------------------------------------');
    
    if (!portraitApp || !portraitApp.canvas) {
      logTest('Dimension Tracking Implementation', false, 'No canvas available for testing');
      return false;
    }
    
    const canvas = portraitApp.canvas;
    
    // Check for dimension tracking properties
    const hasTrackingProperties = canvas.hasOwnProperty('lastKnownDimensions') && 
                                 canvas.hasOwnProperty('dimensionTrackingEnabled');
    logTest('Dimension Tracking Properties', hasTrackingProperties,
      hasTrackingProperties ? 'Properties found' : 'Properties missing');
    
    // Check for dimension tracking methods
    const hasDimensionsChangedMethod = typeof canvas.dimensionsChanged === 'function';
    logTest('dimensionsChanged() Method', hasDimensionsChangedMethod,
      hasDimensionsChangedMethod ? 'Method available' : 'Method missing');
    
    const hasUpdateTrackingMethod = typeof canvas.updateDimensionTracking === 'function';
    logTest('updateDimensionTracking() Method', hasUpdateTrackingMethod,
      hasUpdateTrackingMethod ? 'Method available' : 'Method missing');
    
    return hasTrackingProperties && hasDimensionsChangedMethod && hasUpdateTrackingMethod;
  }

  /**
   * Test 3: Test dimension change detection accuracy
   */
  function testDimensionChangeDetection(portraitApp) {
    console.log('\n📋 Test 3: Dimension Change Detection');
    console.log('------------------------------------');
    
    if (!portraitApp || !portraitApp.canvas) {
      logTest('Dimension Change Detection', false, 'No canvas available for testing');
      return false;
    }
    
    const canvas = portraitApp.canvas;
    const originalWidth = canvas.canvas.width;
    const originalHeight = canvas.canvas.height;
    
    try {
      // Test 3a: No change detection
      const noChange = canvas.dimensionsChanged();
      logTest('No Change Detection', !noChange, 
        `Should return false for unchanged dimensions: ${noChange}`);
      
      // Test 3b: Significant change detection
      canvas.canvas.width = originalWidth + 100;
      const significantChange = canvas.dimensionsChanged();
      logTest('Significant Change Detection', significantChange,
        `Should return true for 100px change: ${significantChange}`);
      
      // Test 3c: Tolerance handling
      canvas.canvas.width = originalWidth + 1; // Within tolerance
      const toleranceChange = canvas.dimensionsChanged();
      logTest('Tolerance Handling', !toleranceChange,
        `Should return false for 1px change (within tolerance): ${toleranceChange}`);
      
      // Restore original dimensions
      canvas.canvas.width = originalWidth;
      canvas.canvas.height = originalHeight;
      canvas.updateDimensionTracking();
      
      return true;
    } catch (error) {
      logTest('Dimension Change Detection', false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test 4: Test layer preservation during dimension changes
   */
  function testLayerPreservation(portraitApp) {
    console.log('\n📋 Test 4: Layer Preservation During Dimension Changes');
    console.log('-----------------------------------------------------');
    
    if (!portraitApp || !portraitApp.canvas) {
      logTest('Layer Preservation Test', false, 'No canvas available for testing');
      return false;
    }
    
    const canvas = portraitApp.canvas;
    const originalWidth = canvas.canvas.width;
    const originalHeight = canvas.canvas.height;
    
    try {
      // Add test layers
      canvas.layers.breed = { id: 'test-breed', name: 'Test Breed' };
      canvas.layers.costume = { id: 'test-costume', name: 'Test Costume' };
      
      // Mock clearCanvas to track calls
      let clearCanvasCalled = false;
      const originalClearCanvas = canvas.clearCanvas;
      canvas.clearCanvas = function() {
        clearCanvasCalled = true;
        return originalClearCanvas.call(this);
      };
      
      // Test 4a: Layer preservation with dimension change
      canvas.canvas.width = originalWidth + 50;
      canvas.hideCanvas();
      
      logTest('Layer Preservation with Dimension Change', !clearCanvasCalled,
        clearCanvasCalled ? 'Canvas was cleared (should be preserved)' : 'Canvas preserved correctly');
      
      // Test 4b: Layer preservation with stable dimensions but existing layers
      clearCanvasCalled = false;
      canvas.canvas.width = originalWidth; // Restore original
      canvas.updateDimensionTracking(); // Update tracking
      canvas.hideCanvas();
      
      logTest('Layer Preservation with Existing Layers', !clearCanvasCalled,
        clearCanvasCalled ? 'Canvas was cleared (should be preserved)' : 'Canvas preserved correctly');
      
      // Test 4c: Canvas clearing with no layers and stable dimensions
      clearCanvasCalled = false;
      canvas.layers = { breed: null, costume: null, frame: null, background: null };
      canvas.hideCanvas();
      
      logTest('Canvas Clearing with No Layers', clearCanvasCalled,
        clearCanvasCalled ? 'Canvas cleared correctly' : 'Canvas not cleared (should be cleared)');
      
      // Restore original clearCanvas method
      canvas.clearCanvas = originalClearCanvas;
      
      // Restore original dimensions
      canvas.canvas.width = originalWidth;
      canvas.canvas.height = originalHeight;
      
      return true;
    } catch (error) {
      logTest('Layer Preservation Test', false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test 5: Test frame selection workflow
   */
  function testFrameSelectionWorkflow(portraitApp) {
    console.log('\n📋 Test 5: Frame Selection Workflow');
    console.log('-----------------------------------');
    
    if (!portraitApp || !portraitApp.frameSelector) {
      logTest('Frame Selection Workflow', false, 'Frame selector not available');
      return false;
    }
    
    try {
      // Add test layers
      if (portraitApp.canvas) {
        portraitApp.canvas.layers.breed = { id: 'test-breed', name: 'Test Breed' };
        portraitApp.canvas.layers.costume = { id: 'test-costume', name: 'Test Costume' };
      }
      
      if (portraitApp.progressiveCanvas) {
        portraitApp.progressiveCanvas.layers.breed = { id: 'test-breed', name: 'Test Breed' };
        portraitApp.progressiveCanvas.layers.costume = { id: 'test-costume', name: 'Test Costume' };
      }
      
      // Mock frame selection
      const originalSelectFrame = portraitApp.frameSelector.selectFrame;
      let frameSelectionCompleted = false;
      
      portraitApp.frameSelector.selectFrame = async function(frameId) {
        try {
          // Simulate dimension change during frame selection
          if (portraitApp.canvas) {
            portraitApp.canvas.canvas.width = 400;
            portraitApp.canvas.canvas.height = 400;
          }
          
          const result = await originalSelectFrame.call(this, frameId);
          frameSelectionCompleted = true;
          return result;
        } catch (error) {
          console.error('Frame selection error:', error);
          throw error;
        }
      };
      
      // Test frame selection (this is async, so we'll just verify the setup)
      logTest('Frame Selection Setup', true, 'Frame selection workflow ready for testing');
      
      // Restore original method
      portraitApp.frameSelector.selectFrame = originalSelectFrame;
      
      return true;
    } catch (error) {
      logTest('Frame Selection Workflow', false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test 6: Performance validation
   */
  function testPerformance(portraitApp) {
    console.log('\n📋 Test 6: Performance Validation');
    console.log('---------------------------------');
    
    if (!portraitApp || !portraitApp.canvas) {
      logTest('Performance Validation', false, 'No canvas available for testing');
      return false;
    }
    
    const canvas = portraitApp.canvas;
    
    try {
      // Test dimensionsChanged() performance
      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        canvas.dimensionsChanged();
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      logTest('dimensionsChanged() Performance', averageTime < 1,
        `Average execution time: ${averageTime.toFixed(3)}ms (should be <1ms)`);
      
      // Test updateDimensionTracking() performance
      const trackingStartTime = performance.now();
      canvas.updateDimensionTracking();
      const trackingEndTime = performance.now();
      const trackingTime = trackingEndTime - trackingStartTime;
      
      logTest('updateDimensionTracking() Performance', trackingTime < TEST_CONFIG.PERFORMANCE_THRESHOLD,
        `Execution time: ${trackingTime.toFixed(3)}ms (should be <${TEST_CONFIG.PERFORMANCE_THRESHOLD}ms)`);
      
      return true;
    } catch (error) {
      logTest('Performance Validation', false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Test 7: Console log validation
   */
  function testConsoleLogging(portraitApp) {
    console.log('\n📋 Test 7: Console Logging Validation');
    console.log('------------------------------------');
    
    if (!portraitApp || !portraitApp.canvas) {
      logTest('Console Logging Validation', false, 'No canvas available for testing');
      return false;
    }
    
    const canvas = portraitApp.canvas;
    
    try {
      // Capture console logs
      const originalConsoleLog = console.log;
      const logMessages = [];
      
      console.log = function(...args) {
        logMessages.push(args.join(' '));
        originalConsoleLog.apply(console, args);
      };
      
      // Trigger operations that should log
      canvas.canvas.width = 600; // Change dimensions
      canvas.hideCanvas(); // Should log preservation due to dimension change
      
      // Restore console.log
      console.log = originalConsoleLog;
      
      // Check for expected log messages
      const hasDimensionLog = logMessages.some(msg => msg.includes('Canvas preserved (dimensions changed)'));
      logTest('Dimension Change Logging', hasDimensionLog,
        hasDimensionLog ? 'Found dimension change log' : 'Dimension change log missing');
      
      const hasTrackingLog = logMessages.some(msg => msg.includes('DIMENSION TRACKING: Updated'));
      logTest('Dimension Tracking Logging', hasTrackingLog,
        hasTrackingLog ? 'Found tracking update log' : 'Tracking update log missing');
      
      return true;
    } catch (error) {
      logTest('Console Logging Validation', false, `Error: ${error.message}`);
      return false;
    }
  }

  /**
   * Main test execution
   */
  function runTests() {
    console.log('Starting Canvas Clearing Fix Validation Tests...\n');
    
    const portraitApp = testPortraitBuilderAvailability();
    
    if (portraitApp) {
      testDimensionTrackingImplementation(portraitApp);
      testDimensionChangeDetection(portraitApp);
      testLayerPreservation(portraitApp);
      testFrameSelectionWorkflow(portraitApp);
      testPerformance(portraitApp);
      testConsoleLogging(portraitApp);
    }
    
    // Print final results
    console.log('\n🏁 TEST RESULTS SUMMARY');
    console.log('=======================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📊 Total: ${testResults.tests.length}`);
    
    const successRate = (testResults.passed / testResults.tests.length * 100).toFixed(1);
    console.log(`📈 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! Canvas clearing fix is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the implementation.');
      console.log('\nFailed tests:');
      testResults.tests.filter(test => !test.passed).forEach(test => {
        console.log(`  - ${test.name}: ${test.details}`);
      });
    }
    
    return {
      passed: testResults.passed,
      failed: testResults.failed,
      total: testResults.tests.length,
      successRate: successRate,
      details: testResults.tests
    };
  }

  // Auto-run tests if script is executed directly
  if (typeof window !== 'undefined') {
    // Browser environment - run tests
    return runTests();
  } else {
    // Export for Node.js testing
    module.exports = { runTests, TEST_CONFIG };
  }

})();
