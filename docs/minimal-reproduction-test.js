/**
 * Minimal Reproduction Test Case for Canvas Clearing Issue
 * 
 * This test validates that the root cause fix is working correctly.
 * The fix prevents canvas clearing during frame selection by preserving
 * layers in the hideCanvas() method when important layers exist.
 */

// Test configuration
const TEST_CONFIG = {
  testBreed: 'golden-retriever',
  testCostume: 'royal-crown',
  testFrame: 'classic-frame',
  expectedLayers: ['breed', 'costume', 'frame']
};

/**
 * Minimal reproduction test that validates the canvas clearing fix
 */
async function runMinimalReproductionTest() {
  console.group('🧪 MINIMAL REPRODUCTION TEST - Canvas Clearing Fix Validation');
  console.log('Testing canvas layer persistence during frame selection...');
  
  try {
    // Get the Portrait Builder instance
    const app = window.portraitBuilderInstance;
    if (!app) {
      throw new Error('Portrait Builder instance not found');
    }

    // Step 1: Select a breed
    console.log('\n📝 Step 1: Selecting breed...');
    await app.selectBreed(TEST_CONFIG.testBreed);
    
    // Validate breed layer exists
    const hasBreedLayer = app.canvas && app.canvas.layers && app.canvas.layers.breed;
    console.log(`✅ Breed layer exists: ${hasBreedLayer}`);
    
    if (!hasBreedLayer) {
      throw new Error('Breed layer not set correctly');
    }

    // Step 2: Select a costume
    console.log('\n📝 Step 2: Selecting costume...');
    await app.selectCostume(TEST_CONFIG.testCostume);
    
    // Validate costume layer exists
    const hasCostumeLayer = app.canvas && app.canvas.layers && app.canvas.layers.costume;
    console.log(`✅ Costume layer exists: ${hasCostumeLayer}`);
    
    if (!hasCostumeLayer) {
      throw new Error('Costume layer not set correctly');
    }

    // Step 3: Log canvas state before frame selection
    console.log('\n📝 Step 3: Canvas state BEFORE frame selection:');
    logCanvasState(app.canvas, 'Main Canvas');
    if (app.progressiveCanvas) {
      logCanvasState(app.progressiveCanvas, 'Progressive Canvas');
    }

    // Step 4: Select a frame (THIS IS THE CRITICAL TEST)
    console.log('\n📝 Step 4: Selecting frame (CRITICAL TEST)...');
    console.log(`🎯 Testing frame selection: ${TEST_CONFIG.testFrame}`);
    
    // This is where the canvas clearing bug would occur
    await app.selectFrame(TEST_CONFIG.testFrame);
    
    // Step 5: Validate all layers still exist after frame selection
    console.log('\n📝 Step 5: Canvas state AFTER frame selection:');
    logCanvasState(app.canvas, 'Main Canvas');
    if (app.progressiveCanvas) {
      logCanvasState(app.progressiveCanvas, 'Progressive Canvas');
    }

    // Step 6: Validate fix is working
    console.log('\n📝 Step 6: Validating fix...');
    const validationResults = validateLayerPersistence(app);
    
    if (validationResults.success) {
      console.log('🎉 SUCCESS: Canvas clearing fix is working correctly!');
      console.log('✅ All layers preserved during frame selection');
      return true;
    } else {
      console.error('❌ FAILURE: Canvas clearing issue still exists');
      console.error('Missing layers:', validationResults.missingLayers);
      return false;
    }

  } catch (error) {
    console.error('❌ TEST ERROR:', error);
    return false;
  } finally {
    console.groupEnd();
  }
}

/**
 * Log canvas state for debugging
 */
function logCanvasState(canvas, canvasName) {
  if (!canvas) {
    console.log(`⚠️ ${canvasName}: Not available`);
    return;
  }

  console.log(`🎨 ${canvasName} State:`);
  console.log(`  - Visible: ${canvas.isVisible}`);
  console.log(`  - Rendering Enabled: ${canvas.renderingEnabled}`);
  console.log(`  - Layers:`, {
    breed: !!canvas.layers.breed,
    costume: !!canvas.layers.costume,
    frame: !!canvas.layers.frame,
    background: !!canvas.layers.background
  });
  
  // Check if canvas has been cleared (white background only)
  if (canvas.canvas) {
    const ctx = canvas.canvas.getContext('2d');
    const imageData = ctx.getImageData(0, 0, 1, 1);
    const isWhite = imageData.data[0] === 255 && 
                   imageData.data[1] === 255 && 
                   imageData.data[2] === 255;
    console.log(`  - Canvas appears cleared: ${isWhite}`);
  }
}

/**
 * Validate that all expected layers still exist after frame selection
 */
function validateLayerPersistence(app) {
  const results = {
    success: true,
    missingLayers: [],
    presentLayers: []
  };

  // Check main canvas layers
  if (app.canvas && app.canvas.layers) {
    TEST_CONFIG.expectedLayers.forEach(layerType => {
      if (app.canvas.layers[layerType]) {
        results.presentLayers.push(`main.${layerType}`);
      } else {
        results.missingLayers.push(`main.${layerType}`);
        results.success = false;
      }
    });
  } else {
    results.missingLayers.push('main.canvas');
    results.success = false;
  }

  // Check progressive canvas layers (if available)
  if (app.progressiveCanvas && app.progressiveCanvas.layers) {
    TEST_CONFIG.expectedLayers.forEach(layerType => {
      if (app.progressiveCanvas.layers[layerType]) {
        results.presentLayers.push(`progressive.${layerType}`);
      } else {
        results.missingLayers.push(`progressive.${layerType}`);
        // Progressive canvas missing layers is not a failure for this test
      }
    });
  }

  console.log('📊 Layer Validation Results:');
  console.log('  - Present layers:', results.presentLayers);
  console.log('  - Missing layers:', results.missingLayers);
  console.log('  - Test success:', results.success);

  return results;
}

/**
 * Run the test automatically when this script is loaded
 */
if (typeof window !== 'undefined') {
  // Wait for Portrait Builder to be ready
  const waitForPortraitBuilder = () => {
    if (window.portraitBuilderInstance) {
      console.log('🚀 Portrait Builder detected, running minimal reproduction test...');
      runMinimalReproductionTest();
    } else {
      console.log('⏳ Waiting for Portrait Builder to initialize...');
      setTimeout(waitForPortraitBuilder, 1000);
    }
  };

  // Start waiting for Portrait Builder
  waitForPortraitBuilder();
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runMinimalReproductionTest,
    logCanvasState,
    validateLayerPersistence,
    TEST_CONFIG
  };
}
