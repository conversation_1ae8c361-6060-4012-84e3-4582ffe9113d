/**
 * Canvas Clearing Regression Fix Validation
 * Tests the renderOffscreen() method fix to ensure layer preservation
 */

console.log('🧪 Starting Canvas Clearing Regression Fix Validation...');

/**
 * Test the renderOffscreen method fix
 */
async function validateRenderOffscreenFix() {
  console.log('🔍 Testing renderOffscreen() method layer preservation...');
  
  try {
    // Get the portrait builder instance
    const app = window.portraitBuilderInstance;
    if (!app || !app.canvas) {
      console.error('❌ Portrait Builder not found or canvas not initialized');
      return false;
    }

    const canvas = app.canvas;
    
    // Step 1: Set up a scenario with important layers
    console.log('📝 Step 1: Setting up test scenario with important layers...');
    
    // Simulate having important layers
    canvas.layers.breed = { id: 'test-breed', img: new Image() };
    canvas.layers.costume = { id: 'test-costume', img: new Image() };
    canvas.layers.frame = { id: 'test-frame', img: new Image() };
    
    console.log('✅ Test layers set up:', {
      breed: !!canvas.layers.breed,
      costume: !!canvas.layers.costume,
      frame: !!canvas.layers.frame,
      background: !!canvas.layers.background
    });

    // Step 2: Test renderOffscreen with important layers
    console.log('📝 Step 2: Testing renderOffscreen() with important layers...');
    
    // Create a simple render function for testing
    const testRenderFunction = (ctx, canvas) => {
      ctx.fillStyle = '#ff0000';
      ctx.fillRect(10, 10, 50, 50);
    };

    // Monitor console output for layer preservation messages
    const originalConsoleLog = console.log;
    let preservationMessageFound = false;
    
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('Canvas layers preserved during render')) {
        preservationMessageFound = true;
      }
      originalConsoleLog.apply(console, args);
    };

    // Call renderOffscreen - this should preserve layers
    if (canvas.offscreenCanvas && canvas.offscreenCtx) {
      canvas.renderOffscreen(testRenderFunction);
      
      if (preservationMessageFound) {
        console.log('✅ Layer preservation logic is working correctly');
      } else {
        console.warn('⚠️ Layer preservation message not found - check implementation');
      }
    } else {
      console.log('ℹ️ Offscreen canvas not available - testing direct rendering path');
    }

    // Restore original console.log
    console.log = originalConsoleLog;

    // Step 3: Test renderOffscreen without important layers
    console.log('📝 Step 3: Testing renderOffscreen() without important layers...');
    
    // Clear all layers
    canvas.layers.breed = null;
    canvas.layers.costume = null;
    canvas.layers.frame = null;
    canvas.layers.background = null;

    let clearingMessageFound = false;
    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('Canvas cleared (no important layers)')) {
        clearingMessageFound = true;
      }
      originalConsoleLog.apply(console, args);
    };

    // Call renderOffscreen - this should allow clearing
    if (canvas.offscreenCanvas && canvas.offscreenCtx) {
      canvas.renderOffscreen(testRenderFunction);
      
      if (clearingMessageFound) {
        console.log('✅ Canvas clearing logic is working correctly');
      } else {
        console.warn('⚠️ Canvas clearing message not found - check implementation');
      }
    }

    // Restore original console.log
    console.log = originalConsoleLog;

    console.log('✅ renderOffscreen() fix validation completed successfully');
    return true;

  } catch (error) {
    console.error('❌ Error during renderOffscreen() fix validation:', error);
    return false;
  }
}

/**
 * Test frame selection workflow to ensure no canvas clearing
 */
async function validateFrameSelectionWorkflow() {
  console.log('🔍 Testing complete frame selection workflow...');
  
  try {
    const app = window.portraitBuilderInstance;
    if (!app) {
      console.error('❌ Portrait Builder not found');
      return false;
    }

    // Step 1: Set up a complete portrait
    console.log('📝 Setting up complete portrait for frame selection test...');
    
    // Select breed
    if (app.selectBreed) {
      await app.selectBreed('golden-retriever');
      console.log('✅ Breed selected');
    }

    // Select costume
    if (app.selectCostume) {
      await app.selectCostume('royal-crown');
      console.log('✅ Costume selected');
    }

    // Select background
    if (app.selectBackground) {
      await app.selectBackground('color', '#ffffff');
      console.log('✅ Background selected');
    }

    // Step 2: Test frame selection
    console.log('📝 Testing frame selection with existing layers...');
    
    // Monitor for canvas clearing during frame selection
    let canvasClearedDuringFrameSelection = false;
    const originalClearRect = CanvasRenderingContext2D.prototype.clearRect;
    
    CanvasRenderingContext2D.prototype.clearRect = function(x, y, width, height) {
      // Check if this is a full canvas clear during frame selection
      const isFullClear = x === 0 && y === 0 && width === this.canvas.width && height === this.canvas.height;
      if (isFullClear) {
        console.warn('⚠️ Full canvas clear detected during frame selection');
        canvasClearedDuringFrameSelection = true;
      }
      return originalClearRect.call(this, x, y, width, height);
    };

    // Select frame
    if (app.selectFrame) {
      await app.selectFrame('classic-frame');
      console.log('✅ Frame selected');
    }

    // Restore original clearRect
    CanvasRenderingContext2D.prototype.clearRect = originalClearRect;

    if (!canvasClearedDuringFrameSelection) {
      console.log('✅ Frame selection completed without inappropriate canvas clearing');
      return true;
    } else {
      console.error('❌ Canvas was inappropriately cleared during frame selection');
      return false;
    }

  } catch (error) {
    console.error('❌ Error during frame selection workflow validation:', error);
    return false;
  }
}

/**
 * Run all validation tests
 */
async function runAllValidationTests() {
  console.log('🚀 Running Canvas Clearing Regression Fix Validation Tests...');
  console.log('='.repeat(60));

  const results = {
    renderOffscreenFix: false,
    frameSelectionWorkflow: false
  };

  // Test 1: renderOffscreen fix
  results.renderOffscreenFix = await validateRenderOffscreenFix();
  
  // Test 2: Frame selection workflow
  results.frameSelectionWorkflow = await validateFrameSelectionWorkflow();

  // Summary
  console.log('='.repeat(60));
  console.log('📊 VALIDATION RESULTS SUMMARY:');
  console.log(`  renderOffscreen() Fix: ${results.renderOffscreenFix ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  Frame Selection Workflow: ${results.frameSelectionWorkflow ? '✅ PASS' : '❌ FAIL'}`);
  
  const allTestsPassed = Object.values(results).every(result => result === true);
  
  if (allTestsPassed) {
    console.log('🎉 ALL TESTS PASSED - Canvas clearing regression fix is working correctly!');
  } else {
    console.log('⚠️ SOME TESTS FAILED - Additional investigation may be required');
  }

  return results;
}

// Auto-run tests if this script is loaded directly
if (typeof window !== 'undefined' && window.portraitBuilderInstance) {
  // Wait a moment for the app to fully initialize
  setTimeout(() => {
    runAllValidationTests();
  }, 1000);
} else {
  console.log('ℹ️ Validation script loaded. Run runAllValidationTests() when Portrait Builder is ready.');
}

// Export for manual testing
if (typeof window !== 'undefined') {
  window.validateCanvasClearingFix = runAllValidationTests;
}
