# Royal Portrait Builder Canvas Clearing Regression - Technical Spike Document

**Document ID:** RPB-SPIKE-001  
**Date:** 2025-07-20  
**Priority:** P0 - Critical User Experience Issue  
**Estimated Duration:** 5-7 business days  
**Status:** Ready for Implementation

---

## Executive Summary

The Royal Portrait Builder experiences a critical canvas clearing regression where all canvas layers (breed, costume, background, frame) are cleared during frame selection operations, despite 5 comprehensive fixes targeting the identified failure points. This technical spike will systematically identify the actual root cause through runtime investigation and implement a definitive solution.

**Problem Statement:** Canvas layers are cleared during frame selection despite theoretical fixes, indicating a fundamental gap between our code analysis and runtime execution.

**Objective:** Identify the actual canvas clearing mechanism and implement a robust solution that preserves all layers during frame selection operations.

---

## Scope and Objectives

### **Specific Problem Definition**

**Primary Issue:**
- Canvas layers (breed, costume, background, frame) are cleared during frame selection
- Issue persists despite 5 implemented fixes targeting identified failure points
- User sees blank/white canvas after selecting a frame
- All previous selections (breed, costume, background) are visually lost

**Secondary Issues:**
- Disconnect between theoretical code analysis and runtime behavior
- Unknown execution path causing canvas clearing
- Potential external interference from Shopify theme framework or third-party scripts

### **Success Criteria**

**Primary Success Criteria:**
- [ ] Frame selection preserves all existing canvas layers
- [ ] User sees complete portrait (breed + costume + background + frame) after frame selection
- [ ] No visual flicker or clearing during frame selection process
- [ ] Solution works consistently across all supported browsers

**Secondary Success Criteria:**
- [ ] Root cause of canvas clearing definitively identified
- [ ] Execution path during frame selection fully documented
- [ ] Robust solution prevents future canvas clearing regressions
- [ ] Performance impact of solution is negligible (<50ms additional processing time)

### **Investigation Timeline**

**Total Duration:** 5-7 business days

**Phase Breakdown:**
- **Phase 1:** Runtime Execution Tracing (2 days)
- **Phase 2:** Canvas State Protection Implementation (1 day)
- **Phase 3:** Root Cause Analysis and Solution (2 days)
- **Phase 4:** Testing and Validation (1-2 days)

### **Resource Requirements**

**Technical Resources:**
- Senior developer with JavaScript debugging expertise
- Access to browser DevTools and debugging environment
- Shopify theme development environment for testing

**Tools Required:**
- Browser DevTools (Chrome/Firefox)
- Code editor with search/replace capabilities
- Local development server for testing
- Console logging and analysis tools

---

## Technical Investigation Plan

### **Primary Approach: Runtime Execution Tracing**

#### **Objective**
Instrument all canvas-related operations to trace the actual execution flow during frame selection and identify the real clearing mechanism.

#### **Implementation Strategy**

**Step 1: Canvas Method Instrumentation**
```javascript
// Instrument all canvas clearing methods
const instrumentCanvasClearing = () => {
  // Main canvas clearing methods
  const originalClearCanvas = PortraitCanvas.prototype.clearCanvas;
  PortraitCanvas.prototype.clearCanvas = function(...args) {
    console.group('🚨 CANVAS CLEARED');
    console.trace('Call stack:');
    console.log('Canvas state before clear:', {
      layers: this.layers,
      isVisible: this.isVisible,
      renderingEnabled: this.renderingEnabled
    });
    console.log('Arguments:', args);
    console.groupEnd();
    
    return originalClearCanvas.apply(this, args);
  };

  // Canvas context clearing methods
  const originalClearRect = CanvasRenderingContext2D.prototype.clearRect;
  CanvasRenderingContext2D.prototype.clearRect = function(...args) {
    console.group('🚨 CANVAS CONTEXT CLEARED');
    console.trace('Call stack:');
    console.log('Clear rect arguments:', args);
    console.groupEnd();
    
    return originalClearRect.apply(this, args);
  };

  // Canvas hide methods
  const originalHideCanvas = PortraitCanvas.prototype.hideCanvas;
  PortraitCanvas.prototype.hideCanvas = function(...args) {
    console.group('👁️ CANVAS HIDDEN');
    console.trace('Call stack:');
    console.log('Canvas state:', {
      layers: this.layers,
      hasImportantLayers: this.layers.breed || this.layers.costume || this.layers.frame || this.layers.background
    });
    console.groupEnd();
    
    return originalHideCanvas.apply(this, args);
  };
};
```

**Step 2: Frame Selection Workflow Tracing**
```javascript
// Instrument frame selection methods
const instrumentFrameSelection = () => {
  const originalSelectFrame = RoyalPortraitBuilderApp.prototype.selectFrame;
  RoyalPortraitBuilderApp.prototype.selectFrame = function(frameId) {
    console.group('🖼️ FRAME SELECTION STARTED');
    console.log('Frame ID:', frameId);
    console.log('Canvas state before selection:', {
      mainCanvas: this.canvas ? this.canvas.layers : null,
      progressiveCanvas: this.progressiveCanvas ? this.progressiveCanvas.layers : null
    });
    console.time('Frame Selection Duration');
    
    const result = originalSelectFrame.call(this, frameId);
    
    // Log state after selection (with delay to catch async operations)
    setTimeout(() => {
      console.log('Canvas state after selection:', {
        mainCanvas: this.canvas ? this.canvas.layers : null,
        progressiveCanvas: this.progressiveCanvas ? this.progressiveCanvas.layers : null
      });
      console.timeEnd('Frame Selection Duration');
      console.groupEnd();
    }, 100);
    
    return result;
  };
};
```

**Step 3: Navigation and Step Management Tracing**
```javascript
// Instrument navigation methods
const instrumentNavigation = () => {
  const originalUpdateNavigationButtons = StepNavigator.prototype.updateNavigationButtons;
  StepNavigator.prototype.updateNavigationButtons = function(...args) {
    console.group('🧭 NAVIGATION UPDATE');
    console.log('Current step:', this.currentStep);
    console.log('Arguments:', args);
    console.trace('Call stack:');
    console.groupEnd();
    
    return originalUpdateNavigationButtons.apply(this, args);
  };

  const originalUpdateCanvasVisibility = StepNavigator.prototype.updateCanvasVisibility;
  StepNavigator.prototype.updateCanvasVisibility = function(...args) {
    console.group('👁️ CANVAS VISIBILITY UPDATE');
    console.log('Current step:', this.currentStep);
    console.trace('Call stack:');
    console.groupEnd();
    
    return originalUpdateCanvasVisibility.apply(this, args);
  };
};
```

**Step 4: Comprehensive Event Tracing**
```javascript
// Monitor all events that might affect canvas
const instrumentEventHandlers = () => {
  const eventsToMonitor = ['click', 'change', 'input', 'focus', 'blur', 'resize'];
  
  eventsToMonitor.forEach(eventType => {
    document.addEventListener(eventType, (e) => {
      if (e.target.closest('.royal-portrait-builder')) {
        console.log(`🎯 Event: ${eventType}`, {
          target: e.target,
          currentTarget: e.currentTarget,
          timestamp: Date.now()
        });
        
        // Check canvas state after event
        setTimeout(() => {
          const app = window.portraitBuilderInstance;
          if (app) {
            console.log('Canvas state after event:', {
              mainCanvas: app.canvas ? app.canvas.layers : null,
              progressiveCanvas: app.progressiveCanvas ? app.progressiveCanvas.layers : null
            });
          }
        }, 10);
      }
    }, true);
  });
};
```

**Logging Format Specification:**
```javascript
// Standardized logging format
const createTraceLog = (operation, data) => ({
  timestamp: Date.now(),
  operation: operation,
  callStack: new Error().stack,
  canvasState: {
    mainCanvas: data.mainCanvas || null,
    progressiveCanvas: data.progressiveCanvas || null,
    layers: data.layers || null
  },
  additionalData: data.additional || {}
});
```

#### **Call Stack Analysis Techniques**

**Technique 1: Stack Trace Parsing**
```javascript
const parseCallStack = (stack) => {
  const lines = stack.split('\n');
  return lines.slice(1, 10).map(line => {
    const match = line.match(/at\s+(.+?)\s+\((.+?):(\d+):(\d+)\)/);
    return match ? {
      function: match[1],
      file: match[2],
      line: parseInt(match[3]),
      column: parseInt(match[4])
    } : { raw: line.trim() };
  });
};
```

**Technique 2: Critical Path Identification**
```javascript
const identifyCriticalPath = (traces) => {
  // Analyze traces to find common execution paths leading to canvas clearing
  const pathFrequency = {};
  traces.forEach(trace => {
    const path = trace.callStack.slice(0, 5).map(frame => frame.function).join(' -> ');
    pathFrequency[path] = (pathFrequency[path] || 0) + 1;
  });
  
  return Object.entries(pathFrequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);
};
```

### **Secondary Approach: Canvas State Protection**

#### **Objective**
Implement a safety net that prevents canvas clearing and provides immediate user experience protection while investigation continues.

#### **Implementation Strategy**

**Step 1: Canvas State Monitoring**
```javascript
class CanvasStateProtector {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.monitoringInterval = options.interval || 100; // 100ms monitoring
    this.backupInterval = options.backupInterval || 500; // 500ms backup
    this.layerBackup = null;
    this.isProtectionEnabled = true;
    this.clearingDetected = false;
    
    this.startMonitoring();
  }

  startMonitoring() {
    // Continuous state monitoring
    this.monitoringTimer = setInterval(() => {
      if (this.isProtectionEnabled) {
        this.checkCanvasState();
      }
    }, this.monitoringInterval);

    // Periodic backup
    this.backupTimer = setInterval(() => {
      if (this.isProtectionEnabled && this.hasImportantLayers()) {
        this.backupCanvasState();
      }
    }, this.backupInterval);
  }

  checkCanvasState() {
    const hasLayers = this.hasImportantLayers();
    const isCanvasCleared = this.isCanvasCleared();
    
    if (!hasLayers && this.layerBackup && !this.clearingDetected) {
      console.warn('🛡️ Canvas clearing detected, initiating restoration');
      this.clearingDetected = true;
      this.restoreCanvasState();
    }
  }

  hasImportantLayers() {
    return this.canvas.layers && (
      this.canvas.layers.breed ||
      this.canvas.layers.costume ||
      this.canvas.layers.frame ||
      this.canvas.layers.background
    );
  }

  isCanvasCleared() {
    if (!this.canvas.canvas) return true;
    
    const ctx = this.canvas.canvas.getContext('2d');
    const imageData = ctx.getImageData(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
    const data = imageData.data;
    
    // Check if canvas is mostly white/transparent
    let nonWhitePixels = 0;
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const a = data[i + 3];
      
      if (!(r > 250 && g > 250 && b > 250) && a > 0) {
        nonWhitePixels++;
      }
    }
    
    return nonWhitePixels < (data.length / 4) * 0.01; // Less than 1% non-white pixels
  }

  backupCanvasState() {
    this.layerBackup = {
      layers: JSON.parse(JSON.stringify(this.canvas.layers)),
      canvasData: this.canvas.canvas.toDataURL(),
      timestamp: Date.now()
    };
    
    console.log('💾 Canvas state backed up', {
      layers: Object.keys(this.layerBackup.layers),
      timestamp: this.layerBackup.timestamp
    });
  }

  async restoreCanvasState() {
    if (!this.layerBackup) {
      console.warn('🛡️ No backup available for restoration');
      return;
    }

    try {
      console.log('🔄 Restoring canvas state from backup');
      
      // Restore layer state
      this.canvas.layers = { ...this.layerBackup.layers };
      
      // Restore visual content
      const img = new Image();
      img.onload = () => {
        const ctx = this.canvas.canvas.getContext('2d');
        ctx.clearRect(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
        ctx.drawImage(img, 0, 0);
        console.log('✅ Canvas state restored successfully');
        this.clearingDetected = false;
      };
      img.src = this.layerBackup.canvasData;
      
    } catch (error) {
      console.error('❌ Failed to restore canvas state:', error);
    }
  }

  destroy() {
    if (this.monitoringTimer) clearInterval(this.monitoringTimer);
    if (this.backupTimer) clearInterval(this.backupTimer);
    this.isProtectionEnabled = false;
  }
}
```

### **Tertiary Approach: Minimal Reproduction**

#### **Objective**
Create an isolated test case that reproduces the canvas clearing issue without the complexity of the full Royal Portrait Builder system.

#### **Test Case Design**

**Step 1: Minimal HTML Structure**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Canvas Clearing Reproduction Test</title>
    <style>
        .test-container { width: 800px; margin: 0 auto; }
        .canvas-container { border: 1px solid #ccc; margin: 20px 0; }
        .frame-selector { display: flex; gap: 10px; margin: 20px 0; }
        .frame-option { padding: 10px; border: 1px solid #ddd; cursor: pointer; }
        .frame-option:hover { background: #f0f0f0; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Canvas Clearing Reproduction Test</h1>
        
        <div class="canvas-container">
            <canvas id="test-canvas" width="400" height="400"></canvas>
        </div>
        
        <div class="frame-selector">
            <div class="frame-option" data-frame-id="classic">Classic Frame</div>
            <div class="frame-option" data-frame-id="modern">Modern Frame</div>
            <div class="frame-option" data-frame-id="ornate">Ornate Frame</div>
        </div>
        
        <div id="test-log"></div>
    </div>
    
    <script src="minimal-reproduction.js"></script>
</body>
</html>
```

**Step 2: Minimal JavaScript Implementation**
```javascript
class MinimalCanvasTest {
  constructor() {
    this.canvas = document.getElementById('test-canvas');
    this.ctx = this.canvas.getContext('2d');
    this.layers = {};
    this.logElement = document.getElementById('test-log');
    
    this.init();
  }

  init() {
    // Set up initial canvas content
    this.drawBreedLayer();
    this.drawCostumeLayer();
    this.drawBackgroundLayer();
    
    // Set up frame selection handlers
    document.querySelectorAll('.frame-option').forEach(option => {
      option.addEventListener('click', (e) => {
        const frameId = e.target.dataset.frameId;
        this.selectFrame(frameId);
      });
    });
    
    this.log('Minimal test initialized');
  }

  drawBreedLayer() {
    this.ctx.fillStyle = '#8B4513';
    this.ctx.fillRect(100, 150, 200, 200);
    this.ctx.fillStyle = '#000';
    this.ctx.fillRect(150, 200, 20, 20); // Eye
    this.ctx.fillRect(230, 200, 20, 20); // Eye
    this.layers.breed = { drawn: true, timestamp: Date.now() };
    this.log('Breed layer drawn');
  }

  drawCostumeLayer() {
    this.ctx.fillStyle = '#FFD700';
    this.ctx.fillRect(180, 120, 40, 30); // Crown
    this.layers.costume = { drawn: true, timestamp: Date.now() };
    this.log('Costume layer drawn');
  }

  drawBackgroundLayer() {
    this.ctx.fillStyle = '#87CEEB';
    this.ctx.fillRect(0, 0, 400, 100); // Sky
    this.ctx.fillStyle = '#90EE90';
    this.ctx.fillRect(0, 100, 400, 300); // Ground
    this.layers.background = { drawn: true, timestamp: Date.now() };
    this.log('Background layer drawn');
  }

  selectFrame(frameId) {
    this.log(`Frame selection started: ${frameId}`);
    
    // Log canvas state before frame selection
    this.logCanvasState('before frame selection');
    
    // Simulate frame selection process
    this.drawFrameLayer(frameId);
    
    // Check canvas state after frame selection
    setTimeout(() => {
      this.logCanvasState('after frame selection');
    }, 10);
  }

  drawFrameLayer(frameId) {
    const frameStyles = {
      classic: { color: '#8B4513', width: 10 },
      modern: { color: '#000000', width: 5 },
      ornate: { color: '#FFD700', width: 15 }
    };
    
    const style = frameStyles[frameId];
    if (!style) return;
    
    this.ctx.strokeStyle = style.color;
    this.ctx.lineWidth = style.width;
    this.ctx.strokeRect(50, 50, 300, 300);
    
    this.layers.frame = { frameId, drawn: true, timestamp: Date.now() };
    this.log(`Frame layer drawn: ${frameId}`);
  }

  logCanvasState(context) {
    const imageData = this.ctx.getImageData(0, 0, 400, 400);
    const data = imageData.data;
    
    let nonWhitePixels = 0;
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const a = data[i + 3];
      
      if (!(r > 250 && g > 250 && b > 250) && a > 0) {
        nonWhitePixels++;
      }
    }
    
    const totalPixels = data.length / 4;
    const contentPercentage = (nonWhitePixels / totalPixels * 100).toFixed(2);
    
    this.log(`Canvas state ${context}: ${contentPercentage}% content, layers: ${Object.keys(this.layers).join(', ')}`);
  }

  log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${timestamp}] ${message}`;
    this.logElement.appendChild(logEntry);
    console.log(message);
  }
}

// Initialize test when page loads
document.addEventListener('DOMContentLoaded', () => {
  new MinimalCanvasTest();
});
```

---

## Execution Strategy

### **Phase 1: Runtime Execution Tracing (Days 1-2)**

#### **Day 1: Instrumentation Setup**

**Morning (4 hours):**
- [ ] Implement canvas method instrumentation
- [ ] Set up frame selection workflow tracing
- [ ] Configure logging format and output

**Afternoon (4 hours):**
- [ ] Add navigation and step management tracing
- [ ] Implement event handler monitoring
- [ ] Test instrumentation in development environment

**Deliverables:**
- Instrumented codebase with comprehensive logging
- Logging configuration and format specification
- Initial test results from development environment

#### **Day 2: Data Collection and Analysis**

**Morning (4 hours):**
- [ ] Deploy instrumented code to test environment
- [ ] Perform systematic frame selection testing
- [ ] Collect execution traces and call stacks

**Afternoon (4 hours):**
- [ ] Analyze collected traces for patterns
- [ ] Identify critical execution paths
- [ ] Document findings and hypotheses

**Deliverables:**
- Comprehensive execution trace logs
- Call stack analysis report
- Identified critical paths and potential root causes

### **Phase 2: Canvas State Protection (Day 3)**

#### **Morning (4 hours):**
- [ ] Implement CanvasStateProtector class
- [ ] Configure monitoring intervals and backup mechanisms
- [ ] Test protection system with known clearing scenarios

#### **Afternoon (4 hours):**
- [ ] Optimize performance and reduce overhead
- [ ] Integrate protection system with main application
- [ ] Validate user experience improvements

**Deliverables:**
- Working canvas state protection system
- Performance impact analysis
- User experience validation report

### **Phase 3: Root Cause Analysis and Solution (Days 4-5)**

#### **Day 4: Root Cause Identification**

**Morning (4 hours):**
- [ ] Analyze Phase 1 findings to identify root cause
- [ ] Validate root cause with minimal reproduction test
- [ ] Design targeted solution approach

**Afternoon (4 hours):**
- [ ] Implement targeted solution
- [ ] Test solution in isolation
- [ ] Validate solution addresses root cause

#### **Day 5: Solution Integration**

**Morning (4 hours):**
- [ ] Integrate solution with main application
- [ ] Remove temporary instrumentation code
- [ ] Test integrated solution

**Afternoon (4 hours):**
- [ ] Performance testing and optimization
- [ ] Cross-browser compatibility testing
- [ ] Documentation and code review

**Deliverables:**
- Root cause analysis report
- Implemented and tested solution
- Performance and compatibility validation

### **Phase 4: Testing and Validation (Days 6-7)**

#### **Day 6: Comprehensive Testing**

**Morning (4 hours):**
- [ ] End-to-end user workflow testing
- [ ] Edge case and error scenario testing
- [ ] Performance regression testing

**Afternoon (4 hours):**
- [ ] Cross-browser testing (Chrome, Firefox, Safari, Edge)
- [ ] Mobile device testing
- [ ] Accessibility testing

#### **Day 7: Final Validation and Documentation**

**Morning (4 hours):**
- [ ] User acceptance testing
- [ ] Final performance validation
- [ ] Security review

**Afternoon (4 hours):**
- [ ] Documentation completion
- [ ] Knowledge transfer preparation
- [ ] Deployment planning

**Deliverables:**
- Comprehensive test results
- Final solution documentation
- Deployment and rollback procedures

---

## Success Metrics and Definition of Done

### **Primary Success Metrics**

**Metric 1: Canvas Layer Persistence**
- **Target:** 100% of frame selections preserve all existing layers
- **Measurement:** Automated testing of layer state before/after frame selection
- **Validation:** Visual verification and programmatic layer state checking

**Metric 2: User Experience Quality**
- **Target:** No visible canvas clearing or flicker during frame selection
- **Measurement:** User testing and visual validation
- **Validation:** Cross-browser testing and mobile device verification

**Metric 3: Performance Impact**
- **Target:** < 50ms additional processing time for frame selection
- **Measurement:** Performance.now() timing measurements
- **Validation:** Performance regression testing

### **Definition of "Done"**

#### **Investigation Phase Complete When:**
- [ ] Root cause of canvas clearing definitively identified
- [ ] Execution trace analysis completed and documented
- [ ] Canvas state protection system implemented and tested
- [ ] Minimal reproduction test case created and validated
- [ ] All investigation deliverables completed and reviewed

#### **Solution Phase Complete When:**
- [ ] Production-ready solution implemented
- [ ] All primary success metrics achieved
- [ ] Comprehensive testing completed (unit, integration, e2e)
- [ ] Cross-browser compatibility verified
- [ ] Performance validation completed
- [ ] Documentation completed and reviewed
- [ ] Solution deployed and monitored in production
- [ ] User acceptance testing passed
- [ ] Knowledge transfer completed

#### **Overall Spike Complete When:**
- [ ] Canvas clearing regression completely resolved
- [ ] User experience restored to expected quality
- [ ] Solution is stable and performant in production
- [ ] Team has complete understanding of root cause and solution
- [ ] Monitoring and maintenance procedures established
- [ ] Post-mortem analysis completed and lessons learned documented

---

## Conclusion

This technical spike provides a comprehensive, systematic approach to resolving the Royal Portrait Builder canvas clearing regression. By combining runtime investigation, protective measures, and targeted solutions, we will definitively identify and resolve the root cause while maintaining excellent user experience throughout the investigation process.

The spike addresses the critical gap between our theoretical understanding and runtime reality through empirical investigation and data-driven analysis. The phased approach ensures we can provide immediate user experience protection while conducting thorough root cause analysis.

**Expected Outcome:** Complete resolution of the canvas clearing regression with a robust, performant solution that prevents future occurrences of similar issues.
