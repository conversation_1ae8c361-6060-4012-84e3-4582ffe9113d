/**
 * Task 3.3: Integration and Cleanup - Comprehensive Integration Test
 * 
 * This script validates that the canvas clearing solution is fully integrated
 * with the main Portrait Builder application and works correctly in all scenarios.
 * 
 * Usage:
 * 1. Load Royal Portrait Builder in browser
 * 2. Open browser console
 * 3. Copy and paste this script
 * 4. Run: await runIntegrationTest()
 */

class IntegrationTestSuite {
  constructor() {
    this.testResults = [];
    this.app = null;
    this.startTime = null;
  }

  /**
   * Initialize the test suite
   */
  async initialize() {
    console.group('🚀 INTEGRATION TEST SUITE - Task 3.3 Validation');
    console.log('Initializing comprehensive integration test...');
    this.startTime = performance.now();

    // Wait for Portrait Builder to be available
    let attempts = 0;
    while (!window.portraitBuilderInstance && attempts < 10) {
      console.log('⏳ Waiting for Portrait Builder to initialize...');
      await this.sleep(1000);
      attempts++;
    }

    if (!window.portraitBuilderInstance) {
      throw new Error('Portrait Builder instance not found after 10 seconds');
    }

    this.app = window.portraitBuilderInstance;
    console.log('✅ Portrait Builder instance found');
    
    // Check debugging instrumentation status
    const debuggingEnabled = window.DEBUG_CANVAS_CLEARING || false;
    console.log(`🔧 Debugging instrumentation: ${debuggingEnabled ? 'ENABLED' : 'DISABLED'}`);
    
    return true;
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Test complete user workflow with canvas clearing fix
   */
  async testCompleteUserWorkflow() {
    console.group('🧪 TEST: Complete User Workflow');
    
    try {
      console.log('📝 Testing complete user workflow with canvas clearing fix...');
      
      // Step 1: Select breed
      console.log('Step 1: Selecting breed...');
      await this.app.selectBreed('golden-retriever');
      await this.sleep(500);
      
      const breedLayerExists = this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.breed;
      console.log(`✅ Breed layer exists: ${breedLayerExists}`);
      
      // Step 2: Select costume (if available)
      if (this.app.selectCostume) {
        console.log('Step 2: Selecting costume...');
        await this.app.selectCostume('royal-crown');
        await this.sleep(500);
        
        const costumeLayerExists = this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.costume;
        console.log(`✅ Costume layer exists: ${costumeLayerExists}`);
      }
      
      // Step 3: Select frame (CRITICAL TEST)
      console.log('Step 3: Selecting frame (CRITICAL TEST)...');
      const layersBeforeFrame = {
        breed: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.breed),
        costume: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.costume)
      };
      console.log('Layers before frame selection:', layersBeforeFrame);
      
      await this.app.selectFrame('classic-frame');
      await this.sleep(500);
      
      const layersAfterFrame = {
        breed: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.breed),
        costume: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.costume),
        frame: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.frame)
      };
      console.log('Layers after frame selection:', layersAfterFrame);
      
      // Validate that all layers persisted
      const allLayersPersisted = layersAfterFrame.breed && layersAfterFrame.frame;
      const costumePreserved = !layersBeforeFrame.costume || layersAfterFrame.costume;
      
      if (allLayersPersisted && costumePreserved) {
        console.log('✅ PASS: Complete workflow preserves all layers');
        return { success: true, message: 'Complete workflow successful', details: layersAfterFrame };
      } else {
        console.log('❌ FAIL: Layers lost during workflow');
        return { success: false, message: 'Layer loss detected', details: { before: layersBeforeFrame, after: layersAfterFrame } };
      }
      
    } catch (error) {
      console.error('❌ ERROR in workflow test:', error);
      return { success: false, message: error.message };
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Test rapid frame selection to check for race conditions
   */
  async testRapidFrameSelection() {
    console.group('🧪 TEST: Rapid Frame Selection');
    
    try {
      console.log('📝 Testing rapid frame selection for race conditions...');
      
      // Ensure we have a breed selected first
      await this.app.selectBreed('labrador');
      await this.sleep(300);
      
      // Rapid frame selection
      const frames = ['classic-frame', 'ornate-frame', 'modern-frame'];
      console.log('Performing rapid frame selection...');
      
      for (let i = 0; i < frames.length; i++) {
        console.log(`Selecting frame ${i + 1}: ${frames[i]}`);
        await this.app.selectFrame(frames[i]);
        await this.sleep(100); // Very short delay to simulate rapid clicking
      }
      
      // Check final state
      await this.sleep(500); // Allow time for all operations to complete
      
      const finalLayers = {
        breed: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.breed),
        frame: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.frame)
      };
      
      if (finalLayers.breed && finalLayers.frame) {
        console.log('✅ PASS: Rapid frame selection preserves layers');
        return { success: true, message: 'Rapid selection handled correctly', details: finalLayers };
      } else {
        console.log('❌ FAIL: Layers lost during rapid selection');
        return { success: false, message: 'Race condition detected', details: finalLayers };
      }
      
    } catch (error) {
      console.error('❌ ERROR in rapid selection test:', error);
      return { success: false, message: error.message };
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Test step navigation integration
   */
  async testStepNavigationIntegration() {
    console.group('🧪 TEST: Step Navigation Integration');
    
    try {
      console.log('📝 Testing step navigation with canvas layers...');
      
      // Set up layers
      await this.app.selectBreed('german-shepherd');
      await this.sleep(300);
      await this.app.selectFrame('ornate-frame');
      await this.sleep(300);
      
      const layersBeforeNavigation = {
        breed: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.breed),
        frame: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.frame)
      };
      console.log('Layers before navigation:', layersBeforeNavigation);
      
      // Test step navigation (if available)
      if (this.app.stepNavigator) {
        console.log('Testing step navigation...');
        
        // Navigate to different steps
        if (typeof this.app.stepNavigator.goToStep === 'function') {
          await this.app.stepNavigator.goToStep(2);
          await this.sleep(300);
          await this.app.stepNavigator.goToStep(4);
          await this.sleep(300);
        }
        
        const layersAfterNavigation = {
          breed: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.breed),
          frame: !!(this.app.canvas && this.app.canvas.layers && this.app.canvas.layers.frame)
        };
        console.log('Layers after navigation:', layersAfterNavigation);
        
        if (layersAfterNavigation.breed && layersAfterNavigation.frame) {
          console.log('✅ PASS: Step navigation preserves layers');
          return { success: true, message: 'Step navigation integration successful' };
        } else {
          console.log('❌ FAIL: Layers lost during step navigation');
          return { success: false, message: 'Step navigation caused layer loss' };
        }
      } else {
        console.log('⚠️ SKIP: Step navigator not available');
        return { success: true, message: 'Step navigator not available - test skipped' };
      }
      
    } catch (error) {
      console.error('❌ ERROR in step navigation test:', error);
      return { success: false, message: error.message };
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Test debugging instrumentation is properly disabled
   */
  async testDebuggingInstrumentationDisabled() {
    console.group('🧪 TEST: Debugging Instrumentation Status');
    
    try {
      console.log('📝 Checking debugging instrumentation status...');
      
      // Check if debugging is disabled
      const debuggingEnabled = window.DEBUG_CANVAS_CLEARING || false;
      console.log(`Debug flag: ${debuggingEnabled}`);
      
      // Check if instrumentation is loaded but not active
      const instrumentationLoaded = !!window.CanvasDebuggingInstrumentation;
      console.log(`Instrumentation loaded: ${instrumentationLoaded}`);
      
      if (!debuggingEnabled) {
        console.log('✅ PASS: Debugging instrumentation properly disabled');
        return { success: true, message: 'Debugging instrumentation disabled correctly' };
      } else {
        console.log('⚠️ WARNING: Debugging instrumentation still enabled');
        return { success: false, message: 'Debugging instrumentation should be disabled' };
      }
      
    } catch (error) {
      console.error('❌ ERROR in instrumentation test:', error);
      return { success: false, message: error.message };
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Run all integration tests
   */
  async runAllTests() {
    const results = [];
    
    // Test 1: Complete user workflow
    const test1 = await this.testCompleteUserWorkflow();
    results.push({ test: 'Complete User Workflow', ...test1 });
    
    // Test 2: Rapid frame selection
    const test2 = await this.testRapidFrameSelection();
    results.push({ test: 'Rapid Frame Selection', ...test2 });
    
    // Test 3: Step navigation integration
    const test3 = await this.testStepNavigationIntegration();
    results.push({ test: 'Step Navigation Integration', ...test3 });
    
    // Test 4: Debugging instrumentation status
    const test4 = await this.testDebuggingInstrumentationDisabled();
    results.push({ test: 'Debugging Instrumentation Status', ...test4 });
    
    // Generate summary
    const passed = results.filter(r => r.success).length;
    const total = results.length;
    const duration = performance.now() - this.startTime;
    
    console.log('\n📊 INTEGRATION TEST SUMMARY:');
    console.log(`✅ Passed: ${passed}/${total} tests`);
    console.log(`❌ Failed: ${total - passed}/${total} tests`);
    console.log(`⏱️ Duration: ${duration.toFixed(2)}ms`);
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.test}: ${result.message}`);
    });
    
    const overallSuccess = passed === total;
    console.log(`\n🎯 INTEGRATION TEST RESULT: ${overallSuccess ? 'SUCCESS' : 'FAILURE'}`);
    
    if (overallSuccess) {
      console.log('🎉 Canvas clearing solution fully integrated and working!');
      console.log('✅ Ready for production deployment');
    } else {
      console.log('⚠️ Integration issues detected - review failed tests');
    }
    
    console.groupEnd();
    return { overallSuccess, results, passed, total, duration };
  }
}

/**
 * Main integration test function
 */
async function runIntegrationTest() {
  const testSuite = new IntegrationTestSuite();
  
  try {
    await testSuite.initialize();
    return await testSuite.runAllTests();
  } catch (error) {
    console.error('❌ Integration test failed:', error);
    return { overallSuccess: false, error: error.message };
  }
}

// Make available globally
window.IntegrationTestSuite = IntegrationTestSuite;
window.runIntegrationTest = runIntegrationTest;

console.log('🔧 Integration Test Suite Loaded');
console.log('🚀 Run: await runIntegrationTest()');
