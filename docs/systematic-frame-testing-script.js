/**
 * Systematic Frame Selection Testing Script
 * Run this in the browser console to test frame selection scenarios
 * 
 * Usage:
 * 1. Load Royal Portrait Builder page
 * 2. Open browser console
 * 3. Copy and paste this entire script
 * 4. Run: await runSystematicFrameTests()
 * 5. Copy console output to execution traces file
 */

class FrameSelectionTester {
  constructor() {
    this.testResults = [];
    this.currentTest = null;
    this.originalConsoleLog = console.log;
    this.originalConsoleWarn = console.warn;
    this.originalConsoleGroup = console.group;
    this.originalConsoleGroupEnd = console.groupEnd;
    this.logBuffer = [];
  }

  /**
   * Start capturing console output
   */
  startCapture() {
    this.logBuffer = [];
    const self = this;
    
    console.log = function(...args) {
      self.originalConsoleLog.apply(console, args);
      self.logBuffer.push(`[LOG] ${new Date().toISOString()}: ${args.join(' ')}`);
    };
    
    console.warn = function(...args) {
      self.originalConsoleWarn.apply(console, args);
      self.logBuffer.push(`[WARN] ${new Date().toISOString()}: ${args.join(' ')}`);
    };
    
    console.group = function(...args) {
      self.originalConsoleGroup.apply(console, args);
      self.logBuffer.push(`[GROUP] ${new Date().toISOString()}: ${args.join(' ')}`);
    };
    
    console.groupEnd = function() {
      self.originalConsoleGroupEnd.apply(console);
      self.logBuffer.push(`[GROUP_END] ${new Date().toISOString()}`);
    };
  }

  /**
   * Stop capturing console output
   */
  stopCapture() {
    console.log = this.originalConsoleLog;
    console.warn = this.originalConsoleWarn;
    console.group = this.originalConsoleGroup;
    console.groupEnd = this.originalConsoleGroupEnd;
    
    return [...this.logBuffer];
  }

  /**
   * Wait for a specified amount of time
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current canvas state
   */
  getCanvasState() {
    const app = window.portraitBuilderInstance;
    if (!app) return { error: 'No portrait builder instance found' };
    
    return {
      mainCanvas: {
        visible: app.canvas ? (!app.canvas.canvas.style.display || app.canvas.canvas.style.display !== 'none') : false,
        layers: app.canvas ? app.canvas.layers : null,
        renderingEnabled: app.canvas ? app.canvas.renderingEnabled : false
      },
      progressiveCanvas: {
        visible: app.progressiveCanvas ? (!app.progressiveCanvas.canvas.style.display || app.progressiveCanvas.canvas.style.display !== 'none') : false,
        layers: app.progressiveCanvas ? app.progressiveCanvas.layers : null,
        renderingEnabled: app.progressiveCanvas ? app.progressiveCanvas.renderingEnabled : false
      },
      currentStep: app.stepNavigator ? app.stepNavigator.currentStep : null,
      selectedFrame: app.state ? app.state.selectedFrame : null
    };
  }

  /**
   * Test frame selection with specific parameters
   */
  async testFrameSelection(testName, breedId, costumeId, frameId) {
    console.log(`\n🧪 STARTING TEST: ${testName}`);
    console.log(`📋 Parameters: breed=${breedId}, costume=${costumeId}, frame=${frameId}`);
    
    this.startCapture();
    const startTime = performance.now();
    
    try {
      const app = window.portraitBuilderInstance;
      if (!app) {
        throw new Error('Portrait builder instance not found');
      }

      // Record initial state
      const initialState = this.getCanvasState();
      console.log('📊 Initial canvas state:', initialState);

      // Select breed if specified
      if (breedId && breedId !== 'none') {
        console.log(`🐕 Selecting breed: ${breedId}`);
        await app.selectBreed(breedId);
        await this.wait(500); // Wait for breed selection to complete
      }

      // Select costume if specified
      if (costumeId && costumeId !== 'none') {
        console.log(`👑 Selecting costume: ${costumeId}`);
        await app.selectCostume(costumeId);
        await this.wait(500); // Wait for costume selection to complete
      }

      // Record state before frame selection
      const preFrameState = this.getCanvasState();
      console.log('📊 Canvas state before frame selection:', preFrameState);

      // Select frame
      console.log(`🖼️ Selecting frame: ${frameId}`);
      await app.selectFrame(frameId);
      await this.wait(1000); // Wait for frame selection to complete

      // Record final state
      const finalState = this.getCanvasState();
      console.log('📊 Final canvas state:', finalState);

      const endTime = performance.now();
      const duration = endTime - startTime;

      const logs = this.stopCapture();
      
      const testResult = {
        testName,
        parameters: { breedId, costumeId, frameId },
        duration,
        initialState,
        preFrameState,
        finalState,
        logs,
        success: true,
        timestamp: new Date().toISOString()
      };

      this.testResults.push(testResult);
      console.log(`✅ TEST COMPLETED: ${testName} (${duration.toFixed(2)}ms)`);
      
      return testResult;

    } catch (error) {
      const logs = this.stopCapture();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      const testResult = {
        testName,
        parameters: { breedId, costumeId, frameId },
        duration,
        error: error.message,
        logs,
        success: false,
        timestamp: new Date().toISOString()
      };

      this.testResults.push(testResult);
      console.error(`❌ TEST FAILED: ${testName} - ${error.message}`);
      
      return testResult;
    }
  }

  /**
   * Test rapid frame selection
   */
  async testRapidFrameSelection() {
    console.log(`\n🧪 STARTING RAPID FRAME SELECTION TEST`);
    
    this.startCapture();
    const startTime = performance.now();
    
    try {
      const app = window.portraitBuilderInstance;
      if (!app) {
        throw new Error('Portrait builder instance not found');
      }

      const frames = ['classic', 'ornate', 'modern', 'classic'];
      
      for (let i = 0; i < frames.length; i++) {
        console.log(`🖼️ Rapid selection ${i + 1}: ${frames[i]}`);
        await app.selectFrame(frames[i]);
        await this.wait(100); // Very short wait for rapid selection
      }

      await this.wait(1000); // Wait for all operations to complete

      const endTime = performance.now();
      const duration = endTime - startTime;
      const logs = this.stopCapture();
      
      const testResult = {
        testName: 'Rapid Frame Selection',
        parameters: { frames },
        duration,
        logs,
        success: true,
        timestamp: new Date().toISOString()
      };

      this.testResults.push(testResult);
      console.log(`✅ RAPID TEST COMPLETED (${duration.toFixed(2)}ms)`);
      
      return testResult;

    } catch (error) {
      const logs = this.stopCapture();
      const testResult = {
        testName: 'Rapid Frame Selection',
        error: error.message,
        logs,
        success: false,
        timestamp: new Date().toISOString()
      };

      this.testResults.push(testResult);
      console.error(`❌ RAPID TEST FAILED: ${error.message}`);
      
      return testResult;
    }
  }

  /**
   * Generate test report
   */
  generateReport() {
    console.log('\n📊 TEST REPORT SUMMARY');
    console.log('='.repeat(50));
    
    const successful = this.testResults.filter(r => r.success).length;
    const failed = this.testResults.filter(r => !r.success).length;
    
    console.log(`Total tests: ${this.testResults.length}`);
    console.log(`Successful: ${successful}`);
    console.log(`Failed: ${failed}`);
    
    console.log('\n📋 DETAILED RESULTS:');
    this.testResults.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.testName}`);
      console.log(`   Status: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`   Duration: ${result.duration ? result.duration.toFixed(2) + 'ms' : 'N/A'}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    return this.testResults;
  }
}

/**
 * Run systematic frame selection tests
 */
async function runSystematicFrameTests() {
  const tester = new FrameSelectionTester();
  
  console.log('🚀 STARTING SYSTEMATIC FRAME SELECTION TESTS');
  console.log('=' .repeat(60));
  
  // Test scenarios from SPIKE.md
  const testScenarios = [
    // Basic frame selection with different breeds
    ['Golden Retriever + Classic Frame', 'golden-retriever', 'none', 'classic'],
    ['Labrador + Ornate Frame', 'labrador', 'none', 'ornate'],
    ['German Shepherd + Modern Frame', 'german-shepherd', 'none', 'modern'],
    
    // Frame selection with costumes
    ['Golden Retriever + Crown + Classic Frame', 'golden-retriever', 'royal-crown', 'classic'],
    ['Labrador + Armor + Ornate Frame', 'labrador', 'knight-armor', 'ornate'],
    
    // Edge cases
    ['No Breed + Classic Frame', 'none', 'none', 'classic'],
    ['Breed Only + No Frame', 'golden-retriever', 'none', 'none']
  ];
  
  // Run individual tests
  for (const [testName, breedId, costumeId, frameId] of testScenarios) {
    await tester.testFrameSelection(testName, breedId, costumeId, frameId);
    await tester.wait(2000); // Wait between tests
  }
  
  // Run rapid selection test
  await tester.testRapidFrameSelection();
  
  // Generate report
  const results = tester.generateReport();
  
  console.log('\n💾 COPY THE ABOVE OUTPUT TO: docs/canvas-clearing-execution-traces.txt');
  console.log('🔍 ANALYZE PATTERNS IN THE LOGS TO IDENTIFY CANVAS CLEARING TRIGGERS');
  
  return results;
}

// Make functions available globally
window.FrameSelectionTester = FrameSelectionTester;
window.runSystematicFrameTests = runSystematicFrameTests;

console.log('📋 Systematic Frame Testing Script Loaded');
console.log('🚀 Run: await runSystematicFrameTests()');
