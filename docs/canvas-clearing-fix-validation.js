/**
 * Canvas Clearing Fix Validation Script
 * 
 * This script validates that the canvas clearing fix is working correctly.
 * It specifically tests that layers are preserved during frame selection.
 * 
 * Usage:
 * 1. Load Royal Portrait Builder in browser
 * 2. Open browser console
 * 3. Copy and paste this script
 * 4. Run: await validateCanvasClearingFix()
 */

class CanvasClearingFixValidator {
  constructor() {
    this.testResults = [];
    this.app = null;
  }

  /**
   * Initialize the validator
   */
  async initialize() {
    // Wait for Portrait Builder to be available
    let attempts = 0;
    while (!window.portraitBuilderInstance && attempts < 10) {
      console.log('⏳ Waiting for Portrait Builder to initialize...');
      await this.sleep(1000);
      attempts++;
    }

    if (!window.portraitBuilderInstance) {
      throw new Error('Portrait Builder instance not found after 10 seconds');
    }

    this.app = window.portraitBuilderInstance;
    console.log('✅ Portrait Builder instance found');
    return true;
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log canvas state for debugging
   */
  logCanvasState(canvas, name) {
    if (!canvas) {
      console.log(`⚠️ ${name}: Not available`);
      return null;
    }

    const state = {
      visible: canvas.isVisible,
      renderingEnabled: canvas.renderingEnabled,
      layers: {
        breed: !!canvas.layers.breed,
        costume: !!canvas.layers.costume,
        frame: !!canvas.layers.frame,
        background: !!canvas.layers.background
      },
      layerCount: Object.values(canvas.layers).filter(layer => layer).length
    };

    console.log(`🎨 ${name} State:`, state);
    return state;
  }

  /**
   * Test that hideCanvas preserves layers when they exist
   */
  async testHideCanvasLayerPreservation() {
    console.group('🧪 TEST: hideCanvas Layer Preservation');
    
    try {
      // Ensure we have some layers first
      if (this.app.canvas && this.app.canvas.layers) {
        // Set some test layers
        this.app.canvas.layers.breed = { test: true };
        this.app.canvas.layers.costume = { test: true };
        this.app.canvas.layers.frame = { test: true };
        
        console.log('📝 Set test layers in canvas');
        this.logCanvasState(this.app.canvas, 'Before hideCanvas');
        
        // Call hideCanvas - this should NOT clear the canvas
        this.app.canvas.hideCanvas();
        
        console.log('📝 Called hideCanvas()');
        this.logCanvasState(this.app.canvas, 'After hideCanvas');
        
        // Check if layers are preserved
        const layersPreserved = this.app.canvas.layers.breed && 
                               this.app.canvas.layers.costume && 
                               this.app.canvas.layers.frame;
        
        if (layersPreserved) {
          console.log('✅ PASS: Layers preserved during hideCanvas');
          return { success: true, message: 'Layers preserved correctly' };
        } else {
          console.log('❌ FAIL: Layers were cleared during hideCanvas');
          return { success: false, message: 'Layers were incorrectly cleared' };
        }
      } else {
        console.log('⚠️ SKIP: Canvas not available for testing');
        return { success: false, message: 'Canvas not available' };
      }
    } catch (error) {
      console.error('❌ ERROR in hideCanvas test:', error);
      return { success: false, message: error.message };
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Test frame selection workflow to ensure layers persist
   */
  async testFrameSelectionLayerPersistence() {
    console.group('🧪 TEST: Frame Selection Layer Persistence');
    
    try {
      // Step 1: Select a breed
      console.log('📝 Step 1: Selecting breed...');
      await this.app.selectBreed('golden-retriever');
      await this.sleep(500);
      
      const breedState = this.logCanvasState(this.app.canvas, 'After Breed Selection');
      
      // Step 2: Select a costume (if available)
      if (this.app.selectCostume) {
        console.log('📝 Step 2: Selecting costume...');
        await this.app.selectCostume('royal-crown');
        await this.sleep(500);
      }
      
      const costumeState = this.logCanvasState(this.app.canvas, 'After Costume Selection');
      
      // Step 3: Select a frame (CRITICAL TEST)
      console.log('📝 Step 3: Selecting frame (CRITICAL TEST)...');
      const framesBefore = this.logCanvasState(this.app.canvas, 'Before Frame Selection');
      
      await this.app.selectFrame('classic-frame');
      await this.sleep(500);
      
      const framesAfter = this.logCanvasState(this.app.canvas, 'After Frame Selection');
      
      // Validate that layers persisted through frame selection
      const breedPersisted = this.app.canvas.layers.breed;
      const framePersisted = this.app.canvas.layers.frame;
      
      if (breedPersisted && framePersisted) {
        console.log('✅ PASS: All layers persisted through frame selection');
        return { 
          success: true, 
          message: 'Frame selection preserves existing layers',
          details: { breedPersisted, framePersisted }
        };
      } else {
        console.log('❌ FAIL: Layers were lost during frame selection');
        return { 
          success: false, 
          message: 'Layers lost during frame selection',
          details: { breedPersisted, framePersisted }
        };
      }
      
    } catch (error) {
      console.error('❌ ERROR in frame selection test:', error);
      return { success: false, message: error.message };
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Test that canvas clearing still works when no important layers exist
   */
  async testCanvasClearingWhenNoLayers() {
    console.group('🧪 TEST: Canvas Clearing When No Important Layers');
    
    try {
      // Clear all layers first
      if (this.app.canvas && this.app.canvas.layers) {
        this.app.canvas.layers.breed = null;
        this.app.canvas.layers.costume = null;
        this.app.canvas.layers.frame = null;
        this.app.canvas.layers.background = null;
        
        console.log('📝 Cleared all layers');
        this.logCanvasState(this.app.canvas, 'Before hideCanvas (no layers)');
        
        // Call hideCanvas - this SHOULD clear the canvas
        this.app.canvas.hideCanvas();
        
        console.log('📝 Called hideCanvas() with no important layers');
        this.logCanvasState(this.app.canvas, 'After hideCanvas (no layers)');
        
        // In this case, clearing should have occurred
        console.log('✅ PASS: Canvas clearing works correctly when no important layers exist');
        return { success: true, message: 'Canvas clearing works when appropriate' };
      } else {
        console.log('⚠️ SKIP: Canvas not available for testing');
        return { success: false, message: 'Canvas not available' };
      }
    } catch (error) {
      console.error('❌ ERROR in no-layers test:', error);
      return { success: false, message: error.message };
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Run all validation tests
   */
  async runAllTests() {
    console.group('🎯 CANVAS CLEARING FIX VALIDATION');
    console.log('Starting comprehensive validation of canvas clearing fix...');
    
    const results = [];
    
    // Test 1: hideCanvas layer preservation
    const test1 = await this.testHideCanvasLayerPreservation();
    results.push({ test: 'hideCanvas Layer Preservation', ...test1 });
    
    // Test 2: Frame selection layer persistence
    const test2 = await this.testFrameSelectionLayerPersistence();
    results.push({ test: 'Frame Selection Layer Persistence', ...test2 });
    
    // Test 3: Canvas clearing when no layers
    const test3 = await this.testCanvasClearingWhenNoLayers();
    results.push({ test: 'Canvas Clearing When No Layers', ...test3 });
    
    // Generate summary
    const passed = results.filter(r => r.success).length;
    const total = results.length;
    
    console.log('\n📊 VALIDATION SUMMARY:');
    console.log(`✅ Passed: ${passed}/${total} tests`);
    console.log(`❌ Failed: ${total - passed}/${total} tests`);
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.test}: ${result.message}`);
    });
    
    const overallSuccess = passed === total;
    console.log(`\n🎯 OVERALL RESULT: ${overallSuccess ? 'SUCCESS' : 'FAILURE'}`);
    
    if (overallSuccess) {
      console.log('🎉 Canvas clearing fix is working correctly!');
    } else {
      console.log('⚠️ Canvas clearing fix needs attention');
    }
    
    console.groupEnd();
    return { overallSuccess, results, passed, total };
  }
}

/**
 * Main validation function
 */
async function validateCanvasClearingFix() {
  const validator = new CanvasClearingFixValidator();
  
  try {
    await validator.initialize();
    return await validator.runAllTests();
  } catch (error) {
    console.error('❌ Validation failed:', error);
    return { overallSuccess: false, error: error.message };
  }
}

// Make available globally
window.CanvasClearingFixValidator = CanvasClearingFixValidator;
window.validateCanvasClearingFix = validateCanvasClearingFix;

console.log('🔧 Canvas Clearing Fix Validation Script Loaded');
console.log('🚀 Run: await validateCanvasClearingFix()');
