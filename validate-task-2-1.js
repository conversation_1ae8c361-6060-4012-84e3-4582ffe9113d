#!/usr/bin/env node

/**
 * Task 2.1 Validation Script
 * Validates that the Canvas State Protector implementation meets all acceptance criteria
 * Run with: node validate-task-2-1.js
 */

const fs = require('fs');
const path = require('path');

console.log('🛡️ Task 2.1 Validation: Canvas State Protector Class');
console.log('='.repeat(60));

const validationResults = {
    passed: 0,
    failed: 0,
    tests: []
};

function logTest(testName, passed, message) {
    const result = { testName, passed, message };
    validationResults.tests.push(result);
    
    if (passed) {
        validationResults.passed++;
        console.log(`✅ ${testName}: ${message}`);
    } else {
        validationResults.failed++;
        console.log(`❌ ${testName}: ${message}`);
    }
}

// Test 1: CanvasStateProtector class implemented
function testClassImplementation() {
    try {
        const protectorPath = path.join(__dirname, 'assets', 'canvas-state-protector.js');
        
        if (!fs.existsSync(protectorPath)) {
            logTest('Class Implementation', false, 'canvas-state-protector.js file not found');
            return;
        }
        
        const protectorCode = fs.readFileSync(protectorPath, 'utf8');
        
        // Check for class definition
        const hasClass = protectorCode.includes('class CanvasStateProtector');
        if (!hasClass) {
            logTest('Class Implementation', false, 'CanvasStateProtector class not found in file');
            return;
        }
        
        // Check for required methods
        const requiredMethods = [
            'constructor',
            'startMonitoring',
            'checkCanvasState',
            'hasImportantLayers',
            'isCanvasCleared',
            'backupCanvasState',
            'restoreCanvasState',
            'getPerformanceMetrics',
            'enable',
            'disable',
            'destroy'
        ];
        
        const missingMethods = requiredMethods.filter(method => 
            !protectorCode.includes(method)
        );
        
        if (missingMethods.length > 0) {
            logTest('Class Implementation', false, `Missing methods: ${missingMethods.join(', ')}`);
            return;
        }
        
        // Check file size (should be substantial implementation)
        const stats = fs.statSync(protectorPath);
        if (stats.size < 5000) {
            logTest('Class Implementation', false, 'Implementation appears incomplete (file too small)');
            return;
        }
        
        logTest('Class Implementation', true, 'CanvasStateProtector class properly implemented');
        
    } catch (error) {
        logTest('Class Implementation', false, `Error checking implementation: ${error.message}`);
    }
}

// Test 2: Integration with main Portrait Builder app
function testIntegration() {
    try {
        const mainAppPath = path.join(__dirname, 'assets', 'royal-portrait-complete.js');
        
        if (!fs.existsSync(mainAppPath)) {
            logTest('Integration', false, 'royal-portrait-complete.js file not found');
            return;
        }
        
        const mainAppCode = fs.readFileSync(mainAppPath, 'utf8');
        
        // Check for integration code
        const hasIntegration = mainAppCode.includes('CanvasStateProtector') &&
                              mainAppCode.includes('canvasProtector') &&
                              mainAppCode.includes('new window.CanvasStateProtector');
        
        if (!hasIntegration) {
            logTest('Integration', false, 'Canvas State Protector integration not found in main app');
            return;
        }
        
        // Check for both main canvas and progressive canvas protection
        const hasMainCanvasProtection = mainAppCode.includes('this.canvasProtector = new window.CanvasStateProtector(this.canvas');
        const hasProgressiveCanvasProtection = mainAppCode.includes('this.progressiveCanvasProtector = new window.CanvasStateProtector(this.progressiveCanvas');
        
        if (!hasMainCanvasProtection || !hasProgressiveCanvasProtection) {
            logTest('Integration', false, 'Missing protection for main canvas or progressive canvas');
            return;
        }
        
        logTest('Integration', true, 'Canvas State Protector properly integrated with main app');
        
    } catch (error) {
        logTest('Integration', false, `Error checking integration: ${error.message}`);
    }
}

// Test 3: Script loading in Liquid template
function testScriptLoading() {
    try {
        const templatePath = path.join(__dirname, 'sections', 'royal-portrait-builder.liquid');
        
        if (!fs.existsSync(templatePath)) {
            logTest('Script Loading', false, 'royal-portrait-builder.liquid template not found');
            return;
        }
        
        const templateCode = fs.readFileSync(templatePath, 'utf8');
        
        // Check for script tag
        const hasScriptTag = templateCode.includes("canvas-state-protector.js");
        
        if (!hasScriptTag) {
            logTest('Script Loading', false, 'canvas-state-protector.js script tag not found in template');
            return;
        }
        
        // Check loading order (should be before main script)
        const protectorIndex = templateCode.indexOf('canvas-state-protector.js');
        const mainScriptIndex = templateCode.indexOf('royal-portrait-complete.js');
        
        if (protectorIndex === -1 || mainScriptIndex === -1 || protectorIndex > mainScriptIndex) {
            logTest('Script Loading', false, 'Script loading order incorrect');
            return;
        }
        
        logTest('Script Loading', true, 'Canvas State Protector script properly loaded in template');
        
    } catch (error) {
        logTest('Script Loading', false, `Error checking script loading: ${error.message}`);
    }
}

// Test 4: Configuration parameters
function testConfiguration() {
    try {
        const protectorPath = path.join(__dirname, 'assets', 'canvas-state-protector.js');
        const protectorCode = fs.readFileSync(protectorPath, 'utf8');
        
        // Check for configurable intervals
        const hasMonitoringInterval = protectorCode.includes('options.interval || 100');
        const hasBackupInterval = protectorCode.includes('options.backupInterval || 500');
        
        if (!hasMonitoringInterval || !hasBackupInterval) {
            logTest('Configuration', false, 'Missing configurable monitoring or backup intervals');
            return;
        }
        
        // Check integration uses correct intervals
        const mainAppPath = path.join(__dirname, 'assets', 'royal-portrait-complete.js');
        const mainAppCode = fs.readFileSync(mainAppPath, 'utf8');
        
        const hasCorrectConfig = mainAppCode.includes('interval: 100') && 
                                mainAppCode.includes('backupInterval: 500');
        
        if (!hasCorrectConfig) {
            logTest('Configuration', false, 'Integration does not use correct intervals (100ms monitoring, 500ms backup)');
            return;
        }
        
        logTest('Configuration', true, 'Monitoring (100ms) and backup (500ms) intervals properly configured');
        
    } catch (error) {
        logTest('Configuration', false, `Error checking configuration: ${error.message}`);
    }
}

// Test 5: Performance monitoring
function testPerformanceMonitoring() {
    try {
        const protectorPath = path.join(__dirname, 'assets', 'canvas-state-protector.js');
        const protectorCode = fs.readFileSync(protectorPath, 'utf8');
        
        // Check for performance metrics
        const hasPerformanceMetrics = protectorCode.includes('performanceMetrics') &&
                                     protectorCode.includes('monitoringTime') &&
                                     protectorCode.includes('backupTime') &&
                                     protectorCode.includes('restorationTime') &&
                                     protectorCode.includes('getPerformanceMetrics');
        
        if (!hasPerformanceMetrics) {
            logTest('Performance Monitoring', false, 'Performance metrics collection not implemented');
            return;
        }
        
        // Check for timing measurements
        const hasTimingMeasurements = protectorCode.includes('performance.now()');
        
        if (!hasTimingMeasurements) {
            logTest('Performance Monitoring', false, 'Performance timing measurements not implemented');
            return;
        }
        
        logTest('Performance Monitoring', true, 'Performance metrics collection properly implemented');
        
    } catch (error) {
        logTest('Performance Monitoring', false, `Error checking performance monitoring: ${error.message}`);
    }
}

// Test 6: Console logging
function testConsoleLogging() {
    try {
        const protectorPath = path.join(__dirname, 'assets', 'canvas-state-protector.js');
        const protectorCode = fs.readFileSync(protectorPath, 'utf8');
        
        // Check for protection system logging
        const hasProtectionLogs = protectorCode.includes('🛡️') &&
                                 protectorCode.includes('💾') &&
                                 protectorCode.includes('🔄') &&
                                 protectorCode.includes('console.log');
        
        if (!hasProtectionLogs) {
            logTest('Console Logging', false, 'Protection system activity logging not implemented');
            return;
        }
        
        logTest('Console Logging', true, 'Console logging for protection system activity implemented');
        
    } catch (error) {
        logTest('Console Logging', false, `Error checking console logging: ${error.message}`);
    }
}

// Run all tests
function runAllTests() {
    console.log('Running validation tests...\n');
    
    testClassImplementation();
    testIntegration();
    testScriptLoading();
    testConfiguration();
    testPerformanceMonitoring();
    testConsoleLogging();
    
    // Generate report
    console.log('\n' + '='.repeat(60));
    console.log('📊 VALIDATION REPORT');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${validationResults.tests.length}`);
    console.log(`Passed: ${validationResults.passed}`);
    console.log(`Failed: ${validationResults.failed}`);
    console.log(`Success Rate: ${((validationResults.passed / validationResults.tests.length) * 100).toFixed(1)}%`);
    
    // Acceptance criteria summary
    console.log('\n📋 ACCEPTANCE CRITERIA STATUS:');
    const criteria = [
        'CanvasStateProtector class implemented',
        'Class integrated with main Portrait Builder app',
        'Protection system monitors canvas state every 100ms',
        'Canvas state is backed up every 500ms',
        'Automatic restoration works when clearing is detected',
        'Console logs show protection system activity'
    ];
    
    criteria.forEach((criterion, index) => {
        const test = validationResults.tests[index];
        const status = test && test.passed ? '✅' : '❌';
        console.log(`${status} ${criterion}`);
    });
    
    const allPassed = validationResults.failed === 0;
    console.log(`\n🎯 TASK 2.1 STATUS: ${allPassed ? '✅ COMPLETE' : '❌ NEEDS WORK'}`);
    
    if (allPassed) {
        console.log('\n🚀 Ready to proceed to Task 2.2: Optimize Protection Performance');
    } else {
        console.log('\n⚠️  Please address the failed tests before proceeding to Task 2.2');
    }
    
    return allPassed;
}

// Run the validation
if (require.main === module) {
    runAllTests();
}

module.exports = { runAllTests, validationResults };
