<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task 2.3: Integration Testing - Canvas State Protector</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        .acceptance-criteria {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .acceptance-criteria h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .criteria-list {
            list-style: none;
            padding: 0;
        }
        .criteria-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .criteria-list li:last-child {
            border-bottom: none;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
        }
        .status.pending { background: #f39c12; color: white; }
        .status.pass { background: #27ae60; color: white; }
        .status.fail { background: #e74c3c; color: white; }
        .test-controls {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .test-controls button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-controls button:hover {
            background: #2980b9;
        }
        .test-controls button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .log-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .canvas-container {
            display: flex;
            gap: 20px;
            margin: 15px 0;
        }
        .canvas-wrapper {
            text-align: center;
        }
        .canvas-wrapper canvas {
            border: 2px solid #3498db;
            border-radius: 4px;
            background: white;
        }
        .canvas-wrapper h4 {
            margin: 10px 0 5px 0;
            color: #2c3e50;
        }
        .metrics-display {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .metric-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #27ae60;
        }
        .metric-label {
            font-weight: bold;
            color: #2c3e50;
            font-size: 12px;
            text-transform: uppercase;
        }
        .metric-value {
            font-size: 18px;
            color: #27ae60;
            font-weight: bold;
        }
        .browser-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .browser-test-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        .browser-test-item h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .progress-bar {
            background: #ecf0f1;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-fill {
            background: #3498db;
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .workflow-step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .workflow-step.active {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .workflow-step.completed {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        .step-number {
            background: #6c757d;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .step-number.active {
            background: #2196f3;
        }
        .step-number.completed {
            background: #4caf50;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Task 2.3: Integration Testing</h1>
            <p>Canvas State Protector Integration with Royal Portrait Builder</p>
            <p><strong>Objective:</strong> Test the protection system with real user workflows and edge cases</p>
        </div>

        <!-- Acceptance Criteria -->
        <div class="acceptance-criteria">
            <h4>📋 Acceptance Criteria</h4>
            <ul class="criteria-list" id="acceptanceCriteria">
                <li><span class="status pending" id="criteria-1">PENDING</span> Full user workflow completes successfully with protection enabled</li>
                <li><span class="status pending" id="criteria-2">PENDING</span> Edge cases handled gracefully</li>
                <li><span class="status pending" id="criteria-3">PENDING</span> Protection works in all supported browsers</li>
                <li><span class="status pending" id="criteria-4">PENDING</span> No false positives (unnecessary restorations)</li>
                <li><span class="status pending" id="criteria-5">PENDING</span> User experience is not negatively impacted</li>
            </ul>
        </div>

        <!-- Test Controls -->
        <div class="test-controls">
            <button onclick="initializeIntegrationTest()" id="initBtn">Initialize Integration Test</button>
            <button onclick="runUserWorkflowTests()" id="workflowBtn" disabled>Run User Workflow Tests (5x)</button>
            <button onclick="runEdgeCaseTests()" id="edgeBtn" disabled>Run Edge Case Tests</button>
            <button onclick="runCrossBrowserTests()" id="browserBtn" disabled>Run Cross-Browser Tests</button>
            <button onclick="generateTestReport()" id="reportBtn" disabled>Generate Test Report</button>
        </div>

        <!-- Canvas Display -->
        <div class="canvas-container">
            <div class="canvas-wrapper">
                <h4>Main Canvas</h4>
                <canvas id="mainCanvas" width="300" height="300"></canvas>
            </div>
            <div class="canvas-wrapper">
                <h4>Progressive Canvas</h4>
                <canvas id="progressiveCanvas" width="300" height="300"></canvas>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="metrics-display" id="metricsDisplay" style="display: none;">
            <h4>🔍 Real-Time Performance Metrics</h4>
            <div class="metrics-grid" id="metricsGrid">
                <!-- Metrics will be populated dynamically -->
            </div>
        </div>

        <!-- Test Progress -->
        <div class="test-section">
            <h3>📊 Test Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
            </div>
            <p id="progressText">Ready to start integration testing...</p>
        </div>

        <!-- User Workflow Testing -->
        <div class="test-section">
            <h3>👤 User Workflow Testing</h3>
            <div id="workflowSteps">
                <div class="workflow-step" id="workflow-1">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span class="step-number">1</span>
                        <span>Breed Selection → Costume → Background → Frame</span>
                    </div>
                    <span class="status pending">PENDING</span>
                </div>
                <div class="workflow-step" id="workflow-2">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span class="step-number">2</span>
                        <span>Photo Upload → Costume → Background → Frame</span>
                    </div>
                    <span class="status pending">PENDING</span>
                </div>
                <div class="workflow-step" id="workflow-3">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span class="step-number">3</span>
                        <span>Rapid Frame Selection Test</span>
                    </div>
                    <span class="status pending">PENDING</span>
                </div>
                <div class="workflow-step" id="workflow-4">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span class="step-number">4</span>
                        <span>Complex Layer Interaction Test</span>
                    </div>
                    <span class="status pending">PENDING</span>
                </div>
                <div class="workflow-step" id="workflow-5">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span class="step-number">5</span>
                        <span>Performance Under Load Test</span>
                    </div>
                    <span class="status pending">PENDING</span>
                </div>
            </div>
        </div>

        <!-- Edge Case Testing -->
        <div class="test-section">
            <h3>⚠️ Edge Case Testing</h3>
            <div id="edgeCaseResults">
                <p>Edge case tests will appear here after initialization...</p>
            </div>
        </div>

        <!-- Cross-Browser Testing -->
        <div class="test-section">
            <h3>🌐 Cross-Browser Compatibility</h3>
            <div class="browser-test-grid" id="browserTestGrid">
                <div class="browser-test-item">
                    <h4>Chrome</h4>
                    <span class="status pending" id="chrome-status">PENDING</span>
                </div>
                <div class="browser-test-item">
                    <h4>Firefox</h4>
                    <span class="status pending" id="firefox-status">PENDING</span>
                </div>
                <div class="browser-test-item">
                    <h4>Safari</h4>
                    <span class="status pending" id="safari-status">PENDING</span>
                </div>
                <div class="browser-test-item">
                    <h4>Edge</h4>
                    <span class="status pending" id="edge-status">PENDING</span>
                </div>
            </div>
        </div>

        <!-- Log Output -->
        <div class="test-section">
            <h3>📝 Test Log</h3>
            <div class="log-output" id="logOutput">
                <div>🧪 Integration Testing Console - Ready</div>
                <div>📋 Task 2.3: Test the protection system with real user workflows and edge cases</div>
                <div>⏰ Waiting for test initialization...</div>
            </div>
        </div>
    </div>

    <!-- Load Canvas State Protector -->
    <script src="assets/canvas-state-protector.js"></script>
    
    <script>
        // Task 2.3: Integration Testing Implementation
        console.log('🧪 Task 2.3 Integration Testing page loaded');

        // Test state management
        let testState = {
            initialized: false,
            mainCanvas: null,
            progressiveCanvas: null,
            mainProtector: null,
            progressiveProtector: null,
            testResults: {
                userWorkflows: [],
                edgeCases: [],
                crossBrowser: {},
                performanceMetrics: {},
                falsePositives: 0,
                userExperienceIssues: []
            },
            currentTest: null,
            startTime: null
        };

        // Mock Portrait Canvas class for testing
        class MockPortraitCanvas {
            constructor(canvasElement) {
                this.canvas = canvasElement;
                this.ctx = canvasElement.getContext('2d');
                this.layers = {
                    background: null,
                    breed: null,
                    costume: null,
                    frame: null
                };
                this.isVisible = true;
                this.renderingEnabled = true;

                // Initialize with some test content
                this.drawTestContent();
            }

            drawTestContent() {
                this.ctx.fillStyle = '#f0f0f0';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.fillStyle = '#3498db';
                this.ctx.fillRect(50, 50, 200, 200);
                this.ctx.fillStyle = 'white';
                this.ctx.font = '16px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('Test Canvas', this.canvas.width/2, this.canvas.height/2);
            }

            // Mock layer methods
            addLayer(type, data) {
                this.layers[type] = data;
                this.renderLayers();
            }

            removeLayer(type) {
                this.layers[type] = null;
                this.renderLayers();
            }

            clearCanvas() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.layers = { background: null, breed: null, costume: null, frame: null };
            }

            renderLayers() {
                this.drawTestContent();
                // Simulate layer rendering
                Object.keys(this.layers).forEach((type, index) => {
                    if (this.layers[type]) {
                        this.ctx.fillStyle = `hsl(${index * 60}, 70%, 50%)`;
                        this.ctx.fillRect(20 + index * 20, 20 + index * 20, 50, 50);
                        this.ctx.fillStyle = 'white';
                        this.ctx.font = '10px Arial';
                        this.ctx.fillText(type.charAt(0).toUpperCase(), 45 + index * 20, 50 + index * 20);
                    }
                });
            }

            // Simulate frame selection
            selectFrame(frameData) {
                log(`🖼️ Selecting frame: ${frameData.name}`);
                this.addLayer('frame', frameData);
                return new Promise(resolve => setTimeout(resolve, 100));
            }

            // Simulate breed selection
            selectBreed(breedData) {
                log(`🐕 Selecting breed: ${breedData.name}`);
                this.addLayer('breed', breedData);
                return new Promise(resolve => setTimeout(resolve, 50));
            }

            // Simulate costume selection
            selectCostume(costumeData) {
                log(`👑 Selecting costume: ${costumeData.name}`);
                this.addLayer('costume', costumeData);
                return new Promise(resolve => setTimeout(resolve, 75));
            }

            // Simulate background selection
            selectBackground(backgroundData) {
                log(`🏰 Selecting background: ${backgroundData.name}`);
                this.addLayer('background', backgroundData);
                return new Promise(resolve => setTimeout(resolve, 60));
            }
        }

        // Logging utility
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');

            let prefix = '';
            switch(type) {
                case 'success': prefix = '✅'; break;
                case 'error': prefix = '❌'; break;
                case 'warning': prefix = '⚠️'; break;
                case 'info': prefix = 'ℹ️'; break;
                default: prefix = '📝'; break;
            }

            logEntry.innerHTML = `<span style="color: #7f8c8d;">[${timestamp}]</span> ${prefix} ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;

            console.log(`[Integration Test] ${message}`);
        }

        // Update progress
        function updateProgress(percentage, text) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }

        // Update acceptance criteria status
        function updateCriteriaStatus(criteriaId, status) {
            const element = document.getElementById(`criteria-${criteriaId}`);
            if (element) {
                element.className = `status ${status}`;
                element.textContent = status.toUpperCase();
            }
        }

        // Update workflow step status
        function updateWorkflowStatus(stepId, status) {
            const stepElement = document.getElementById(`workflow-${stepId}`);
            const statusElement = stepElement.querySelector('.status');
            const stepNumber = stepElement.querySelector('.step-number');

            statusElement.className = `status ${status}`;
            statusElement.textContent = status.toUpperCase();

            stepNumber.className = `step-number ${status === 'pass' ? 'completed' : status === 'fail' ? 'active' : ''}`;
            stepElement.className = `workflow-step ${status === 'pass' ? 'completed' : status === 'fail' ? 'active' : ''}`;
        }

        // Initialize integration test
        async function initializeIntegrationTest() {
            log('🚀 Initializing Task 2.3: Integration Testing...', 'info');
            updateProgress(10, 'Initializing test environment...');

            try {
                // Initialize canvas elements
                const mainCanvasElement = document.getElementById('mainCanvas');
                const progressiveCanvasElement = document.getElementById('progressiveCanvas');

                testState.mainCanvas = new MockPortraitCanvas(mainCanvasElement);
                testState.progressiveCanvas = new MockPortraitCanvas(progressiveCanvasElement);

                log('✅ Mock canvas instances created', 'success');
                updateProgress(30, 'Creating canvas protectors...');

                // Initialize Canvas State Protectors
                if (window.CanvasStateProtector) {
                    testState.mainProtector = new window.CanvasStateProtector(testState.mainCanvas, {
                        interval: 100,
                        backupInterval: 2000 // Using optimized interval from Task 2.2
                    });

                    testState.progressiveProtector = new window.CanvasStateProtector(testState.progressiveCanvas, {
                        interval: 100,
                        backupInterval: 2000
                    });

                    log('✅ Canvas State Protectors initialized with performance optimization', 'success');
                } else {
                    throw new Error('CanvasStateProtector not available');
                }

                updateProgress(60, 'Setting up test monitoring...');

                // Start performance monitoring
                startPerformanceMonitoring();

                updateProgress(80, 'Enabling test controls...');

                // Enable test buttons
                document.getElementById('workflowBtn').disabled = false;
                document.getElementById('edgeBtn').disabled = false;
                document.getElementById('browserBtn').disabled = false;
                document.getElementById('initBtn').disabled = true;

                testState.initialized = true;
                testState.startTime = Date.now();

                updateProgress(100, 'Integration test environment ready!');
                log('🎉 Integration testing environment successfully initialized', 'success');

                // Show metrics display
                document.getElementById('metricsDisplay').style.display = 'block';

            } catch (error) {
                log(`❌ Initialization failed: ${error.message}`, 'error');
                updateProgress(0, 'Initialization failed - check console for details');
            }
        }

        // Start performance monitoring
        function startPerformanceMonitoring() {
            setInterval(() => {
                if (testState.mainProtector && testState.progressiveProtector) {
                    const mainMetrics = testState.mainProtector.getPerformanceMetrics();
                    const progressiveMetrics = testState.progressiveProtector.getPerformanceMetrics();

                    updateMetricsDisplay(mainMetrics, progressiveMetrics);
                }
            }, 1000);
        }

        // Update metrics display
        function updateMetricsDisplay(mainMetrics, progressiveMetrics) {
            const metricsGrid = document.getElementById('metricsGrid');

            const avgMainMonitoring = mainMetrics.monitoringCount > 0 ?
                (mainMetrics.monitoringTime / mainMetrics.monitoringCount).toFixed(2) : '0.00';
            const avgProgressiveMonitoring = progressiveMetrics.monitoringCount > 0 ?
                (progressiveMetrics.monitoringTime / progressiveMetrics.monitoringCount).toFixed(2) : '0.00';

            const avgMainBackup = mainMetrics.backupCount > 0 ?
                (mainMetrics.backupTime / mainMetrics.backupCount).toFixed(2) : '0.00';
            const avgProgressiveBackup = progressiveMetrics.backupCount > 0 ?
                (progressiveMetrics.backupTime / progressiveMetrics.backupCount).toFixed(2) : '0.00';
            const avgMainRestoration = mainMetrics.restorationCount > 0 ?
                (mainMetrics.restorationTime / mainMetrics.restorationCount).toFixed(2) : '0.00';
            const avgProgressiveRestoration = progressiveMetrics.restorationCount > 0 ?
                (progressiveMetrics.restorationTime / progressiveMetrics.restorationCount).toFixed(2) : '0.00';

            metricsGrid.innerHTML = `
                <div class="metric-item">
                    <div class="metric-label">Main Canvas Monitoring</div>
                    <div class="metric-value">${avgMainMonitoring}ms</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Progressive Canvas Monitoring</div>
                    <div class="metric-value">${avgProgressiveMonitoring}ms</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Main Canvas Backup Time</div>
                    <div class="metric-value">${avgMainBackup}ms</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Progressive Canvas Backup Time</div>
                    <div class="metric-value">${avgProgressiveBackup}ms</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Main Canvas Restoration Time</div>
                    <div class="metric-value">${avgMainRestoration}ms</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Progressive Canvas Restoration Time</div>
                    <div class="metric-value">${avgProgressiveRestoration}ms</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Main Canvas Backups</div>
                    <div class="metric-value">${mainMetrics.backupCount}</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Progressive Canvas Backups</div>
                    <div class="metric-value">${progressiveMetrics.backupCount}</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Main Canvas Restorations</div>
                    <div class="metric-value">${mainMetrics.restorationCount}</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Progressive Canvas Restorations</div>
                    <div class="metric-value">${progressiveMetrics.restorationCount}</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Memory Usage (Main)</div>
                    <div class="metric-value">${(mainMetrics.memoryUsage / 1024 / 1024).toFixed(2)}MB</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Memory Usage (Progressive)</div>
                    <div class="metric-value">${(progressiveMetrics.memoryUsage / 1024 / 1024).toFixed(2)}MB</div>
                </div>
            `;
        }

        // User Workflow Testing
        async function runUserWorkflowTests() {
            if (!testState.initialized) {
                log('❌ Test environment not initialized', 'error');
                return;
            }

            log('🚀 Starting User Workflow Tests (5 complete workflows)...', 'info');
            updateProgress(0, 'Running user workflow tests...');

            const workflows = [
                { id: 1, name: 'Breed → Costume → Background → Frame', type: 'breed' },
                { id: 2, name: 'Photo Upload → Costume → Background → Frame', type: 'photo' },
                { id: 3, name: 'Rapid Frame Selection Test', type: 'rapid' },
                { id: 4, name: 'Complex Layer Interaction Test', type: 'complex' },
                { id: 5, name: 'Performance Under Load Test', type: 'load' }
            ];

            let completedWorkflows = 0;
            let failedWorkflows = 0;

            for (const workflow of workflows) {
                try {
                    log(`🔄 Running workflow ${workflow.id}: ${workflow.name}`, 'info');
                    updateWorkflowStatus(workflow.id, 'pending');

                    const startTime = performance.now();
                    let workflowResult = null;

                    switch (workflow.type) {
                        case 'breed':
                            workflowResult = await runBreedWorkflow();
                            break;
                        case 'photo':
                            workflowResult = await runPhotoWorkflow();
                            break;
                        case 'rapid':
                            workflowResult = await runRapidFrameSelectionTest();
                            break;
                        case 'complex':
                            workflowResult = await runComplexLayerTest();
                            break;
                        case 'load':
                            workflowResult = await runLoadTest();
                            break;
                    }

                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    if (workflowResult.success) {
                        updateWorkflowStatus(workflow.id, 'pass');
                        completedWorkflows++;
                        log(`✅ Workflow ${workflow.id} completed successfully in ${duration.toFixed(2)}ms`, 'success');
                    } else {
                        updateWorkflowStatus(workflow.id, 'fail');
                        failedWorkflows++;
                        log(`❌ Workflow ${workflow.id} failed: ${workflowResult.error}`, 'error');
                    }

                    testState.testResults.userWorkflows.push({
                        id: workflow.id,
                        name: workflow.name,
                        success: workflowResult.success,
                        duration: duration,
                        error: workflowResult.error || null
                    });

                    updateProgress((workflow.id / workflows.length) * 100,
                        `Completed workflow ${workflow.id}/${workflows.length}`);

                    // Brief pause between workflows
                    await new Promise(resolve => setTimeout(resolve, 500));

                } catch (error) {
                    updateWorkflowStatus(workflow.id, 'fail');
                    failedWorkflows++;
                    log(`❌ Workflow ${workflow.id} threw exception: ${error.message}`, 'error');
                }
            }

            // Update acceptance criteria
            if (completedWorkflows === workflows.length) {
                updateCriteriaStatus(1, 'pass');
                log(`🎉 All ${completedWorkflows} user workflows completed successfully!`, 'success');
            } else {
                updateCriteriaStatus(1, 'fail');
                log(`⚠️ ${failedWorkflows} out of ${workflows.length} workflows failed`, 'warning');
            }

            // Enable next test phase
            document.getElementById('reportBtn').disabled = false;
            updateProgress(100, `User workflow testing complete: ${completedWorkflows}/${workflows.length} passed`);
        }

        // Individual workflow implementations
        async function runBreedWorkflow() {
            try {
                // Step 1: Select breed
                await testState.mainCanvas.selectBreed({ name: 'Golden Retriever', id: 'golden-retriever' });
                await verifyCanvasIntegrity('after breed selection');

                // Step 2: Select costume
                await testState.mainCanvas.selectCostume({ name: 'Royal Crown', id: 'royal-crown' });
                await verifyCanvasIntegrity('after costume selection');

                // Step 3: Select background
                await testState.mainCanvas.selectBackground({ name: 'Castle', id: 'castle' });
                await verifyCanvasIntegrity('after background selection');

                // Step 4: Select frame
                await testState.mainCanvas.selectFrame({ name: 'Gold Frame', id: 'gold-frame' });
                await verifyCanvasIntegrity('after frame selection');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function runPhotoWorkflow() {
            try {
                // Step 1: Simulate photo upload
                log('📸 Simulating photo upload...', 'info');
                testState.mainCanvas.addLayer('photo', { name: 'User Photo', id: 'user-photo' });
                await verifyCanvasIntegrity('after photo upload');

                // Step 2: Select costume
                await testState.mainCanvas.selectCostume({ name: 'Knight Armor', id: 'knight-armor' });
                await verifyCanvasIntegrity('after costume selection');

                // Step 3: Select background
                await testState.mainCanvas.selectBackground({ name: 'Medieval Hall', id: 'medieval-hall' });
                await verifyCanvasIntegrity('after background selection');

                // Step 4: Select frame
                await testState.mainCanvas.selectFrame({ name: 'Silver Frame', id: 'silver-frame' });
                await verifyCanvasIntegrity('after frame selection');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function runRapidFrameSelectionTest() {
            try {
                log('⚡ Testing rapid frame selection...', 'info');

                // Set up initial layers
                await testState.mainCanvas.selectBreed({ name: 'Labrador', id: 'labrador' });
                await testState.mainCanvas.selectCostume({ name: 'Princess Dress', id: 'princess-dress' });

                // Rapid frame selection
                const frames = [
                    { name: 'Gold Frame', id: 'gold-frame' },
                    { name: 'Silver Frame', id: 'silver-frame' },
                    { name: 'Bronze Frame', id: 'bronze-frame' },
                    { name: 'Wooden Frame', id: 'wooden-frame' },
                    { name: 'Crystal Frame', id: 'crystal-frame' }
                ];

                for (const frame of frames) {
                    await testState.mainCanvas.selectFrame(frame);
                    await verifyCanvasIntegrity(`after selecting ${frame.name}`);
                    // Very short delay to simulate rapid clicking
                    await new Promise(resolve => setTimeout(resolve, 50));
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function runComplexLayerTest() {
            try {
                log('🎨 Testing complex layer interactions...', 'info');

                // Build up complex layer state
                await testState.mainCanvas.selectBreed({ name: 'German Shepherd', id: 'german-shepherd' });
                await testState.mainCanvas.selectCostume({ name: 'Royal Robe', id: 'royal-robe' });
                await testState.mainCanvas.selectBackground({ name: 'Throne Room', id: 'throne-room' });

                // Test layer removal and re-addition
                testState.mainCanvas.removeLayer('costume');
                await verifyCanvasIntegrity('after costume removal');

                await testState.mainCanvas.selectCostume({ name: 'Crown Jewels', id: 'crown-jewels' });
                await verifyCanvasIntegrity('after costume re-addition');

                // Test frame selection with complex state
                await testState.mainCanvas.selectFrame({ name: 'Ornate Frame', id: 'ornate-frame' });
                await verifyCanvasIntegrity('after frame selection with complex layers');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function runLoadTest() {
            try {
                log('🔥 Testing performance under load...', 'info');

                const startTime = performance.now();

                // Simulate high-frequency operations
                for (let i = 0; i < 20; i++) {
                    await testState.mainCanvas.selectBreed({ name: `Test Breed ${i}`, id: `test-breed-${i}` });
                    await testState.mainCanvas.selectFrame({ name: `Test Frame ${i}`, id: `test-frame-${i}` });

                    // Verify canvas integrity every 5 operations
                    if (i % 5 === 0) {
                        await verifyCanvasIntegrity(`load test iteration ${i}`);
                    }
                }

                const endTime = performance.now();
                const totalTime = endTime - startTime;

                log(`⚡ Load test completed in ${totalTime.toFixed(2)}ms (avg: ${(totalTime/20).toFixed(2)}ms per operation)`, 'info');

                // Verify final state
                await verifyCanvasIntegrity('after load test completion');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Canvas integrity verification
        async function verifyCanvasIntegrity(context) {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    try {
                        // Check if canvas has content
                        const imageData = testState.mainCanvas.ctx.getImageData(0, 0,
                            testState.mainCanvas.canvas.width, testState.mainCanvas.canvas.height);

                        let hasContent = false;
                        for (let i = 0; i < imageData.data.length; i += 4) {
                            if (imageData.data[i + 3] > 0) { // Check alpha channel
                                hasContent = true;
                                break;
                            }
                        }

                        if (!hasContent) {
                            throw new Error(`Canvas appears to be cleared ${context}`);
                        }

                        // Check protection system metrics
                        const metrics = testState.mainProtector.getPerformanceMetrics();
                        if (metrics.restorationCount > testState.testResults.falsePositives) {
                            log(`🛡️ Canvas restoration detected ${context} (total: ${metrics.restorationCount})`, 'warning');
                            testState.testResults.falsePositives = metrics.restorationCount;
                        }

                        resolve(true);
                    } catch (error) {
                        reject(error);
                    }
                }, 100); // Allow time for protection system to react
            });
        }

        // Edge Case Testing
        async function runEdgeCaseTests() {
            if (!testState.initialized) {
                log('❌ Test environment not initialized', 'error');
                return;
            }

            log('🚀 Starting Edge Case Testing...', 'info');
            updateProgress(0, 'Running edge case tests...');

            const edgeCases = [
                { name: 'Rapid Frame Selection', test: testRapidFrameSelection },
                { name: 'Browser Tab Switching', test: testTabSwitching },
                { name: 'Window Resizing', test: testWindowResizing },
                { name: 'Memory Pressure', test: testMemoryPressure },
                { name: 'Canvas Manipulation', test: testDirectCanvasManipulation },
                { name: 'Protection System Stress', test: testProtectionStress }
            ];

            let passedTests = 0;
            let failedTests = 0;

            const edgeCaseResults = document.getElementById('edgeCaseResults');
            edgeCaseResults.innerHTML = '<h4>Edge Case Test Results:</h4>';

            for (let i = 0; i < edgeCases.length; i++) {
                const edgeCase = edgeCases[i];
                try {
                    log(`🔄 Running edge case: ${edgeCase.name}`, 'info');

                    const startTime = performance.now();
                    const result = await edgeCase.test();
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    if (result.success) {
                        passedTests++;
                        log(`✅ Edge case "${edgeCase.name}" passed in ${duration.toFixed(2)}ms`, 'success');
                        edgeCaseResults.innerHTML += `<div style="color: green;">✅ ${edgeCase.name}: PASSED (${duration.toFixed(2)}ms)</div>`;
                    } else {
                        failedTests++;
                        log(`❌ Edge case "${edgeCase.name}" failed: ${result.error}`, 'error');
                        edgeCaseResults.innerHTML += `<div style="color: red;">❌ ${edgeCase.name}: FAILED - ${result.error}</div>`;
                    }

                    testState.testResults.edgeCases.push({
                        name: edgeCase.name,
                        success: result.success,
                        duration: duration,
                        error: result.error || null
                    });

                } catch (error) {
                    failedTests++;
                    log(`❌ Edge case "${edgeCase.name}" threw exception: ${error.message}`, 'error');
                    edgeCaseResults.innerHTML += `<div style="color: red;">❌ ${edgeCase.name}: EXCEPTION - ${error.message}</div>`;
                }

                updateProgress(((i + 1) / edgeCases.length) * 100,
                    `Edge case ${i + 1}/${edgeCases.length} completed`);

                // Brief pause between tests
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            // Update acceptance criteria
            if (passedTests === edgeCases.length) {
                updateCriteriaStatus(2, 'pass');
                log(`🎉 All ${passedTests} edge cases handled gracefully!`, 'success');
            } else {
                updateCriteriaStatus(2, 'fail');
                log(`⚠️ ${failedTests} out of ${edgeCases.length} edge cases failed`, 'warning');
            }

            updateProgress(100, `Edge case testing complete: ${passedTests}/${edgeCases.length} passed`);
        }

        // Individual edge case test implementations
        async function testRapidFrameSelection() {
            try {
                // Set up initial state
                await testState.mainCanvas.selectBreed({ name: 'Test Breed', id: 'test-breed' });

                // Rapid frame selection (simulate user clicking very fast)
                const frames = ['frame1', 'frame2', 'frame3', 'frame4', 'frame5'];
                const promises = frames.map(frameId =>
                    testState.mainCanvas.selectFrame({ name: frameId, id: frameId })
                );

                await Promise.all(promises);
                await verifyCanvasIntegrity('after rapid frame selection');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testTabSwitching() {
            try {
                // Set up canvas state
                await testState.mainCanvas.selectBreed({ name: 'Test Breed', id: 'test-breed' });
                await testState.mainCanvas.selectFrame({ name: 'Test Frame', id: 'test-frame' });

                // Simulate tab switching by triggering visibility change events
                document.dispatchEvent(new Event('visibilitychange'));
                window.dispatchEvent(new Event('blur'));

                // Wait a bit
                await new Promise(resolve => setTimeout(resolve, 500));

                // Simulate tab return
                window.dispatchEvent(new Event('focus'));
                document.dispatchEvent(new Event('visibilitychange'));

                await verifyCanvasIntegrity('after tab switching simulation');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testWindowResizing() {
            try {
                // Set up canvas state
                await testState.mainCanvas.selectBreed({ name: 'Test Breed', id: 'test-breed' });

                // Simulate window resize
                const originalWidth = window.innerWidth;
                const originalHeight = window.innerHeight;

                // Trigger resize events
                window.dispatchEvent(new Event('resize'));

                // Wait for any resize handlers
                await new Promise(resolve => setTimeout(resolve, 300));

                await verifyCanvasIntegrity('after window resize simulation');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testMemoryPressure() {
            try {
                // Create memory pressure by forcing many backups
                for (let i = 0; i < 10; i++) {
                    await testState.mainCanvas.selectBreed({ name: `Breed ${i}`, id: `breed-${i}` });
                    await testState.mainCanvas.selectFrame({ name: `Frame ${i}`, id: `frame-${i}` });

                    // Force backup by waiting for backup interval
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Check memory metrics
                const metrics = testState.mainProtector.getPerformanceMetrics();
                const memoryUsageMB = metrics.memoryUsage / (1024 * 1024);

                if (memoryUsageMB > 50) { // More than 50MB seems excessive
                    return { success: false, error: `High memory usage: ${memoryUsageMB.toFixed(2)}MB` };
                }

                await verifyCanvasIntegrity('after memory pressure test');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testDirectCanvasManipulation() {
            try {
                // Set up canvas state
                await testState.mainCanvas.selectBreed({ name: 'Test Breed', id: 'test-breed' });
                await testState.mainCanvas.selectFrame({ name: 'Test Frame', id: 'test-frame' });

                // Directly manipulate canvas (simulate external interference)
                testState.mainCanvas.ctx.clearRect(0, 0, 100, 100);

                // Wait for protection system to detect and restore
                await new Promise(resolve => setTimeout(resolve, 200));

                // Check if protection system restored the canvas
                const metrics = testState.mainProtector.getPerformanceMetrics();
                if (metrics.restorationCount === 0) {
                    return { success: false, error: 'Protection system did not detect canvas manipulation' };
                }

                await verifyCanvasIntegrity('after direct canvas manipulation');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testProtectionStress() {
            try {
                // Stress test the protection system
                const initialRestorations = testState.mainProtector.getPerformanceMetrics().restorationCount;

                // Repeatedly clear canvas to stress protection system
                for (let i = 0; i < 5; i++) {
                    testState.mainCanvas.clearCanvas();
                    await new Promise(resolve => setTimeout(resolve, 150)); // Wait for restoration
                }

                const finalRestorations = testState.mainProtector.getPerformanceMetrics().restorationCount;
                const restorations = finalRestorations - initialRestorations;

                if (restorations < 3) { // Should have detected at least some clearings
                    return { success: false, error: `Protection system only performed ${restorations} restorations` };
                }

                await verifyCanvasIntegrity('after protection stress test');

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Cross-Browser Testing
        async function runCrossBrowserTests() {
            if (!testState.initialized) {
                log('❌ Test environment not initialized', 'error');
                return;
            }

            log('🌐 Starting Cross-Browser Compatibility Testing...', 'info');
            updateProgress(0, 'Running cross-browser tests...');

            // Detect current browser
            const browserInfo = detectBrowser();
            log(`🔍 Detected browser: ${browserInfo.name} ${browserInfo.version}`, 'info');

            // Test browser-specific features
            const browserTests = [
                { name: 'Canvas API Support', test: testCanvasAPISupport },
                { name: 'Performance API Support', test: testPerformanceAPISupport },
                { name: 'Event Handling', test: testEventHandling },
                { name: 'Memory Management', test: testMemoryManagement },
                { name: 'Timer Precision', test: testTimerPrecision }
            ];

            let passedTests = 0;
            let failedTests = 0;

            for (const test of browserTests) {
                try {
                    const result = await test.test();
                    if (result.success) {
                        passedTests++;
                        log(`✅ Browser test "${test.name}" passed`, 'success');
                    } else {
                        failedTests++;
                        log(`❌ Browser test "${test.name}" failed: ${result.error}`, 'error');
                    }
                } catch (error) {
                    failedTests++;
                    log(`❌ Browser test "${test.name}" threw exception: ${error.message}`, 'error');
                }
            }

            // Update browser status
            const browserStatus = passedTests === browserTests.length ? 'pass' : 'fail';
            updateBrowserStatus(browserInfo.name.toLowerCase(), browserStatus);

            // Update acceptance criteria
            if (browserStatus === 'pass') {
                updateCriteriaStatus(3, 'pass');
                log(`🎉 All browser compatibility tests passed for ${browserInfo.name}!`, 'success');
            } else {
                updateCriteriaStatus(3, 'fail');
                log(`⚠️ Some browser compatibility issues detected in ${browserInfo.name}`, 'warning');
            }

            testState.testResults.crossBrowser[browserInfo.name] = {
                passed: passedTests,
                failed: failedTests,
                total: browserTests.length
            };

            updateProgress(100, `Cross-browser testing complete for ${browserInfo.name}`);
        }

        // Browser detection utility
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            let browserName = 'Unknown';
            let browserVersion = 'Unknown';

            if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
                browserName = 'Chrome';
                browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)?.[1] || 'Unknown';
            } else if (userAgent.includes('Firefox')) {
                browserName = 'Firefox';
                browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)?.[1] || 'Unknown';
            } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
                browserName = 'Safari';
                browserVersion = userAgent.match(/Version\/([0-9.]+)/)?.[1] || 'Unknown';
            } else if (userAgent.includes('Edg')) {
                browserName = 'Edge';
                browserVersion = userAgent.match(/Edg\/([0-9.]+)/)?.[1] || 'Unknown';
            }

            return { name: browserName, version: browserVersion };
        }

        // Update browser status in UI
        function updateBrowserStatus(browserName, status) {
            const statusElement = document.getElementById(`${browserName}-status`);
            if (statusElement) {
                statusElement.className = `status ${status}`;
                statusElement.textContent = status.toUpperCase();
            }
        }

        // Browser-specific test implementations
        async function testCanvasAPISupport() {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                if (!ctx) {
                    return { success: false, error: 'Canvas 2D context not supported' };
                }

                // Test basic canvas operations
                ctx.fillStyle = 'red';
                ctx.fillRect(0, 0, 10, 10);

                const imageData = ctx.getImageData(0, 0, 10, 10);
                if (!imageData || imageData.data.length === 0) {
                    return { success: false, error: 'Canvas ImageData not supported' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testPerformanceAPISupport() {
            try {
                if (typeof performance === 'undefined' || typeof performance.now !== 'function') {
                    return { success: false, error: 'Performance API not supported' };
                }

                const start = performance.now();
                await new Promise(resolve => setTimeout(resolve, 10));
                const end = performance.now();

                if (end <= start) {
                    return { success: false, error: 'Performance timing not working correctly' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testEventHandling() {
            try {
                let eventFired = false;

                const testHandler = () => { eventFired = true; };
                document.addEventListener('test-event', testHandler);

                document.dispatchEvent(new Event('test-event'));

                document.removeEventListener('test-event', testHandler);

                if (!eventFired) {
                    return { success: false, error: 'Event handling not working correctly' };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testMemoryManagement() {
            try {
                // Test if browser can handle memory allocation
                const testArray = new Array(1000).fill(0).map((_, i) => ({ id: i, data: 'test' }));

                if (testArray.length !== 1000) {
                    return { success: false, error: 'Memory allocation failed' };
                }

                // Clean up
                testArray.length = 0;

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testTimerPrecision() {
            try {
                const start = Date.now();
                await new Promise(resolve => setTimeout(resolve, 100));
                const end = Date.now();

                const elapsed = end - start;

                // Timer should be reasonably accurate (within 50ms tolerance)
                if (elapsed < 50 || elapsed > 200) {
                    return { success: false, error: `Timer precision poor: ${elapsed}ms for 100ms timeout` };
                }

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Generate comprehensive test report
        async function generateTestReport() {
            if (!testState.initialized) {
                log('❌ Test environment not initialized', 'error');
                return;
            }

            log('📊 Generating comprehensive test report...', 'info');
            updateProgress(0, 'Generating test report...');

            const endTime = Date.now();
            const totalTestTime = endTime - testState.startTime;

            // Collect final metrics
            const mainMetrics = testState.mainProtector.getPerformanceMetrics();
            const progressiveMetrics = testState.progressiveProtector.getPerformanceMetrics();

            // Evaluate acceptance criteria
            evaluateAcceptanceCriteria(mainMetrics, progressiveMetrics);

            // Generate report
            const report = {
                testSuite: 'Task 2.3: Integration Testing',
                timestamp: new Date().toISOString(),
                duration: totalTestTime,
                environment: {
                    browser: detectBrowser(),
                    userAgent: navigator.userAgent,
                    viewport: { width: window.innerWidth, height: window.innerHeight }
                },
                results: testState.testResults,
                performanceMetrics: {
                    main: mainMetrics,
                    progressive: progressiveMetrics
                },
                acceptanceCriteria: getAcceptanceCriteriaStatus(),
                summary: generateTestSummary()
            };

            // Display report
            displayTestReport(report);

            // Save report to console for copying
            console.log('📋 Complete Test Report:', JSON.stringify(report, null, 2));

            updateProgress(100, 'Test report generated successfully');
            log('✅ Integration testing complete! Report generated.', 'success');
        }

        // Evaluate acceptance criteria based on test results
        function evaluateAcceptanceCriteria(mainMetrics, progressiveMetrics) {
            // Criteria 4: No false positives (unnecessary restorations)
            const totalRestorations = mainMetrics.restorationCount + progressiveMetrics.restorationCount;
            if (totalRestorations <= 5) { // Allow some restorations from legitimate tests
                updateCriteriaStatus(4, 'pass');
            } else {
                updateCriteriaStatus(4, 'fail');
                testState.testResults.userExperienceIssues.push(`Too many restorations: ${totalRestorations}`);
            }

            // Criteria 5: User experience not negatively impacted
            const avgMainMonitoring = mainMetrics.monitoringCount > 0 ?
                mainMetrics.monitoringTime / mainMetrics.monitoringCount : 0;
            const avgProgressiveMonitoring = progressiveMetrics.monitoringCount > 0 ?
                progressiveMetrics.monitoringTime / progressiveMetrics.monitoringCount : 0;

            const maxMonitoringTime = Math.max(avgMainMonitoring, avgProgressiveMonitoring);

            if (maxMonitoringTime < 25 && testState.testResults.userExperienceIssues.length === 0) {
                updateCriteriaStatus(5, 'pass');
            } else {
                updateCriteriaStatus(5, 'fail');
                if (maxMonitoringTime >= 25) {
                    testState.testResults.userExperienceIssues.push(`High monitoring overhead: ${maxMonitoringTime.toFixed(2)}ms`);
                }
            }
        }

        // Get acceptance criteria status
        function getAcceptanceCriteriaStatus() {
            const criteria = {};
            for (let i = 1; i <= 5; i++) {
                const element = document.getElementById(`criteria-${i}`);
                criteria[i] = element ? element.textContent.toLowerCase() : 'pending';
            }
            return criteria;
        }

        // Generate test summary
        function generateTestSummary() {
            const userWorkflowsPassed = testState.testResults.userWorkflows.filter(w => w.success).length;
            const edgeCasesPassed = testState.testResults.edgeCases.filter(e => e.success).length;
            const browserTestsPassed = Object.values(testState.testResults.crossBrowser).reduce((sum, browser) => sum + browser.passed, 0);
            const browserTestsTotal = Object.values(testState.testResults.crossBrowser).reduce((sum, browser) => sum + browser.total, 0);

            return {
                userWorkflows: `${userWorkflowsPassed}/${testState.testResults.userWorkflows.length} passed`,
                edgeCases: `${edgeCasesPassed}/${testState.testResults.edgeCases.length} passed`,
                crossBrowser: `${browserTestsPassed}/${browserTestsTotal} passed`,
                falsePositives: testState.testResults.falsePositives,
                userExperienceIssues: testState.testResults.userExperienceIssues.length,
                overallStatus: determineOverallStatus()
            };
        }

        // Determine overall test status
        function determineOverallStatus() {
            const criteriaStatuses = getAcceptanceCriteriaStatus();
            const passedCriteria = Object.values(criteriaStatuses).filter(status => status === 'pass').length;
            const totalCriteria = Object.keys(criteriaStatuses).length;

            if (passedCriteria === totalCriteria) {
                return 'PASS';
            } else if (passedCriteria >= totalCriteria * 0.8) {
                return 'PARTIAL PASS';
            } else {
                return 'FAIL';
            }
        }

        // Display test report in UI
        function displayTestReport(report) {
            const logOutput = document.getElementById('logOutput');

            logOutput.innerHTML += `
                <div style="border-top: 2px solid #3498db; margin-top: 20px; padding-top: 15px;">
                    <div style="color: #3498db; font-weight: bold; font-size: 16px;">📊 INTEGRATION TEST REPORT</div>
                    <div style="margin: 10px 0;">
                        <strong>Overall Status:</strong> <span style="color: ${report.summary.overallStatus === 'PASS' ? '#27ae60' : report.summary.overallStatus === 'PARTIAL PASS' ? '#f39c12' : '#e74c3c'}">${report.summary.overallStatus}</span>
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>Test Duration:</strong> ${(report.duration / 1000).toFixed(2)} seconds
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>Browser:</strong> ${report.environment.browser.name} ${report.environment.browser.version}
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>User Workflows:</strong> ${report.summary.userWorkflows}
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>Edge Cases:</strong> ${report.summary.edgeCases}
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>Cross-Browser Tests:</strong> ${report.summary.crossBrowser}
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>False Positives:</strong> ${report.summary.falsePositives}
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>UX Issues:</strong> ${report.summary.userExperienceIssues}
                    </div>
                    <div style="margin: 15px 0; padding: 10px; background: #2c3e50; border-radius: 4px;">
                        <div style="color: #ecf0f1; font-weight: bold;">Performance Metrics:</div>
                        <div style="color: #bdc3c7;">Main Canvas Avg Monitoring: ${(report.performanceMetrics.main.monitoringCount > 0 ? report.performanceMetrics.main.monitoringTime / report.performanceMetrics.main.monitoringCount : 0).toFixed(2)}ms</div>
                        <div style="color: #bdc3c7;">Progressive Canvas Avg Monitoring: ${(report.performanceMetrics.progressive.monitoringCount > 0 ? report.performanceMetrics.progressive.monitoringTime / report.performanceMetrics.progressive.monitoringCount : 0).toFixed(2)}ms</div>
                        <div style="color: #bdc3c7;">Total Restorations: ${report.performanceMetrics.main.restorationCount + report.performanceMetrics.progressive.restorationCount}</div>
                        <div style="color: #bdc3c7;">Memory Usage: ${((report.performanceMetrics.main.memoryUsage + report.performanceMetrics.progressive.memoryUsage) / 1024 / 1024).toFixed(2)}MB</div>
                    </div>
                    <div style="margin: 15px 0; color: #7f8c8d; font-size: 12px;">
                        Complete report available in browser console (F12)
                    </div>
                </div>
            `;

            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 Task 2.3: Integration Testing page ready', 'info');
            log('📋 Click "Initialize Integration Test" to begin comprehensive testing', 'info');
        });
    </script>
</body>
</html>
