<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Instrumentation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .console-output {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Canvas Clearing Instrumentation Test</h1>
    
    <p>This page tests the canvas debugging instrumentation. Open the browser console to see the logs.</p>
    
    <canvas id="testCanvas" width="400" height="300"></canvas>
    
    <div>
        <button onclick="testClearRect()">Test clearRect()</button>
        <button onclick="testFillRect()">Test fillRect() (white)</button>
        <button onclick="testCustomClear()">Test Custom clearCanvas()</button>
        <button onclick="testFrameSelection()">Test Frame Selection</button>
        <button onclick="testSetFrameLayer()">Test setFrameLayer()</button>
        <button onclick="drawSomething()">Draw Something</button>
    </div>
    
    <div class="console-output" id="consoleOutput">
        Console output will appear here...
    </div>

    <!-- Load the instrumentation first -->
    <script src="assets/canvas-debugging-instrumentation.js"></script>
    
    <script>
        // Mock some global objects that the instrumentation expects
        window.RoyalPortraitBuilderApp = function() {};
        window.RoyalPortraitBuilderApp.prototype.selectFrame = async function(frameId) {
            console.log('Mock selectFrame called with:', frameId);
            return Promise.resolve();
        };
        window.RoyalPortraitBuilderApp.prototype.updateNavigationButtons = function() {
            console.log('Mock updateNavigationButtons called');
        };
        
        window.PortraitCanvas = function() {
            this.canvas = document.getElementById('testCanvas');
            this.layers = { breed: null, costume: null, frame: null, background: null };
            this.needsRedraw = false;
        };
        window.PortraitCanvas.prototype.clearCanvas = function() {
            const ctx = this.canvas.getContext('2d');
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        };
        window.PortraitCanvas.prototype.setFrameLayer = async function(frame, scale = 1.0) {
            console.log('Mock setFrameLayer called with:', frame, scale);
            this.layers.frame = frame ? { frame, scale } : null;
            this.needsRedraw = true;
            return Promise.resolve();
        };
        window.PortraitCanvas.prototype.loadImage = function(url) {
            console.log('Mock loadImage called with:', url);
            return Promise.resolve(new Image());
        };

        // Initialize instrumentation
        if (window.CanvasDebuggingInstrumentation) {
            console.log('🔧 Initializing canvas debugging instrumentation...');
            window.CanvasDebuggingInstrumentation.instrumentCanvasClearing();
            window.CanvasDebuggingInstrumentation.instrumentFrameSelection();
            window.CanvasDebuggingInstrumentation.instrumentNavigation();
            window.CanvasDebuggingInstrumentation.instrumentEventHandlers();
            console.log('✅ Canvas debugging instrumentation initialized');
        }

        // Test functions
        function testClearRect() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function testFillRect() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function testCustomClear() {
            const portraitCanvas = new window.PortraitCanvas();
            portraitCanvas.clearCanvas();
        }

        function testFrameSelection() {
            // Mock a frame object
            const mockFrame = {
                id: 'test-frame-1',
                name: 'Test Frame',
                style: 'classic',
                base_price: 25.00
            };

            // Create mock app instance
            const mockApp = new window.RoyalPortraitBuilderApp();
            mockApp.canvas = new window.PortraitCanvas();
            mockApp.progressiveCanvas = new window.PortraitCanvas();
            mockApp.state = { selectedFrame: null };
            mockApp.frameLibrary = {
                getFrameById: (id) => mockFrame
            };
            mockApp.isSelectionInProgress = false;

            // Mock additional methods
            mockApp.updateCanvasLayers = async function(options) {
                console.log('Mock updateCanvasLayers called with:', options);
                return Promise.resolve();
            };
            mockApp.updateFrameSelectionUI = function(frame) {
                console.log('Mock updateFrameSelectionUI called with:', frame);
            };
            mockApp.showFrameSelectionSuccess = function(frame) {
                console.log('Mock showFrameSelectionSuccess called with:', frame);
            };
            mockApp.updateAddToCartButton = function() {
                console.log('Mock updateAddToCartButton called');
            };
            mockApp.saveCurrentState = function() {
                console.log('Mock saveCurrentState called');
            };

            // Test frame selection
            mockApp.selectFrame('test-frame-1');
        }

        function testSetFrameLayer() {
            const portraitCanvas = new window.PortraitCanvas();
            const mockFrame = {
                id: 'test-frame-2',
                name: 'Test Frame 2',
                style: 'ornate'
            };

            portraitCanvas.setFrameLayer(mockFrame, 1.2);
        }

        function drawSomething() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // Draw a simple rectangle
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(50, 50, 100, 100);
            
            // Draw a circle
            ctx.fillStyle = '#4ecdc4';
            ctx.beginPath();
            ctx.arc(250, 100, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw some text
            ctx.fillStyle = '#45b7d1';
            ctx.font = '20px Arial';
            ctx.fillText('Test Canvas', 150, 200);
        }

        // Capture console output for display
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalGroup = console.group;
        const originalGroupEnd = console.groupEnd;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToOutput(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'warn' ? '#ff6b6b' : type === 'group' ? '#4ecdc4' : '#333';
            div.textContent = message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToOutput(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToOutput('⚠️ ' + args.join(' '), 'warn');
        };
        
        console.group = function(...args) {
            originalGroup.apply(console, args);
            addToOutput('📁 ' + args.join(' '), 'group');
        };
        
        console.groupEnd = function() {
            originalGroupEnd.apply(console);
            addToOutput('📁 [Group End]', 'group');
        };

        // Draw initial content
        drawSomething();
    </script>
</body>
</html>
