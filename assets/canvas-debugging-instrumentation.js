/**
 * Canvas Debugging Instrumentation
 * TEMPORARY: For debugging canvas clearing regression
 * TODO: Remove after canvas clearing issue is resolved
 */

console.log('🔧 Canvas Debugging Instrumentation Loading...');

/**
 * Instrument canvas clearing methods to trace when and why canvas is cleared
 */
function instrumentCanvasClearing() {
  console.log('🔧 Setting up canvas clearing instrumentation...');

  // Store original methods
  const originalClearRect = CanvasRenderingContext2D.prototype.clearRect;
  const originalFillRect = CanvasRenderingContext2D.prototype.fillRect;

  // Override clearRect method
  CanvasRenderingContext2D.prototype.clearRect = function (x, y, width, height) {
    console.group('🚨 CANVAS CLEARING DETECTED - clearRect');
    console.log('📍 Clear area:', { x, y, width, height });
    console.log('🎯 Canvas element:', this.canvas);
    console.log('📏 Canvas dimensions:', {
      width: this.canvas.width,
      height: this.canvas.height,
    });

    // Capture call stack
    console.log('📚 Call stack:');
    console.trace();

    // Check if this is a full canvas clear
    const isFullClear = x === 0 && y === 0 && width === this.canvas.width && height === this.canvas.height;

    if (isFullClear) {
      console.warn('⚠️ FULL CANVAS CLEAR DETECTED!');
    }

    console.groupEnd();

    // Call original method
    return originalClearRect.call(this, x, y, width, height);
  };

  // Override fillRect method (used for clearing with white background)
  CanvasRenderingContext2D.prototype.fillRect = function (x, y, width, height) {
    // Only log if this looks like a clearing operation (white fill covering full canvas)
    const isLikelyClear =
      (this.fillStyle === '#ffffff' || this.fillStyle === 'white') &&
      x === 0 &&
      y === 0 &&
      width === this.canvas.width &&
      height === this.canvas.height;

    if (isLikelyClear) {
      console.group('🚨 CANVAS CLEARING DETECTED - fillRect (white background)');
      console.log('📍 Fill area:', { x, y, width, height });
      console.log('🎨 Fill style:', this.fillStyle);
      console.log('🎯 Canvas element:', this.canvas);
      console.log('📏 Canvas dimensions:', {
        width: this.canvas.width,
        height: this.canvas.height,
      });

      // Capture call stack
      console.log('📚 Call stack:');
      console.trace();

      console.warn('⚠️ FULL CANVAS CLEAR WITH WHITE BACKGROUND DETECTED!');
      console.groupEnd();
    }

    // Call original method
    return originalFillRect.call(this, x, y, width, height);
  };

  // Instrument custom clearCanvas methods if they exist
  if (window.PortraitCanvas && window.PortraitCanvas.prototype.clearCanvas) {
    const originalClearCanvas = window.PortraitCanvas.prototype.clearCanvas;

    window.PortraitCanvas.prototype.clearCanvas = function () {
      console.group('🚨 CANVAS CLEARING DETECTED - PortraitCanvas.clearCanvas()');
      console.log('🎯 PortraitCanvas instance:', this);
      console.log('📏 Canvas dimensions:', {
        width: this.canvas.width,
        height: this.canvas.height,
      });
      console.log('🎨 Current layers:', this.layers);

      // Capture call stack
      console.log('📚 Call stack:');
      console.trace();

      console.warn('⚠️ PORTRAIT CANVAS CLEAR METHOD CALLED!');
      console.groupEnd();

      // Call original method
      return originalClearCanvas.call(this);
    };
  }

  // Instrument setFrameLayer method to track frame layer changes
  if (window.PortraitCanvas && window.PortraitCanvas.prototype.setFrameLayer) {
    const originalSetFrameLayer = window.PortraitCanvas.prototype.setFrameLayer;

    window.PortraitCanvas.prototype.setFrameLayer = async function (frame, scale = 1.0) {
      console.group('🖼️ FRAME LAYER UPDATE - setFrameLayer()');
      console.log('🎯 PortraitCanvas instance:', this);
      console.log('🖼️ Frame data:', frame);
      console.log('📏 Scale:', scale);
      console.log('🎨 Current layers before update:', this.layers);

      // Capture call stack
      console.log('📚 Call stack:');
      console.trace();

      // Call original method
      const result = await originalSetFrameLayer.call(this, frame, scale);

      console.log('🎨 Layers after frame update:', this.layers);
      console.log('🔄 Needs redraw:', this.needsRedraw);
      console.groupEnd();

      return result;
    };
  }

  // Instrument canvas show/hide methods
  if (window.PortraitCanvas && window.PortraitCanvas.prototype.showCanvas) {
    const originalShowCanvas = window.PortraitCanvas.prototype.showCanvas;

    window.PortraitCanvas.prototype.showCanvas = function () {
      console.group('👁️ CANVAS SHOW - showCanvas()');
      console.log('🎯 PortraitCanvas instance:', this);
      console.log('🎨 Canvas state before show:', {
        visible: !this.canvas.style.display || this.canvas.style.display !== 'none',
        renderingEnabled: this.renderingEnabled,
      });

      console.log('📚 Call stack:');
      console.trace();

      const result = originalShowCanvas.call(this);

      console.log('🎨 Canvas state after show:', {
        visible: !this.canvas.style.display || this.canvas.style.display !== 'none',
        renderingEnabled: this.renderingEnabled,
      });
      console.groupEnd();

      return result;
    };
  }

  if (window.PortraitCanvas && window.PortraitCanvas.prototype.hideCanvas) {
    const originalHideCanvas = window.PortraitCanvas.prototype.hideCanvas;

    window.PortraitCanvas.prototype.hideCanvas = function () {
      console.group('👁️ CANVAS HIDE - hideCanvas()');
      console.log('🎯 PortraitCanvas instance:', this);
      console.log('🎨 Canvas state before hide:', {
        visible: !this.canvas.style.display || this.canvas.style.display !== 'none',
        renderingEnabled: this.renderingEnabled,
        layers: this.layers,
      });

      console.log('📚 Call stack:');
      console.trace();

      const result = originalHideCanvas.call(this);

      console.log('🎨 Canvas state after hide:', {
        visible: !this.canvas.style.display || this.canvas.style.display !== 'none',
        renderingEnabled: this.renderingEnabled,
        layers: this.layers,
      });
      console.warn('⚠️ CANVAS HIDDEN - Check if layers were preserved!');
      console.groupEnd();

      return result;
    };
  }

  console.log('✅ Canvas clearing instrumentation setup complete');
}

/**
 * Instrument frame selection workflow to trace what happens during frame selection
 */
function instrumentFrameSelection() {
  console.log('🔧 Setting up frame selection instrumentation...');

  // Wait for RoyalPortraitBuilderApp to be available
  const checkForApp = () => {
    if (window.RoyalPortraitBuilderApp && window.RoyalPortraitBuilderApp.prototype.selectFrame) {
      const originalSelectFrame = window.RoyalPortraitBuilderApp.prototype.selectFrame;

      window.RoyalPortraitBuilderApp.prototype.selectFrame = async function (frameId) {
        const startTime = performance.now();

        console.group('🖼️ FRAME SELECTION STARTED');
        console.log('🆔 Frame ID:', frameId);
        console.log('⏰ Start time:', new Date().toISOString());

        // Log canvas state before selection
        if (this.canvas) {
          console.log('🎨 Canvas state BEFORE frame selection:');
          console.log('  - Canvas dimensions:', {
            width: this.canvas.canvas.width,
            height: this.canvas.canvas.height,
          });
          console.log('  - Current layers:', this.canvas.layers);
          console.log(
            '  - Canvas visible:',
            !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
          );
        }

        // Log progressive canvas state
        if (this.progressiveCanvas) {
          console.log('🎨 Progressive canvas state BEFORE frame selection:');
          console.log('  - Canvas dimensions:', {
            width: this.progressiveCanvas.canvas.width,
            height: this.progressiveCanvas.canvas.height,
          });
          console.log(
            '  - Canvas visible:',
            !this.progressiveCanvas.canvas.style.display || this.progressiveCanvas.canvas.style.display !== 'none'
          );
        }

        // Call original method
        const result = await originalSelectFrame.call(this, frameId);

        // Log canvas state after selection (with small delay to catch async operations)
        setTimeout(() => {
          console.log('🎨 Canvas state AFTER frame selection (100ms delay):');
          if (this.canvas) {
            console.log('  - Canvas dimensions:', {
              width: this.canvas.canvas.width,
              height: this.canvas.canvas.height,
            });
            console.log('  - Current layers:', this.canvas.layers);
            console.log(
              '  - Canvas visible:',
              !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
            );
          }

          if (this.progressiveCanvas) {
            console.log('  - Progressive canvas dimensions:', {
              width: this.progressiveCanvas.canvas.width,
              height: this.progressiveCanvas.canvas.height,
            });
            console.log(
              '  - Progressive canvas visible:',
              !this.progressiveCanvas.canvas.style.display || this.progressiveCanvas.canvas.style.display !== 'none'
            );
          }

          const endTime = performance.now();
          console.log('⏱️ Frame selection duration:', `${(endTime - startTime).toFixed(2)}ms`);
          console.groupEnd();
        }, 100);

        return result;
      };

      // Also instrument updateCanvasLayers method
      if (window.RoyalPortraitBuilderApp.prototype.updateCanvasLayers) {
        const originalUpdateCanvasLayers = window.RoyalPortraitBuilderApp.prototype.updateCanvasLayers;

        window.RoyalPortraitBuilderApp.prototype.updateCanvasLayers = async function (options = {}) {
          console.group('🎨 CANVAS LAYERS UPDATE STARTED');
          console.log('🔧 Update options:', options);
          console.log('⏰ Time:', new Date().toISOString());

          // Log canvas state before update
          if (this.canvas) {
            console.log('🎨 Main canvas state BEFORE layers update:');
            console.log('  - Current layers:', this.canvas.layers);
            console.log(
              '  - Canvas visible:',
              !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
            );
          }

          if (this.progressiveCanvas) {
            console.log('🎨 Progressive canvas state BEFORE layers update:');
            console.log(
              '  - Canvas visible:',
              !this.progressiveCanvas.canvas.style.display || this.progressiveCanvas.canvas.style.display !== 'none'
            );
          }

          // Call original method
          const result = await originalUpdateCanvasLayers.call(this, options);

          // Log canvas state after update
          setTimeout(() => {
            console.log('🎨 Canvas state AFTER layers update (50ms delay):');
            if (this.canvas) {
              console.log('  - Main canvas layers:', this.canvas.layers);
              console.log(
                '  - Main canvas visible:',
                !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
              );
            }

            if (this.progressiveCanvas) {
              console.log(
                '  - Progressive canvas visible:',
                !this.progressiveCanvas.canvas.style.display || this.progressiveCanvas.canvas.style.display !== 'none'
              );
            }

            console.groupEnd();
          }, 50);

          return result;
        };
      }

      console.log('✅ Frame selection instrumentation setup complete');
    } else {
      // Try again in 100ms
      setTimeout(checkForApp, 100);
    }
  };

  checkForApp();
}

/**
 * Instrument navigation updates that might trigger canvas clearing
 */
function instrumentNavigation() {
  console.log('🔧 Setting up navigation instrumentation...');

  // Wait for RoyalPortraitBuilderApp to be available
  const checkForApp = () => {
    if (window.RoyalPortraitBuilderApp && window.RoyalPortraitBuilderApp.prototype.updateNavigationButtons) {
      const originalUpdateNavigation = window.RoyalPortraitBuilderApp.prototype.updateNavigationButtons;

      window.RoyalPortraitBuilderApp.prototype.updateNavigationButtons = function () {
        console.group('🧭 NAVIGATION UPDATE');
        console.log('📍 Current step:', this.currentStep);
        console.log('⏰ Time:', new Date().toISOString());

        // Log canvas visibility before navigation update
        if (this.canvas) {
          console.log(
            '🎨 Canvas visibility before navigation update:',
            !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
          );
        }

        // Capture call stack
        console.log('📚 Call stack:');
        console.trace();

        // Call original method
        const result = originalUpdateNavigation.call(this);

        // Log canvas visibility after navigation update
        if (this.canvas) {
          console.log(
            '🎨 Canvas visibility after navigation update:',
            !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
          );
        }

        console.groupEnd();

        return result;
      };

      // Also instrument updateNavigationButtonsOnly method
      if (window.RoyalPortraitBuilderApp.prototype.updateNavigationButtonsOnly) {
        const originalUpdateNavigationOnly = window.RoyalPortraitBuilderApp.prototype.updateNavigationButtonsOnly;

        window.RoyalPortraitBuilderApp.prototype.updateNavigationButtonsOnly = function () {
          console.group('🧭 NAVIGATION UPDATE (BUTTONS ONLY)');
          console.log('📍 Current step:', this.currentStep);
          console.log('⏰ Time:', new Date().toISOString());
          console.log('📚 Call stack:');
          console.trace();

          const result = originalUpdateNavigationOnly.call(this);
          console.groupEnd();

          return result;
        };
      }

      // Instrument updateCanvasVisibility method
      if (window.RoyalPortraitBuilderApp.prototype.updateCanvasVisibility) {
        const originalUpdateCanvasVisibility = window.RoyalPortraitBuilderApp.prototype.updateCanvasVisibility;

        window.RoyalPortraitBuilderApp.prototype.updateCanvasVisibility = function () {
          console.group('👁️ CANVAS VISIBILITY UPDATE');
          console.log('📍 Current step:', this.currentStep);
          console.log('⏰ Time:', new Date().toISOString());

          // Log canvas state before visibility update
          if (this.canvas) {
            console.log('🎨 Main canvas state BEFORE visibility update:');
            console.log(
              '  - Visible:',
              !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
            );
            console.log('  - Rendering enabled:', this.canvas.renderingEnabled);
          }

          if (this.progressiveCanvas) {
            console.log('🎨 Progressive canvas state BEFORE visibility update:');
            console.log(
              '  - Visible:',
              !this.progressiveCanvas.canvas.style.display || this.progressiveCanvas.canvas.style.display !== 'none'
            );
            console.log('  - Rendering enabled:', this.progressiveCanvas.renderingEnabled);
          }

          console.log('📚 Call stack:');
          console.trace();

          // Call original method
          const result = originalUpdateCanvasVisibility.call(this);

          // Log canvas state after visibility update
          setTimeout(() => {
            console.log('🎨 Canvas state AFTER visibility update (50ms delay):');
            if (this.canvas) {
              console.log(
                '  - Main canvas visible:',
                !this.canvas.canvas.style.display || this.canvas.canvas.style.display !== 'none'
              );
              console.log('  - Main canvas rendering enabled:', this.canvas.renderingEnabled);
            }

            if (this.progressiveCanvas) {
              console.log(
                '  - Progressive canvas visible:',
                !this.progressiveCanvas.canvas.style.display || this.progressiveCanvas.canvas.style.display !== 'none'
              );
              console.log('  - Progressive canvas rendering enabled:', this.progressiveCanvas.renderingEnabled);
            }

            console.groupEnd();
          }, 50);

          return result;
        };
      }

      console.log('✅ Navigation instrumentation setup complete');
    } else {
      setTimeout(checkForApp, 100);
    }
  };

  checkForApp();
}

/**
 * Instrument DOM events within Portrait Builder that might affect canvas
 */
function instrumentEventHandlers() {
  console.log('🔧 Setting up event handler instrumentation...');

  // Override addEventListener to catch events within Portrait Builder
  const originalAddEventListener = EventTarget.prototype.addEventListener;

  EventTarget.prototype.addEventListener = function (type, listener, options) {
    // Only instrument events on Portrait Builder elements
    const isPortraitBuilderElement = this.closest && this.closest('#royal-portrait-builder');

    if (isPortraitBuilderElement && ['click', 'change', 'input'].includes(type)) {
      const wrappedListener = function (event) {
        console.group(`🎯 DOM EVENT: ${type.toUpperCase()}`);
        console.log('🎯 Target element:', event.target);
        console.log('⏰ Time:', new Date().toISOString());

        // Check canvas state before event handling
        const portraitBuilder = document.querySelector('#royal-portrait-builder');
        if (portraitBuilder && window.portraitBuilderInstance) {
          const canvas = window.portraitBuilderInstance.canvas;
          if (canvas) {
            console.log('🎨 Canvas state before event:', {
              visible: !canvas.canvas.style.display || canvas.canvas.style.display !== 'none',
              layers: canvas.layers,
            });
          }
        }

        // Call original listener
        const result = listener.call(this, event);

        // Check canvas state after event handling (with delay)
        setTimeout(() => {
          if (portraitBuilder && window.portraitBuilderInstance) {
            const canvas = window.portraitBuilderInstance.canvas;
            if (canvas) {
              console.log('🎨 Canvas state after event (50ms delay):', {
                visible: !canvas.canvas.style.display || canvas.canvas.style.display !== 'none',
                layers: canvas.layers,
              });
            }
          }
          console.groupEnd();
        }, 50);

        return result;
      };

      return originalAddEventListener.call(this, type, wrappedListener, options);
    }

    // Call original method for non-Portrait Builder events
    return originalAddEventListener.call(this, type, listener, options);
  };

  console.log('✅ Event handler instrumentation setup complete');
}

// Export functions for use in main application
window.CanvasDebuggingInstrumentation = {
  instrumentCanvasClearing,
  instrumentFrameSelection,
  instrumentNavigation,
  instrumentEventHandlers,
};

console.log('✅ Canvas Debugging Instrumentation Module Loaded');
