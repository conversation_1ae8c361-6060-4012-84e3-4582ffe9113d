/* Royal Portrait Builder Styles - Performance Optimized */

/* Critical CSS is loaded separately via royal-portrait-critical.css */
/* This file contains non-critical styles that can be loaded asynchronously */

.royal-portrait-builder {
  margin: 0 auto;
  /* Use CSS containment for better performance */
  contain: layout style;
}

/* Performance optimizations */
.royal-portrait-builder * {
  /* Optimize repaints */
  backface-visibility: hidden;
  /* Optimize transforms */
  transform-style: preserve-3d;
}

/* Lazy loading styles */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-load.loading {
  opacity: 0.5;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

.lazy-load.loaded {
  opacity: 1;
}

.lazy-load.error {
  opacity: 0.7;
  filter: grayscale(100%);
}

@keyframes loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* =============================================================================
   CANVAS CAROUSEL LAYOUT
   ============================================================================= */

.costume-canvas-carousel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
  width: 100%;
  min-height: 450px; /* Ensure minimum height for proper layout */
}

.canvas-carousel-container {
  position: relative;
  width: 350px !important; /* Force dimensions to prevent layout issues */
  height: 350px !important;
  min-width: 350px;
  min-height: 350px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  background: #f8f9fa;
  flex-shrink: 0; /* Prevent flex container from shrinking */
}

.costume-carousel-canvas {
  width: 100%;
  height: 100%;
  cursor: grab;
  touch-action: none; /* Prevent default touch behaviors */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: block;
  user-select: none; /* Prevent text selection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  /* Performance optimizations */
  will-change: transform, filter;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.costume-carousel-canvas:hover {
  filter: brightness(1.05) contrast(1.02);
  transform: scale(1.01);
}

.costume-carousel-canvas:active {
  cursor: grabbing;
}

/* =============================================================================
   TOUCH INTERACTION OVERLAY
   ============================================================================= */

.canvas-interaction-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  pointer-events: none;
}

.canvas-nav-zone {
  pointer-events: none; /* Disable by default to allow touch events through */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* Enable pointer events only on desktop for click zones */
@media (hover: hover) and (pointer: fine) {
  .canvas-nav-zone {
    pointer-events: all;
  }
}

.canvas-nav-zone:hover {
  opacity: 0.1;
}

.canvas-nav-zone--prev:hover {
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
}

.canvas-nav-zone--next:hover {
  background: linear-gradient(to left, rgba(0, 0, 0, 0.1), transparent);
}

/* =============================================================================
   LOADING INDICATOR
   ============================================================================= */

.canvas-loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e1e5e9;
  border-top: 2px solid #1a73e8;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* =============================================================================
   COSTUME INFO OVERLAY
   ============================================================================= */

.costume-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 20px;
  text-align: center;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.canvas-carousel-container:hover .costume-info-overlay {
  transform: translateY(0);
}

.costume-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.costume-price {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* =============================================================================
   COSTUME INDICATORS
   ============================================================================= */

.costume-indicators {
  display: flex;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.costume-indicator {
  width: 48px;
  height: 48px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: #f1f3f4;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0;
}

.costume-indicator:hover {
  background: #e8eaed;
  transform: scale(1.15);
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.costume-indicator.active {
  border-color: #1a73e8;
  background: #e8f0fe;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
}

.costume-indicator.active:hover {
  transform: scale(1.2);
  box-shadow: 0 6px 16px rgba(26, 115, 232, 0.4);
}

.indicator-icon {
  font-size: 20px;
  line-height: 1;
}

/* =============================================================================
   SWIPE HINT
   ============================================================================= */

.swipe-hint {
  text-align: center;
  color: #5f6368;
  font-size: 14px;
  opacity: 0.8;
}

.swipe-hint p {
  margin: 0;
}

/* =============================================================================
   CANVAS CAROUSEL RESPONSIVE DESIGN
   ============================================================================= */

/* Mobile optimizations */
@media (max-width: 480px) {
  .canvas-carousel-container {
    width: 300px;
    height: 300px;
  }

  .costume-indicators {
    gap: 8px;
  }

  .costume-indicator {
    width: 40px;
    height: 40px;
  }

  .indicator-icon {
    font-size: 16px;
  }
}

/* Desktop adaptations */
@media (min-width: 768px) {
  .canvas-carousel-container {
    width: 450px;
    height: 450px;
  }

  .costume-indicators {
    flex-direction: row;
    gap: 16px;
  }

  .costume-indicator {
    width: 56px;
    height: 56px;
  }

  .indicator-icon {
    font-size: 24px;
  }
}

/* Canvas carousel accessibility */
.costume-indicator:focus {
  outline: 2px solid #1a73e8;
  outline-offset: 2px;
}

.costume-carousel-canvas:focus {
  outline: 2px solid #1a73e8;
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .costume-carousel-canvas,
  .costume-indicator,
  .costume-info-overlay,
  .canvas-nav-zone {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }
}

/* =============================================================================
   UNIVERSAL COSTUME SELECTOR STYLES
   ============================================================================= */

.costume-selector {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

/* Costume Categories */
.costume-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(var(--color-border), 0.2);
}

.costume-category {
  background: rgba(var(--color-background), 1);
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 24px;
  padding: 10px 20px;
  font-size: 1.4rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.costume-category:hover {
  border-color: rgba(var(--color-button), 0.5);
  color: rgba(var(--color-foreground), 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.1);
}

.costume-category--active {
  background: rgb(var(--color-button));
  border-color: rgb(var(--color-button));
  color: rgba(var(--color-background), 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(var(--color-button), 0.3);
}

.costume-category--active:hover {
  background: rgba(var(--color-button), 0.9);
  transform: translateY(-3px);
}

.costume-category__count {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-left: 6px;
}

/* Costume Grid */
.costume-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

/* Live Preview Section */
.costume-preview {
  background: rgba(var(--color-background), 0.5);
  border: 1px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
}

.costume-preview h4 {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: rgba(var(--color-foreground), 0.9);
}

.costume-preview__info {
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.6);
  line-height: 1.4;
}

.costume-preview__info h5 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(var(--color-foreground), 0.9);
}

.costume-preview__info p {
  margin-bottom: 6px;
}

.costume-preview__price {
  font-weight: 600;
  color: rgb(var(--color-button));
  font-size: 1.4rem;
}

/* Enhanced Costume Options */
.costume-option {
  position: relative;
  background: rgba(var(--color-background), 1);
  border: 2px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(var(--color-shadow), 0.1);
}

/* No Costume Option */
.costume-option--none {
  border-style: dashed;
  border-color: rgba(var(--color-border), 0.4);
}

.costume-option__image--none {
  background: rgba(var(--color-foreground), 0.05);
  border: 2px dashed rgba(var(--color-border), 0.3);
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.costume-option__none-text {
  font-size: 1.2rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.5);
}

.costume-option:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(var(--color-shadow), 0.15);
  border-color: rgba(var(--color-button), 0.4);
  background: rgba(var(--color-button), 0.02);
}

.costume-option--selected {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(var(--color-button), 0.2);
}

.costume-option--selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, rgb(var(--color-button)), rgba(var(--color-button), 0.7));
  animation: selectedGlow 2s ease-in-out infinite alternate;
}

@keyframes selectedGlow {
  from { opacity: 0.7; }
  to { opacity: 1; }
}

.costume-option__image {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto 16px;
  border-radius: 50%;
  background: rgba(var(--color-foreground), 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.costume-option__image img {
  width: 80px;
  height: 80px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.costume-option__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-button), 0.9);
  color: rgba(var(--color-background), 1);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
}

.costume-option__try-on {
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}

.costume-option:hover .costume-option__image {
  transform: scale(1.1);
  background: rgba(var(--color-button), 0.1);
}

.costume-option:hover .costume-option__image img {
  transform: scale(1.1);
}

.costume-option:hover .costume-option__overlay {
  opacity: 1;
  transform: scale(1);
}

.costume-option__no-costume {
  width: 48px;
  height: 48px;
  color: rgba(var(--color-foreground), 0.4);
  transition: all 0.3s ease;
}

.costume-option:hover .costume-option__no-costume {
  color: rgba(var(--color-foreground), 0.6);
  transform: scale(1.1);
}

.costume-option__name {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(var(--color-foreground), 0.9);
  transition: color 0.3s ease;
}

.costume-option:hover .costume-option__name {
  color: rgb(var(--color-button));
}

.costume-option__description {
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.6);
  margin-bottom: 12px;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.costume-option:hover .costume-option__description {
  color: rgba(var(--color-foreground), 0.8);
}

.costume-option__price {
  font-size: 1.4rem;
  font-weight: 600;
  color: rgb(var(--color-button));
  padding: 8px 16px;
  background: rgba(var(--color-button), 0.1);
  border-radius: 20px;
  display: inline-block;
  transition: all 0.3s ease;
}

.costume-option:hover .costume-option__price {
  background: rgba(var(--color-button), 0.15);
  transform: scale(1.05);
}

/* Real-time preview overlay */
.costume-option__preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-button), 0.9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
}

.costume-option:hover .costume-option__preview-overlay {
  opacity: 1;
  transform: scale(1);
}

.costume-option__preview-text {
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
}

/* Enhanced hover states for dynamic try-on interface */
.costume-option--hovering {
  animation: costumeHoverPulse 0.6s ease-in-out infinite alternate;
}

.costume-option--hovering .costume-option__preview {
  animation: costumePreviewSpin 2s linear infinite;
}

.costume-option--hovering .costume-option__name {
  color: rgb(var(--color-button)) !important;
  text-shadow: 0 0 8px rgba(var(--color-button), 0.3);
}

.costume-option--hovering .costume-option__price {
  animation: costumePriceBounce 0.8s ease-in-out infinite alternate;
}

@keyframes costumeHoverPulse {
  from {
    box-shadow: 0 8px 24px rgba(var(--color-shadow), 0.15);
  }
  to {
    box-shadow: 0 12px 32px rgba(var(--color-button), 0.25);
  }
}

@keyframes costumePreviewSpin {
  from {
    transform: scale(1.1) rotate(0deg);
  }
  to {
    transform: scale(1.1) rotate(360deg);
  }
}

@keyframes costumePriceBounce {
  from {
    transform: scale(1.05);
  }
  to {
    transform: scale(1.1);
  }
}

/* Focus states for accessibility */
.costume-option:focus {
  outline: 2px solid rgb(var(--color-button));
  outline-offset: 2px;
}

.costume-option:focus .costume-option__preview-overlay {
  opacity: 1;
  transform: scale(1);
}

/* Mobile responsive */
@media screen and (max-width: 749px) {
  .costume-options {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
  }

  .costume-option {
    padding: 16px;
  }

  .costume-option__preview {
    width: 80px;
    height: 80px;
    margin-bottom: 12px;
  }

  .costume-option__preview img {
    width: 60px;
    height: 60px;
  }

  .costume-option__name {
    font-size: 1.4rem;
  }

  .costume-option__description {
    font-size: 1.2rem;
  }

  .costume-option__price {
    font-size: 1.3rem;
    padding: 6px 12px;
  }

  /* Reduce animation intensity on mobile */
  .costume-option--hovering .costume-option__preview {
    animation: none;
    transform: scale(1.05);
  }

  /* Mobile responsive for new costume selector */
  .costume-categories {
    gap: 8px;
    margin-bottom: 20px;
  }

  .costume-category {
    padding: 8px 16px;
    font-size: 1.3rem;
  }

  .costume-category__count {
    font-size: 1.1rem;
  }

  .costume-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }

  .costume-option__image {
    width: 80px;
    height: 80px;
    margin-bottom: 12px;
  }

  .costume-option__image img {
    width: 60px;
    height: 60px;
  }

  .costume-preview {
    padding: 16px;
  }

  .costume-preview h4 {
    font-size: 1.5rem;
  }

  .costume-preview__info {
    font-size: 1.2rem;
  }
}

/* =============================================================================
   STEP NAVIGATION STYLES
   ============================================================================= */

.royal-portrait-builder__steps {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.step-container {
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  position: relative;
  overflow: hidden;
}

.step-container[data-step-active="true"] {
  opacity: 1;
  transform: translateX(0);
  pointer-events: all;
}

.step-container--active {
  animation: stepSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes stepSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(var(--color-background), 0.8), rgba(var(--color-background), 1));
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  border-bottom: 1px solid rgba(var(--color-border), var(--alpha-border));
}

.step-title {
  font-size: 2.4rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: rgba(var(--color-foreground), 1);
}

.step-description {
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 0.7);
  margin-bottom: 1rem;
}

.step-content {
  padding: 2rem;
  min-height: 300px;
  background: rgba(var(--color-background), 1);
}

.step-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: rgba(var(--color-background), 0.5);
  border-top: 1px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  gap: 1rem;
}

@media screen and (max-width: 749px) {
  .step-navigation {
    flex-direction: column;
    gap: 1rem;
  }

  .step-navigation .button {
    width: 100%;
  }
}

/* =============================================================================
   MESSAGE STYLES
   ============================================================================= */

.step-requirement,
.step-message {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: var(--border-radius);
  animation: messageSlideIn 0.3s ease-out;
}

.step-requirement__message {
  color: rgba(var(--color-foreground), 0.6);
  font-size: 1.4rem;
  margin: 0;
}

.step-success {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.5rem;
  background: rgba(46, 125, 50, 0.1);
  border: 1px solid rgba(46, 125, 50, 0.3);
  border-radius: var(--border-radius);
  color: rgb(46, 125, 50);
  animation: successPulse 0.6s ease-out;
}

.step-success__icon {
  font-size: 1.6rem;
  font-weight: bold;
}

.step-success__text {
  font-size: 1.4rem;
  font-weight: 500;
}

.step-error {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.5rem;
  background: rgba(211, 47, 47, 0.1);
  border: 1px solid rgba(211, 47, 47, 0.3);
  border-radius: var(--border-radius);
  color: rgb(211, 47, 47);
  animation: errorShake 0.5s ease-out;
}

.step-error__icon {
  font-size: 1.6rem;
  font-weight: bold;
}

.step-error__text {
  font-size: 1.4rem;
  font-weight: 500;
}

.step-info {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.5rem;
  background: rgba(2, 136, 209, 0.1);
  border: 1px solid rgba(2, 136, 209, 0.3);
  border-radius: var(--border-radius);
  color: rgb(2, 136, 209);
  animation: infoSlide 0.4s ease-out;
}

.step-info__icon {
  font-size: 1.6rem;
  font-weight: bold;
}

.step-info__text {
  font-size: 1.4rem;
  font-weight: 500;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes successPulse {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@keyframes infoSlide {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* =============================================================================
   BUTTON ANIMATIONS
   ============================================================================= */

.step-navigation .button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.step-navigation .button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.15);
}

.step-navigation .button:not(:disabled):active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

.step-navigation .button:disabled,
.step-navigation .button.button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.button--primary {
  background: linear-gradient(135deg, rgb(var(--color-button)), rgb(var(--color-button-text)));
  position: relative;
}

.button--primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.button--primary:not(:disabled):hover::before {
  left: 100%;
}

.button--secondary {
  border: 2px solid rgba(var(--color-border), 0.3);
  background: transparent;
  transition: all 0.3s ease;
}

.button--secondary:not(:disabled):hover {
  border-color: rgba(var(--color-button), 0.5);
  background: rgba(var(--color-button), 0.05);
}

.button--tertiary {
  background: rgba(var(--color-foreground), 0.05);
  color: rgba(var(--color-foreground), 0.7);
  border: 1px solid rgba(var(--color-border), 0.2);
  transition: all 0.3s ease;
}

.button--tertiary:not(:disabled):hover {
  background: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 0.9);
}

/* =============================================================================
   PHOTO UPLOAD ANIMATIONS
   ============================================================================= */

.photo-upload__dropzone {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px dashed rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.photo-upload__dropzone:hover {
  border-color: rgba(var(--color-button), 0.5);
  background: rgba(var(--color-button), 0.02);
  transform: translateY(-2px);
}

.photo-upload__dropzone.dragover {
  border-color: rgba(var(--color-button), 0.8);
  background: rgba(var(--color-button), 0.05);
  transform: scale(1.02);
  animation: dragPulse 1s infinite;
}

.photo-upload__icon {
  margin-bottom: 1rem;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.photo-upload__dropzone:hover .photo-upload__icon {
  opacity: 1;
  transform: translateY(-2px);
}

.photo-upload__preview {
  animation: photoPreviewSlide 0.4s ease-out;
  position: relative;
}

.upload-preview-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.preview-option {
  text-align: center;
}

.preview-canvas {
  width: 100%;
  height: 200px;
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid rgba(var(--color-border), 0.2);
}

.preview-canvas:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.1);
}

.preview-breed-image,
.preview-photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.preview-canvas:hover .preview-breed-image,
.preview-canvas:hover .preview-photo-image {
  transform: scale(1.05);
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(var(--color-foreground), 0.5);
  font-size: 1.4rem;
  background: rgba(var(--color-background), 0.5);
}

.preview-label {
  margin-top: 1rem;
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.7);
  font-weight: 500;
}

@keyframes dragPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--color-button), 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(var(--color-button), 0);
  }
}

@keyframes photoPreviewSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =============================================================================
   POPULAR BREED CAROUSEL STYLES
   ============================================================================= */

.popular-breeds-section {
  margin-bottom: 3rem;
}

.popular-breeds__title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: rgba(var(--color-foreground), 1);
  text-align: center;
}

.popular-breeds-carousel {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
  background: rgba(var(--color-background), 0.5);
  border: 1px solid rgba(var(--color-border), 0.2);
}

.popular-breeds-carousel__container {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 1.5rem 0;
}

.popular-breed-item {
  flex: 0 0 25%; /* 4 items per view on desktop */
  padding: 0 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.popular-breed-item:hover {
  transform: translateY(-4px);
}

.popular-breed-item__image {
  width: 100%;
  height: 120px;
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
  border: 2px solid rgba(var(--color-border), 0.2);
  transition: all 0.3s ease;
}

.popular-breed-item:hover .popular-breed-item__image {
  border-color: rgba(var(--color-button), 0.4);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.15);
}

.popular-breed-item__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.popular-breed-item:hover .popular-breed-item__image img {
  transform: scale(1.05);
}

.popular-breed-item__info {
  text-align: center;
}

.popular-breed-item__name {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: rgba(var(--color-foreground), 1);
}

.popular-breed-item__badge {
  display: inline-block;
  background: linear-gradient(135deg, rgba(var(--color-button), 0.8), rgba(var(--color-button), 1));
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.popular-breed-item--selected {
  transform: translateY(-2px);
}

.popular-breed-item--selected .popular-breed-item__image {
  border-color: rgba(var(--color-button), 0.8);
  box-shadow: 0 6px 20px rgba(var(--color-button), 0.3);
}

.popular-breed-item--selected .popular-breed-item__image::after {
  content: '✓';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 2.4rem;
  height: 2.4rem;
  background: rgba(var(--color-button), 1);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  font-weight: bold;
  animation: checkmarkPop 0.3s ease-out;
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 4rem;
  height: 4rem;
  background: rgba(var(--color-background), 0.9);
  border: 1px solid rgba(var(--color-border), 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
  backdrop-filter: blur(4px);
}

.carousel-nav:hover:not(.carousel-nav--disabled) {
  background: rgba(var(--color-button), 1);
  color: white;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(var(--color-button), 0.3);
}

.carousel-nav--prev {
  left: 1rem;
}

.carousel-nav--next {
  right: 1rem;
}

.carousel-nav--disabled {
  opacity: 0.3;
  cursor: not-allowed;
  pointer-events: none;
}

.breed-picker__search-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(var(--color-border), 0.2);
}

/* Responsive carousel styles */
@media screen and (max-width: 1023px) {
  .popular-breed-item {
    flex: 0 0 33.333%; /* 3 items per view on tablet */
  }
}

@media screen and (max-width: 767px) {
  .popular-breed-item {
    flex: 0 0 50%; /* 2 items per view on mobile */
  }

  .popular-breed-item__image {
    height: 100px;
  }

  .popular-breed-item__name {
    font-size: 1.3rem;
  }

  .carousel-nav {
    width: 3.5rem;
    height: 3.5rem;
  }

  .carousel-nav--prev {
    left: 0.5rem;
  }

  .carousel-nav--next {
    right: 0.5rem;
  }
}

/* =============================================================================
   PREDICTIVE SEARCH STYLES
   ============================================================================= */

.breed-picker__search {
  position: relative;
  margin-bottom: 1.5rem;
}

.predictive-search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(var(--color-background), 1);
  border: 1px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  box-shadow: 0 8px 25px rgba(var(--color-shadow), 0.15);
  z-index: 10;
  max-height: 400px;
  overflow-y: auto;
  backdrop-filter: blur(8px);
}

.predictive-search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(var(--color-border), 0.2);
  background: rgba(var(--color-background), 0.8);
  font-size: 1.3rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 0.8);
}

.predictive-search-clear {
  background: none;
  border: none;
  color: rgba(var(--color-button), 1);
  font-size: 1.2rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.predictive-search-clear:hover {
  background: rgba(var(--color-button), 0.1);
  color: rgba(var(--color-button), 0.8);
}

.predictive-search-list {
  padding: 0.5rem 0;
}

.predictive-search-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.predictive-search-item:hover,
.predictive-search-item--selected {
  background: rgba(var(--color-button), 0.05);
  border-left-color: rgba(var(--color-button), 0.6);
}

.predictive-search-item--selected {
  background: rgba(var(--color-button), 0.08);
  border-left-color: rgba(var(--color-button), 1);
}

.predictive-search-item__image {
  width: 4rem;
  height: 4rem;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid rgba(var(--color-border), 0.2);
}

.predictive-search-item__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.predictive-search-item__info {
  flex: 1;
  min-width: 0;
}

.predictive-search-item__name {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
  color: rgba(var(--color-foreground), 1);
  line-height: 1.3;
}

.predictive-search-item__category {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.6);
  margin: 0;
  line-height: 1.2;
}

.search-highlight {
  background: rgba(var(--color-button), 0.2);
  color: rgba(var(--color-button), 1);
  font-weight: 600;
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
}

.predictive-search-no-results {
  padding: 2rem 1.5rem;
  text-align: center;
  color: rgba(var(--color-foreground), 0.6);
  font-size: 1.4rem;
}

.predictive-search-no-results p {
  margin: 0;
}

/* Search input enhancements */
.breed-picker__search-input {
  width: 100%;
  padding: 1.2rem 1.5rem;
  border: 2px solid rgba(var(--color-border), 0.8);
  border-radius: var(--border-radius);
  font-size: 1.4rem;
  transition: all 0.3s ease;
  background: rgba(var(--color-background), 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.breed-picker__search-input:focus {
  outline: none;
  border-color: rgba(var(--color-button), 0.6);
  box-shadow: 0 0 0 3px rgba(var(--color-button), 0.1);
}

.breed-picker__search-input::placeholder {
  color: rgba(var(--color-foreground), 0.5);
}

/* Responsive predictive search */
@media screen and (max-width: 767px) {
  .predictive-search-results {
    max-height: 300px;
  }

  .predictive-search-item {
    padding: 0.8rem 1rem;
    gap: 0.8rem;
  }

  .predictive-search-item__image {
    width: 3.5rem;
    height: 3.5rem;
  }

  .predictive-search-item__name {
    font-size: 1.3rem;
  }

  .predictive-search-item__category {
    font-size: 1.1rem;
  }

  .predictive-search-header {
    padding: 0.8rem 1rem;
    font-size: 1.2rem;
  }
}

/* =============================================================================
   BREED CATEGORY FILTER STYLES
   ============================================================================= */

.breed-categories-container,
.size-filters-container {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: rgba(var(--color-background), 0.5);
  border: 1px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
}

.breed-categories__title,
.size-filters__title {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: rgba(var(--color-foreground), 1);
}

.breed-categories__tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 0;
}

.category-tab {
  background: rgba(var(--color-background), 1);
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 2rem;
  padding: 0.8rem 1.5rem;
  font-size: 1.2rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.8);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  user-select: none;
}

.category-tab:hover {
  border-color: rgba(var(--color-button), 0.5);
  color: rgba(var(--color-foreground), 1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(var(--color-shadow), 0.1);
}

.category-tab--active {
  background: rgba(var(--color-button), 1);
  border-color: rgba(var(--color-button), 1);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-button), 0.3);
}

.category-tab--active:hover {
  background: rgba(var(--color-button), 0.9);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(var(--color-button), 0.4);
}

.size-filters__options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.size-filter {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.size-filter:hover {
  transform: translateX(2px);
}

.size-filter input[type="radio"] {
  margin-right: 0.8rem;
  width: 1.6rem;
  height: 1.6rem;
  accent-color: rgba(var(--color-button), 1);
  cursor: pointer;
}

.size-filter__label {
  font-size: 1.2rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.8);
  cursor: pointer;
  transition: color 0.2s ease;
}

.size-filter:hover .size-filter__label {
  color: rgba(var(--color-foreground), 1);
}

.size-filter input[type="radio"]:checked + .size-filter__label {
  color: rgba(var(--color-button), 1);
  font-weight: 600;
}

/* Focus states for accessibility */
.category-tab:focus {
  outline: 3px solid rgba(var(--color-button), 0.3);
  outline-offset: 2px;
}

.size-filter input[type="radio"]:focus {
  outline: 3px solid rgba(var(--color-button), 0.3);
  outline-offset: 2px;
}

/* Responsive category filter styles */
@media screen and (max-width: 767px) {
  .breed-categories-container,
  .size-filters-container {
    margin: 1rem 0;
    padding: 1rem;
  }

  .breed-categories__tabs {
    gap: 0.5rem;
  }

  .category-tab {
    padding: 0.6rem 1.2rem;
    font-size: 1.1rem;
  }

  .size-filters__options {
    flex-direction: column;
    gap: 0.8rem;
  }

  .size-filter__label {
    font-size: 1.1rem;
  }
}

@media screen and (max-width: 480px) {
  .breed-categories__tabs {
    flex-direction: column;
    align-items: stretch;
  }

  .category-tab {
    text-align: center;
    padding: 1rem;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .breed-categories-container,
  .size-filters-container {
    background: rgba(var(--color-background), 0.3);
    border-color: rgba(var(--color-border), 0.4);
  }

  .category-tab {
    background: rgba(var(--color-background), 0.8);
    border-color: rgba(var(--color-border), 0.5);
  }

  .category-tab:hover {
    background: rgba(var(--color-background), 1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .category-tab,
  .size-filter {
    transition: none;
  }

  .category-tab:hover,
  .category-tab--active {
    transform: none;
  }
}

/* =============================================================================
   BREED SELECTION ANIMATIONS
   ============================================================================= */

.breed-picker__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.breed-card {
  border: 2px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(var(--color-background), 1);
  position: relative;
  overflow: hidden;
}

.breed-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--color-button), 0.1), transparent);
  transition: left 0.5s;
}

.breed-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(var(--color-shadow), 0.15);
  border-color: rgba(var(--color-button), 0.3);
}

.breed-card:hover::before {
  left: 100%;
}

.breed-card--selected {
  border-color: rgba(var(--color-button), 0.8);
  background: rgba(var(--color-button), 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(var(--color-button), 0.2);
}

.breed-card--selected::after {
  content: '✓';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 2rem;
  height: 2rem;
  background: rgba(var(--color-button), 1);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  animation: checkmarkPop 0.3s ease-out;
}

@keyframes checkmarkPop {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* =============================================================================
   DYNAMIC PRICING STYLES
   ============================================================================= */

.royal-portrait-builder__pricing {
  background: rgba(var(--color-background), 0.8);
  border: 1px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
}

.royal-portrait-builder__price {
  font-size: 2.4rem;
  font-weight: 700;
  color: rgba(var(--color-button), 1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-block;
}

.royal-portrait-builder__price.price-changing {
  transform: scale(1.1);
  animation: priceChange 0.6s ease-out;
}

.royal-portrait-builder__price.price-increase {
  color: rgb(46, 125, 50);
  animation: priceIncrease 0.6s ease-out;
}

.royal-portrait-builder__price.price-decrease {
  color: rgb(211, 47, 47);
  animation: priceDecrease 0.6s ease-out;
}

/* Enhanced price change animations */
.royal-portrait-builder__price.price-major-change {
  animation: priceMajorChange 0.8s ease-out;
}

.royal-portrait-builder__price.price-moderate-change {
  animation: priceModerateChange 0.6s ease-out;
}

[data-current-price].price-changing,
[data-total-price].price-changing {
  transform: scale(1.05);
  animation: priceChange 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-current-price].price-increase,
[data-total-price].price-increase {
  color: rgb(46, 125, 50);
  animation: priceIncrease 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-current-price].price-decrease,
[data-total-price].price-decrease {
  color: rgb(211, 47, 47);
  animation: priceDecrease 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes priceChange {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); }
  100% { transform: scale(1); }
}

@keyframes priceIncrease {
  0% {
    transform: scale(1);
    color: rgba(var(--color-button), 1);
  }
  50% {
    transform: scale(1.1);
    color: rgb(46, 125, 50);
  }
  100% {
    transform: scale(1);
    color: rgba(var(--color-button), 1);
  }
}

@keyframes priceDecrease {
  0% {
    transform: scale(1);
    color: rgba(var(--color-button), 1);
  }
  50% {
    transform: scale(1.1);
    color: rgb(211, 47, 47);
  }
  100% {
    transform: scale(1);
    color: rgba(var(--color-button), 1);
  }
}

@keyframes priceMajorChange {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.15) rotate(1deg);
  }
  50% {
    transform: scale(1.2) rotate(-1deg);
  }
  75% {
    transform: scale(1.1) rotate(0.5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

@keyframes priceModerateChange {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
  100% {
    transform: scale(1);
  }
}

.price-breakdown {
  margin-top: 1.5rem;
}

.price-breakdown__header {
  margin-bottom: 1rem;
}

.price-breakdown__header h5 {
  font-size: 1.6rem;
  font-weight: 600;
  margin: 0;
  color: rgba(var(--color-foreground), 1);
}

.price-breakdown__items {
  margin-bottom: 1rem;
}

.price-breakdown__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(var(--color-border), 0.1);
}

.price-breakdown__item:last-child {
  border-bottom: none;
}

.price-breakdown__item--total {
  border-top: 2px solid rgba(var(--color-border), 0.3);
  padding-top: 1rem;
  margin-top: 1rem;
  font-weight: 600;
  font-size: 1.1em;
}

.price-breakdown__label {
  color: rgba(var(--color-foreground), 0.8);
  font-size: 1.4rem;
}

.price-breakdown__value {
  color: rgba(var(--color-foreground), 1);
  font-size: 1.4rem;
  font-weight: 500;
}

.price-breakdown__item--total .price-breakdown__label,
.price-breakdown__item--total .price-breakdown__value {
  color: rgba(var(--color-button), 1);
  font-weight: 600;
}

.price-breakdown__total {
  border-top: 1px solid rgba(var(--color-border), 0.2);
  padding-top: 1rem;
}

.price-breakdown__note {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(var(--color-border), 0.1);
}

.price-breakdown__note p {
  margin: 0;
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.6);
  font-style: italic;
}

.compact-price-breakdown {
  background: rgba(var(--color-background), 0.5);
  border: 1px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 1.5rem;
}

.compact-price-breakdown__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 1.3rem;
}

.compact-price-breakdown__total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0 0.5rem 0;
  border-top: 2px solid rgba(var(--color-border), 0.3);
  margin-top: 0.5rem;
  font-weight: 600;
  font-size: 1.5rem;
  color: rgba(var(--color-button), 1);
}

/* =============================================================================
   PRICE NOTIFICATIONS AND INDICATORS
   ============================================================================= */

.price-notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  pointer-events: none;
}

.price-notification {
  background: rgba(var(--color-background), 0.95);
  border: 1px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  padding: 1rem 1.5rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.15);
  backdrop-filter: blur(10px);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.price-notification--visible {
  transform: translateX(0);
  opacity: 1;
}

.price-notification--increase {
  border-left: 4px solid rgb(46, 125, 50);
}

.price-notification--decrease {
  border-left: 4px solid rgb(211, 47, 47);
}

.price-notification__content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.price-notification__change {
  font-weight: 600;
  font-size: 1.4rem;
}

.price-notification--increase .price-notification__change {
  color: rgb(46, 125, 50);
}

.price-notification--decrease .price-notification__change {
  color: rgb(211, 47, 47);
}

.price-notification__reason {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
}

/* Component highlighting for price changes */
.component-price-changed {
  position: relative;
  animation: componentHighlight 1.5s ease-out;
}

.component-price-increase {
  animation: componentPriceIncrease 1.5s ease-out;
}

.component-price-decrease {
  animation: componentPriceDecrease 1.5s ease-out;
}

@keyframes componentHighlight {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--color-button), 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(var(--color-button), 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--color-button), 0);
  }
}

@keyframes componentPriceIncrease {
  0% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(46, 125, 50, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0);
  }
}

@keyframes componentPriceDecrease {
  0% {
    box-shadow: 0 0 0 0 rgba(211, 47, 47, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(211, 47, 47, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(211, 47, 47, 0);
  }
}

/* Savings indicator */
.savings-indicator {
  background: linear-gradient(135deg, rgb(46, 125, 50), rgb(76, 175, 80));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  margin-top: 1rem;
  font-weight: 500;
  text-align: center;
  transform: translateY(-10px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.savings-indicator--visible {
  transform: translateY(0);
  opacity: 1;
}

/* Price preview for hover states */
.price-preview {
  position: absolute;
  top: -10px;
  right: -10px;
  background: rgba(var(--color-background), 0.95);
  border: 1px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  padding: 0.5rem 0.75rem;
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.2);
  backdrop-filter: blur(8px);
  z-index: 10;
  transform: scale(0.8) translateY(10px);
  opacity: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.price-preview--visible {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.price-preview__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  font-size: 1.1rem;
  white-space: nowrap;
}

.price-preview__change {
  font-weight: 600;
  font-size: 1.2rem;
}

.price-preview--increase .price-preview__change {
  color: rgb(46, 125, 50);
}

.price-preview--decrease .price-preview__change {
  color: rgb(211, 47, 47);
}

.price-preview__total {
  font-size: 1rem;
  color: rgba(var(--color-foreground), 0.8);
}

.selection-summary {
  animation: summarySlideIn 0.5s ease-out;
}

.selection-summary__title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: rgba(var(--color-foreground), 1);
}

.selection-summary__items {
  margin-bottom: 2rem;
}

.selection-summary__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(var(--color-border), 0.1);
}

.selection-summary__item:last-child {
  border-bottom: none;
}

.selection-summary__label {
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.7);
}

.selection-summary__value {
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

.selection-summary__pricing {
  margin-top: 2rem;
}

/* Responsive pricing styles */
@media screen and (max-width: 767px) {
  .royal-portrait-builder__price {
    font-size: 2rem;
  }

  .price-breakdown__item {
    padding: 0.6rem 0;
  }

  .price-breakdown__label,
  .price-breakdown__value {
    font-size: 1.3rem;
  }

  .compact-price-breakdown__item {
    font-size: 1.2rem;
  }

  .compact-price-breakdown__total {
    font-size: 1.4rem;
  }

  /* Mobile-optimized price notifications */
  .price-notifications {
    top: 10px;
    right: 10px;
    left: 10px;
  }

  .price-notification {
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
  }

  .price-notification__change {
    font-size: 1.2rem;
  }

  .price-notification__reason {
    font-size: 1.1rem;
  }

  /* Reduce animation intensity on mobile */
  .component-price-changed {
    animation-duration: 1s;
  }

  .savings-indicator {
    padding: 0.5rem 1rem;
    font-size: 1.3rem;
  }

  /* Mobile photo editor optimizations */
  .photo-editor-modal__content {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }

  .photo-editor-modal__header {
    padding: 12px 16px;
  }

  .photo-editor-modal__header h3 {
    font-size: 1.6rem;
  }

  .photo-editor-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .photo-editor-modal__tools {
    padding: 12px 16px;
  }

  .photo-editor-tool-group {
    margin-bottom: 12px;
  }

  .photo-editor-tool {
    width: 48px;
    height: 48px;
  }

  .photo-editor-slider-group {
    gap: 8px;
  }

  .photo-editor-slider-label {
    font-size: 1.2rem;
  }

  .photo-editor-slider-label span {
    min-width: 70px;
  }

  .photo-editor-slider {
    margin-left: 12px;
    height: 8px;
  }

  .photo-editor-slider::-webkit-slider-thumb {
    width: 24px;
    height: 24px;
  }

  .photo-editor-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
  }

  .photo-editor-modal__actions {
    padding: 16px;
    gap: 12px;
  }

  .photo-editor-modal__actions .button {
    padding: 16px 20px;
    font-size: 1.5rem;
  }

  /* Enhanced mobile camera controls */
  .camera-settings {
    gap: 8px;
  }

  .camera-setting-btn,
  .camera-action-btn {
    width: 48px;
    height: 48px;
    font-size: 2rem;
  }

  .camera-modal__controls {
    padding: 16px;
  }
}

/* =============================================================================
   MOBILE CONTEXT MENU AND ADVANCED TOUCH INTERACTIONS
   ============================================================================= */

.mobile-context-menu {
  background: rgba(var(--color-background), 0.95);
  border: 1px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  box-shadow: 0 8px 24px rgba(var(--color-shadow), 0.2);
  backdrop-filter: blur(12px);
  padding: 8px 0;
  min-width: 150px;
  z-index: 1000;
  animation: contextMenuSlideIn 0.2s ease-out;
}

.context-menu-item {
  padding: 12px 16px;
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 1);
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid rgba(var(--color-border), 0.1);
}

.context-menu-item:last-child {
  border-bottom: none;
}

.context-menu-item:hover,
.context-menu-item:active {
  background: rgba(var(--color-button), 0.1);
}

@keyframes contextMenuSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Mobile canvas controls */
.canvas-controls {
  position: sticky;
  bottom: 0;
  background: rgba(var(--color-background), 0.95);
  backdrop-filter: blur(8px);
  padding: 12px;
  border-top: 1px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.canvas-zoom-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 8px;
}

.zoom-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: rgba(var(--color-button), 1);
  color: white;
  border: none;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(var(--color-shadow), 0.2);
}

.zoom-button:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(var(--color-shadow), 0.3);
}

.zoom-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Swipe navigation indicators */
.swipe-indicator {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 80px;
  background: rgba(var(--color-button), 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  font-size: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 100;
}

.swipe-indicator--left {
  left: 10px;
}

.swipe-indicator--right {
  right: 10px;
}

.swipe-indicator.visible {
  opacity: 1;
}

/* Enhanced expandable sections for mobile */
.expandable-section {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.expandable-section.expanded {
  max-height: 500px;
}

.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: rgba(var(--color-background), 1);
  border: 1px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.expand-toggle:active {
  background: rgba(var(--color-button), 0.1);
  transform: scale(0.98);
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-toggle.expanded .expand-icon {
  transform: rotate(180deg);
}

/* =============================================================================
   MOBILE CAMERA STYLES
   ============================================================================= */

.mobile-photo-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 2rem;
}

.mobile-photo-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(var(--color-background), 1);
}

.mobile-photo-option:hover {
  border-color: rgba(var(--color-button), 0.5);
  background: rgba(var(--color-button), 0.02);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.1);
}

.mobile-photo-option__icon {
  margin-bottom: 1rem;
  color: rgba(var(--color-button), 0.8);
}

.mobile-photo-option h4 {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: rgba(var(--color-foreground), 1);
}

.mobile-photo-option p {
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.6);
  margin: 0;
}

.mobile-photo-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  position: relative;
}

.mobile-photo-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(var(--color-border), 0.3);
}

.mobile-photo-divider span {
  background: rgba(var(--color-background), 1);
  padding: 0 1rem;
  color: rgba(var(--color-foreground), 0.5);
  font-size: 1.2rem;
  font-weight: 500;
}

/* Camera Modal Styles */
.camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-modal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

.camera-modal__content {
  position: relative;
  width: 90vw;
  max-width: 500px;
  height: 80vh;
  background: rgba(var(--color-background), 1);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.camera-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(var(--color-border), 0.2);
  background: rgba(var(--color-background), 0.95);
}

.camera-modal__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

.camera-modal__close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: rgba(var(--color-foreground), 0.6);
}

.camera-modal__close:hover {
  background: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 1);
}

.camera-modal__viewport {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.camera-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 280px;
  height: 280px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
}

.camera-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.8);
  padding: 2rem;
  border-radius: var(--border-radius);
}

.camera-error p {
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.camera-modal__controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: rgba(var(--color-background), 0.95);
  border-top: 1px solid rgba(var(--color-border), 0.2);
}

.camera-settings {
  display: flex;
  gap: 12px;
  align-items: center;
}

.camera-setting-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: rgba(var(--color-button), 0.1);
  color: rgba(var(--color-button), 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.camera-setting-btn:hover,
.camera-setting-btn.active {
  background: rgba(var(--color-button), 0.2);
  transform: scale(1.05);
}

.camera-setting-btn.active {
  background: rgba(var(--color-button), 1);
  color: white;
}

.camera-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.camera-action-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: rgba(var(--color-button), 0.1);
  color: rgba(var(--color-button), 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.camera-action-btn:hover {
  background: rgba(var(--color-button), 0.2);
  transform: scale(1.05);
}

/* =============================================================================
   PHOTO EDITOR MODAL STYLES
   ============================================================================= */

.photo-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-editor-modal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(8px);
}

.photo-editor-modal__content {
  position: relative;
  width: 95vw;
  max-width: 800px;
  height: 90vh;
  background: rgba(var(--color-background), 1);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.photo-editor-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(var(--color-border), 0.3);
  background: rgba(var(--color-background), 1);
}

.photo-editor-modal__header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

.photo-editor-modal__header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.photo-editor-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(var(--color-button), 0.1);
  color: rgba(var(--color-button), 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.photo-editor-btn:hover {
  background: rgba(var(--color-button), 0.2);
  transform: scale(1.05);
}

.photo-editor-modal__viewport {
  flex: 1;
  position: relative;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.photo-editor-canvas {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: grab;
}

.photo-editor-canvas:active {
  cursor: grabbing;
}

.photo-editor-crop-overlay {
  position: absolute;
  border: 2px solid rgba(var(--color-button), 1);
  background: rgba(var(--color-button), 0.1);
  display: none;
  pointer-events: none;
}

.crop-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: rgba(var(--color-button), 1);
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: all;
}

.crop-handle--tl { top: -6px; left: -6px; cursor: nw-resize; }
.crop-handle--tr { top: -6px; right: -6px; cursor: ne-resize; }
.crop-handle--bl { bottom: -6px; left: -6px; cursor: sw-resize; }
.crop-handle--br { bottom: -6px; right: -6px; cursor: se-resize; }
.crop-handle--t { top: -6px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.crop-handle--r { right: -6px; top: 50%; transform: translateY(-50%); cursor: e-resize; }
.crop-handle--b { bottom: -6px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.crop-handle--l { left: -6px; top: 50%; transform: translateY(-50%); cursor: w-resize; }

.photo-editor-modal__tools {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(var(--color-border), 0.3);
  background: rgba(var(--color-background), 0.98);
}

.photo-editor-tool-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  justify-content: center;
}

.photo-editor-tool {
  width: 44px;
  height: 44px;
  border: 1px solid rgba(var(--color-border), 0.3);
  background: rgba(var(--color-background), 1);
  color: rgba(var(--color-foreground), 0.7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.photo-editor-tool:hover,
.photo-editor-tool.active {
  background: rgba(var(--color-button), 0.1);
  color: rgba(var(--color-button), 1);
  border-color: rgba(var(--color-button), 0.3);
  transform: translateY(-1px);
}

.photo-editor-tool.active {
  background: rgba(var(--color-button), 1);
  color: white;
  border-color: rgba(var(--color-button), 1);
}

.photo-editor-slider-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.photo-editor-slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.8);
}

.photo-editor-slider-label span {
  min-width: 80px;
  font-weight: 500;
}

.photo-editor-slider {
  flex: 1;
  margin-left: 16px;
  height: 6px;
  border-radius: 3px;
  background: rgba(var(--color-border), 0.3);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.photo-editor-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(var(--color-button), 1);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.photo-editor-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.photo-editor-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(var(--color-button), 1);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.photo-editor-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.photo-editor-modal__actions {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  padding: 20px;
  background: rgba(var(--color-background), 1);
  border-top: 1px solid rgba(var(--color-border), 0.3);
}

.photo-editor-modal__actions .button {
  flex: 1;
  padding: 14px 24px;
  font-size: 1.4rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.photo-editor-modal__actions .button--secondary {
  background: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 0.8);
  border: 1px solid rgba(var(--color-border), 0.3);
}

.photo-editor-modal__actions .button--secondary:hover {
  background: rgba(var(--color-foreground), 0.15);
  transform: translateY(-1px);
}

.photo-editor-modal__actions .button--primary {
  background: rgba(var(--color-button), 1);
  color: white;
  border: 1px solid rgba(var(--color-button), 1);
}

.photo-editor-modal__actions .button--primary:hover {
  background: rgba(var(--color-button), 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-button), 0.3);
}

.camera-capture-btn {
  width: 70px;
  height: 70px;
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  margin: 0 auto;
}

.camera-capture-btn__ring {
  width: 100%;
  height: 100%;
  border: 4px solid rgba(var(--color-button), 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.camera-capture-btn:hover .camera-capture-btn__ring {
  border-color: rgba(var(--color-button), 0.8);
  transform: scale(1.05);
}

.camera-capture-btn:active .camera-capture-btn__ring {
  transform: scale(0.95);
}

.camera-capture-btn__inner {
  width: 50px;
  height: 50px;
  background: rgba(var(--color-button), 1);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.camera-capture-btn:hover .camera-capture-btn__inner {
  background: rgba(var(--color-button), 0.8);
}

/* Mobile responsive adjustments */
@media screen and (max-width: 767px) {
  .mobile-photo-options {
    gap: 0.8rem;
    padding: 1.5rem;
  }

  .mobile-photo-option {
    padding: 1.5rem;
  }

  .mobile-photo-option h4 {
    font-size: 1.5rem;
  }

  .mobile-photo-option p {
    font-size: 1.2rem;
  }

  .camera-modal__content {
    width: 95vw;
    height: 85vh;
  }

  .camera-modal__header {
    padding: 1rem;
  }

  .camera-modal__header h3 {
    font-size: 1.6rem;
  }

  .camera-frame {
    width: 240px;
    height: 240px;
  }

  .camera-modal__controls {
    padding: 1.5rem;
  }

  .camera-capture-btn {
    width: 60px;
    height: 60px;
  }

  .camera-capture-btn__inner {
    width: 40px;
    height: 40px;
  }
}

/* =============================================================================
   FINAL REVIEW STYLES
   ============================================================================= */

.final-review {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
}

@media screen and (min-width: 990px) {
  .final-review {
    grid-template-columns: 1fr 1fr;
    align-items: start;
  }
}

.final-review__preview {
  text-align: center;
}

.final-review__details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.selection-summary {
  background: rgba(var(--color-background), 0.5);
  border: 1px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 2rem;
  animation: summarySlideIn 0.5s ease-out;
}

@keyframes summarySlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* =============================================================================
   KEYBOARD NAVIGATION & ACCESSIBILITY STYLES
   ============================================================================= */

/* Focus indicators */
*:focus {
  outline: 2px solid rgba(var(--color-button), 0.8);
  outline-offset: 2px;
  border-radius: 2px;
}

/* Enhanced focus for interactive elements */
.breed-card:focus,
.popular-breed-item:focus,
.predictive-search-item:focus,
[data-frame-id]:focus,
[data-size-id]:focus {
  outline: 3px solid rgba(var(--color-button), 1);
  outline-offset: 3px;
  box-shadow: 0 0 0 1px rgba(var(--color-button), 0.2);
}

/* Button focus states */
.button:focus {
  outline: 3px solid rgba(var(--color-button), 0.8);
  outline-offset: 2px;
  box-shadow: 0 0 0 1px rgba(var(--color-button), 0.3);
}

.button--secondary:focus {
  outline-color: rgba(var(--color-foreground), 0.6);
}

.button--tertiary:focus {
  outline-color: rgba(var(--color-foreground), 0.5);
}

/* Input focus states */
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid rgba(var(--color-button), 0.8);
  outline-offset: 1px;
  border-color: rgba(var(--color-button), 0.6);
  box-shadow: 0 0 0 3px rgba(var(--color-button), 0.1);
}

/* Carousel navigation focus */
.carousel-nav:focus {
  outline: 3px solid rgba(var(--color-button), 1);
  outline-offset: 2px;
  background: rgba(var(--color-button), 0.1);
}

/* Step navigation focus */
.step-navigation .button:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.15),
              0 0 0 3px rgba(var(--color-button), 0.3);
}

/* Camera modal focus */
.camera-modal__close:focus {
  outline: 2px solid rgba(var(--color-button), 0.8);
  outline-offset: 2px;
  background: rgba(var(--color-button), 0.1);
}

.camera-capture-btn:focus {
  outline: 3px solid rgba(var(--color-button), 1);
  outline-offset: 4px;
}

.camera-capture-btn:focus .camera-capture-btn__ring {
  border-color: rgba(var(--color-button), 1);
  box-shadow: 0 0 0 2px rgba(var(--color-button), 0.3);
}

/* Mobile photo options focus */
.mobile-photo-option:focus {
  outline: 3px solid rgba(var(--color-button), 1);
  outline-offset: 3px;
  border-color: rgba(var(--color-button), 0.8);
  background: rgba(var(--color-button), 0.05);
}

/* Skip focus ring for elements that handle their own focus styling */
.breed-card:focus,
.popular-breed-item:focus,
.predictive-search-item:focus {
  outline: none;
}

.breed-card:focus {
  border-color: rgba(var(--color-button), 1);
  box-shadow: 0 0 0 3px rgba(var(--color-button), 0.3);
  transform: translateY(-2px);
}

.popular-breed-item:focus {
  transform: translateY(-6px);
}

.popular-breed-item:focus .popular-breed-item__image {
  border-color: rgba(var(--color-button), 1);
  box-shadow: 0 0 0 3px rgba(var(--color-button), 0.3);
}

.predictive-search-item:focus {
  background: rgba(var(--color-button), 0.1);
  border-left-color: rgba(var(--color-button), 1);
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  *:focus {
    outline: 3px solid;
    outline-offset: 2px;
  }

  .breed-card:focus,
  .popular-breed-item:focus {
    border-width: 3px;
  }
}

/* Reduced motion support for focus animations */
@media (prefers-reduced-motion: reduce) {
  *:focus {
    transition: none;
  }

  .breed-card:focus,
  .popular-breed-item:focus,
  .step-navigation .button:focus {
    transform: none;
  }
}

/* Dark mode focus adjustments */
@media (prefers-color-scheme: dark) {
  *:focus {
    outline-color: rgba(255, 255, 255, 0.8);
  }

  input:focus,
  textarea:focus,
  select:focus {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  }
}

/* =============================================================================
   ENHANCED MOBILE RESPONSIVENESS
   ============================================================================= */

/* Enhanced touch-friendly interface improvements */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for mobile - enhanced sizes */
  .breed-card,
  .popular-breed-item,
  .button,
  .carousel-nav,
  .costume-option,
  .frame-option,
  .size-option {
    min-height: 48px;
    min-width: 48px;
    padding: 12px;
  }

  /* Larger touch targets for critical elements */
  .step-navigation .button {
    min-height: 56px;
    padding: 16px 24px;
    font-size: 1.6rem;
  }

  /* Enhanced spacing for touch-friendly layout */
  .breed-picker__grid {
    gap: 16px;
  }

  .popular-breeds-carousel {
    gap: 12px;
  }

  /* Remove hover effects on touch devices */
  .breed-card:hover,
  .popular-breed-item:hover,
  .button:hover,
  .carousel-nav:hover,
  .costume-option:hover,
  .frame-option:hover {
    transform: none;
    box-shadow: none;
  }

  /* Enhanced active states for touch feedback */
  .breed-card:active,
  .popular-breed-item:active {
    transform: scale(0.97);
    transition: transform 0.1s ease;
    background: rgba(var(--color-button), 0.1);
  }

  .button:active {
    transform: scale(0.94);
    transition: transform 0.1s ease;
  }

  .costume-option:active,
  .frame-option:active,
  .size-option:active {
    transform: scale(0.96);
    transition: transform 0.1s ease;
    box-shadow: 0 0 0 3px rgba(var(--color-button), 0.3);
  }

  /* Enhanced touch feedback for interactive elements */
  .touch-feedback {
    position: relative;
    overflow: hidden;
  }

  .touch-feedback::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
    z-index: 1;
  }

  .touch-feedback.touching::before {
    width: 100px;
    height: 100px;
  }
}

/* Mobile-first responsive breakpoints */
@media screen and (max-width: 480px) {
  /* Extra small mobile devices */
  .royal-portrait-builder {
    padding: 1rem;
  }

  .step-header {
    padding: 1.5rem 1rem;
  }

  .step-title {
    font-size: 2rem;
  }

  .step-description {
    font-size: 1.3rem;
  }

  .step-content {
    padding: 1.5rem 1rem;
  }

  .step-navigation {
    padding: 1.5rem 1rem;
    gap: 0.8rem;
  }

  /* Carousel adjustments */
  .popular-breed-item {
    flex: 0 0 100%; /* Single item on very small screens */
  }

  .popular-breed-item__image {
    height: 80px;
  }

  .popular-breed-item__name {
    font-size: 1.2rem;
  }

  /* Search improvements */
  .breed-picker__search-input {
    padding: 1rem;
    font-size: 1.6rem;
  }

  .predictive-search-results {
    max-height: 250px;
  }

  /* Pricing display */
  .royal-portrait-builder__price {
    font-size: 1.8rem;
  }

  .price-breakdown__item {
    padding: 0.5rem 0;
  }

  .price-breakdown__label,
  .price-breakdown__value {
    font-size: 1.2rem;
  }
}

@media screen and (max-width: 767px) {
  /* Enhanced mobile devices optimization */
  .royal-portrait-builder {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Enhanced step navigation improvements */
  .step-navigation {
    position: sticky;
    bottom: 0;
    background: rgba(var(--color-background), 0.98);
    backdrop-filter: blur(12px);
    border-top: 2px solid rgba(var(--color-border), 0.3);
    box-shadow: 0 -4px 16px rgba(var(--color-shadow), 0.15);
    z-index: 100;
    padding: 16px;
  }

  .step-navigation .button {
    padding: 16px 24px;
    font-size: 1.6rem;
    font-weight: 600;
    min-height: 56px;
    border-radius: 12px;
  }

  /* Enhanced breed grid mobile optimization */
  .breed-picker__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px;
  }

  .breed-card {
    padding: 16px;
    border-radius: 12px;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .breed-card__image {
    width: 60px;
    height: 60px;
    margin-bottom: 8px;
  }

  .breed-card__name {
    font-size: 1.3rem;
    font-weight: 600;
  }

  /* Enhanced photo upload mobile experience */
  .photo-upload__dropzone {
    padding: 24px 16px;
    border-radius: 12px;
    min-height: 200px;
  }

  .mobile-photo-option {
    padding: 20px;
    border-radius: 12px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    font-weight: 600;
  }

  .mobile-photo-option__icon {
    margin-right: 12px;
    font-size: 2rem;
  }

  /* Enhanced canvas mobile optimization */
  .canvas-carousel-container {
    width: calc(100vw - 32px) !important;
    max-width: 350px !important;
    height: calc(100vw - 32px) !important;
    max-height: 350px !important;
    margin: 16px auto;
  }

  .costume-carousel-canvas {
    border-radius: 12px;
    touch-action: pan-x pan-y pinch-zoom;
  }

  /* Mobile-specific costume, frame, and background selectors */
  .costume-selector__grid,
  .frame-selector__grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 16px;
  }

  .background-options {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 16px;
  }

  .costume-option,
  .frame-option,
  .background-option {
    aspect-ratio: 1;
    border-radius: 12px;
    padding: 12px;
    min-height: 80px;
  }

  /* Enhanced size selector for mobile */
  .size-selector__options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .size-option {
    padding: 20px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
  }

  .size-option__name {
    font-size: 1.4rem;
    font-weight: 600;
  }

  .size-option__price {
    font-size: 1.3rem;
    color: rgba(var(--color-button), 1);
    font-weight: 600;
  }

  .mobile-photo-option h4 {
    font-size: 1.4rem;
  }

  /* Frame and size selectors */
  .frame-selector__grid,
  .size-selector__options {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  /* Final review mobile layout */
  .final-review {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .final-review__preview {
    order: 1;
  }

  .final-review__details {
    order: 2;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  /* Tablet devices */
  .royal-portrait-builder {
    padding: 2rem;
  }

  .step-content {
    padding: 2.5rem;
  }

  /* Carousel tablet optimization */
  .popular-breed-item {
    flex: 0 0 33.333%;
  }

  /* Breed grid tablet layout */
  .breed-picker__grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  /* Frame, size, and background selectors */
  .frame-selector__grid,
  .size-selector__options {
    grid-template-columns: repeat(3, 1fr);
  }

  .background-options {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media screen and (min-width: 1024px) {
  /* Desktop devices */
  .royal-portrait-builder {
    max-width: 1200px;
    padding: 3rem;
  }

  .step-content {
    padding: 3rem;
  }

  /* Desktop carousel */
  .popular-breed-item {
    flex: 0 0 25%;
  }

  /* Desktop breed grid */
  .breed-picker__grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  /* Desktop frame, size, and background selectors */
  .frame-selector__grid,
  .size-selector__options {
    grid-template-columns: repeat(4, 1fr);
  }

  .background-options {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  /* Desktop final review layout */
  .final-review {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .breed-card,
  .popular-breed-item,
  .frame-selector__item,
  .size-selector__option,
  .background-option {
    border-width: 0.5px;
  }

  .step-navigation {
    border-top-width: 0.5px;
  }
}

/* Landscape mobile orientation */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .step-header {
    padding: 1rem 2rem;
  }

  .step-title {
    font-size: 1.8rem;
  }

  .step-description {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }

  .step-content {
    padding: 1.5rem 2rem;
  }

  .popular-breed-item__image {
    height: 80px;
  }

  .camera-modal__content {
    height: 90vh;
  }
}

/* Print styles */
@media print {
  .royal-portrait-builder {
    background: white;
    color: black;
  }

  .step-navigation,
  .carousel-nav,
  .camera-modal {
    display: none;
  }

  .step-container {
    page-break-inside: avoid;
  }
}

/* Enhanced performance optimizations for mobile */
@media screen and (max-width: 767px) {
  /* Optimize animations for mobile performance */
  .breed-card,
  .popular-breed-item,
  .button,
  .costume-option,
  .frame-option {
    transition-duration: 0.15s;
    transition-timing-function: ease-out;
  }

  /* Simplify shadows on mobile for better performance */
  .breed-card:hover,
  .popular-breed-item:hover,
  .costume-option:hover,
  .frame-option:hover {
    box-shadow: 0 2px 8px rgba(var(--color-shadow), 0.08);
  }

  /* Optimize backdrop filters for performance */
  .step-navigation {
    backdrop-filter: none;
    background: rgba(var(--color-background), 1);
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .breed-card,
    .popular-breed-item,
    .button,
    .costume-option,
    .frame-option,
    .step-container,
    .canvas-carousel-container {
      transition: none;
      animation: none;
    }

    .touch-feedback::before {
      transition: none;
    }
  }

  /* Optimize rendering performance */
  .costume-carousel-canvas,
  .portrait-canvas {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  /* Memory optimization for mobile */
  .breed-card__image,
  .costume-option__image,
  .frame-option__image {
    image-rendering: optimizeSpeed;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
  }

  /* Touch-specific accessibility improvements */
  .breed-card:focus,
  .popular-breed-item:focus,
  .button:focus,
  .costume-option:focus,
  .frame-option:focus {
    outline: 3px solid rgba(var(--color-button), 0.6);
    outline-offset: 2px;
  }

  /* Enhanced focus management for touch navigation */
  .step-container.active {
    scroll-margin-top: 20px;
  }

  /* Optimize scroll behavior */
  .royal-portrait-builder {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="search"],
  textarea {
    font-size: 16px !important;
  }

  /* Enhanced loading states for mobile */
  .loading-spinner {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }

  .skeleton-loader {
    animation-duration: 1.5s;
  }
}

/* =============================================================================
   CART INTEGRATION STYLES
   ============================================================================= */

/* Cart messages */
.cart-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  background: rgba(var(--color-background), 0.95);
  border-radius: var(--border-radius);
  box-shadow: 0 8px 32px rgba(var(--color-shadow), 0.2);
  backdrop-filter: blur(8px);
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.cart-message--visible {
  transform: translateX(0);
}

.cart-message--success {
  border-left: 4px solid #10b981;
}

.cart-message--error {
  border-left: 4px solid #ef4444;
}

.cart-message--info {
  border-left: 4px solid #3b82f6;
}

.cart-message__content {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
}

.cart-message__icon {
  font-size: 1.8rem;
  flex-shrink: 0;
}

.cart-message--success .cart-message__icon {
  color: #10b981;
}

.cart-message--error .cart-message__icon {
  color: #ef4444;
}

.cart-message--info .cart-message__icon {
  color: #3b82f6;
}

.cart-message__text {
  font-size: 1.4rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.9);
  line-height: 1.4;
}

/* Error containers */
.portrait-error-container {
  margin-bottom: 2rem;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.validation-error,
.cart-error {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--border-radius);
}

.validation-error__icon,
.cart-error__icon {
  font-size: 1.8rem;
  color: #ef4444;
  flex-shrink: 0;
}

.validation-error__message,
.cart-error__message {
  font-size: 1.4rem;
  font-weight: 500;
  color: #dc2626;
  flex: 1;
}

.cart-error__retry {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: calc(var(--border-radius) / 2);
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cart-error__retry:hover {
  background: #dc2626;
}

/* Loading button states */
.button--loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.button__spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.button--loading span:not(.button__spinner) {
  color: white;
  font-weight: 600;
  margin-left: 3rem;
}

/* Cart item styles for drawer */
.cart-item--portrait {
  border-left: 3px solid rgba(var(--color-button), 0.8);
  background: rgba(var(--color-button), 0.02);
}

.cart-item__image .portrait-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(var(--color-button), 0.1), rgba(var(--color-button), 0.05));
  border: 2px solid rgba(var(--color-button), 0.2);
  border-radius: var(--border-radius);
  text-align: center;
}

.portrait-thumbnail__icon {
  font-size: 2.4rem;
  margin-bottom: 0.2rem;
}

.portrait-thumbnail__label {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(var(--color-button), 0.8);
  line-height: 1;
}

.cart-item__properties {
  margin: 1rem 0;
}

.cart-item__properties p {
  margin: 0.3rem 0;
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
}

.cart-item__properties strong {
  color: rgba(var(--color-foreground), 0.9);
  font-weight: 600;
}

.cart-item__price {
  font-size: 1.6rem;
  font-weight: 700;
  color: rgba(var(--color-button), 1);
}

/* Mobile cart message adjustments */
@media screen and (max-width: 767px) {
  .cart-message {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .cart-message__content {
    padding: 1.2rem;
  }

  .cart-message__text {
    font-size: 1.3rem;
  }

  .portrait-error-container {
    margin-bottom: 1.5rem;
  }

  .validation-error,
  .cart-error {
    padding: 1.2rem;
  }

  .validation-error__message,
  .cart-error__message {
    font-size: 1.3rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .cart-message {
    border: 2px solid;
    backdrop-filter: none;
    background: rgba(var(--color-background), 1);
  }

  .validation-error,
  .cart-error {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .cart-message {
    transition: none;
  }

  .button__spinner {
    animation: none;
  }

  .button--loading .button__spinner {
    border: 2px solid white;
    border-top: 2px solid transparent;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .cart-message {
    background: rgba(0, 0, 0, 0.9);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  }

  .validation-error,
  .cart-error {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.4);
  }
}

/* =============================================================================
   ORDER MANAGEMENT STYLES
   ============================================================================= */

/* Order tracking interface */
.order-tracking-interface {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 800px;
  max-height: 80vh;
  background: rgba(var(--color-background), 1);
  border-radius: var(--border-radius);
  box-shadow: 0 20px 60px rgba(var(--color-shadow), 0.3);
  z-index: 1000;
  overflow: hidden;
}

.order-tracking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(var(--color-border), 0.2);
  background: rgba(var(--color-background), 0.95);
}

.order-tracking-header h3 {
  margin: 0;
  font-size: 2.4rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

.order-tracking-close {
  background: none;
  border: none;
  font-size: 2.4rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: rgba(var(--color-foreground), 0.6);
}

.order-tracking-close:hover {
  background: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 1);
}

.order-tracking-content {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

.order-search {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.order-search input {
  flex: 1;
  padding: 1.2rem;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: var(--border-radius);
  font-size: 1.4rem;
  transition: border-color 0.2s ease;
}

.order-search input:focus {
  outline: none;
  border-color: rgba(var(--color-button), 0.6);
  box-shadow: 0 0 0 3px rgba(var(--color-button), 0.1);
}

/* Order cards */
.order-card {
  background: rgba(var(--color-background), 1);
  border: 2px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.order-card:hover {
  border-color: rgba(var(--color-button), 0.3);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.1);
}

.order-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.order-card__number {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

.order-status {
  padding: 0.6rem 1.2rem;
  border-radius: calc(var(--border-radius) / 2);
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.order-status--pending {
  background: rgba(255, 193, 7, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.order-status--confirmed {
  background: rgba(34, 197, 94, 0.1);
  color: #10b981;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.order-status--in-production {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.order-status--quality-check {
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
  border: 1px solid rgba(168, 85, 247, 0.3);
}

.order-status--shipped {
  background: rgba(6, 182, 212, 0.1);
  color: #06b6d4;
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.order-status--delivered {
  background: rgba(34, 197, 94, 0.1);
  color: #10b981;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.order-status--cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.order-card__content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
  margin-bottom: 2rem;
}

.order-card__portraits {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.portrait-summary {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: rgba(var(--color-button), 0.02);
  border: 1px solid rgba(var(--color-button), 0.1);
  border-radius: calc(var(--border-radius) / 2);
}

.portrait-summary__breed {
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

.portrait-summary__frame,
.portrait-summary__size {
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.7);
}

.order-card__details {
  text-align: right;
}

.order-card__total {
  font-size: 1.8rem;
  font-weight: 700;
  color: rgba(var(--color-button), 1);
  margin: 0 0 0.5rem 0;
}

.order-card__delivery {
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.7);
  margin: 0;
}

/* Order timeline */
.order-timeline {
  position: relative;
  padding-left: 2rem;
}

.order-timeline::before {
  content: '';
  position: absolute;
  left: 0.8rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(var(--color-border), 0.3);
}

.timeline-entry {
  position: relative;
  padding-bottom: 2rem;
}

.timeline-entry:last-child {
  padding-bottom: 0;
}

.timeline-entry__marker {
  position: absolute;
  left: -1.8rem;
  top: 0.2rem;
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 50%;
  background: rgba(var(--color-border), 0.5);
  border: 2px solid rgba(var(--color-background), 1);
}

.timeline-entry--current .timeline-entry__marker {
  background: rgba(var(--color-button), 1);
  box-shadow: 0 0 0 4px rgba(var(--color-button), 0.2);
}

.timeline-entry__content {
  padding-left: 0.5rem;
}

.timeline-entry__message {
  font-size: 1.4rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.9);
  margin: 0 0 0.3rem 0;
}

.timeline-entry__time {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.6);
}

/* No results states */
.no-orders,
.no-results {
  text-align: center;
  padding: 3rem;
  color: rgba(var(--color-foreground), 0.6);
  font-size: 1.6rem;
}

/* Customer dashboard */
.customer-dashboard {
  background: rgba(var(--color-background), 1);
  border: 2px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin: 2rem 0;
}

.customer-dashboard h3 {
  margin: 0 0 1.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 1);
}

/* Mobile responsive adjustments */
@media screen and (max-width: 767px) {
  .order-tracking-interface {
    width: 95vw;
    max-height: 90vh;
  }

  .order-tracking-header {
    padding: 1.5rem;
  }

  .order-tracking-header h3 {
    font-size: 2rem;
  }

  .order-tracking-content {
    padding: 1.5rem;
  }

  .order-search {
    flex-direction: column;
  }

  .order-card {
    padding: 1.5rem;
  }

  .order-card__content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .order-card__details {
    text-align: left;
  }

  .portrait-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .order-timeline {
    padding-left: 1.5rem;
  }

  .timeline-entry__marker {
    left: -1.5rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .order-card {
    border-width: 3px;
  }

  .order-status {
    border-width: 2px;
  }

  .timeline-entry__marker {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .order-card {
    transition: none;
  }

  .order-tracking-close {
    transition: none;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .order-tracking-interface {
    background: rgba(0, 0, 0, 0.95);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  }

  .order-tracking-header {
    background: rgba(0, 0, 0, 0.9);
  }

  .order-card {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .portrait-summary {
    background: rgba(255, 255, 255, 0.02);
    border-color: rgba(255, 255, 255, 0.05);
  }
}

/* =============================================================================
   RESPONSIVE ANIMATIONS
   ============================================================================= */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media screen and (max-width: 749px) {
  .step-container {
    transform: translateY(20px);
  }

  .step-container[data-step-active="true"] {
    transform: translateY(0);
  }

  .upload-preview-comparison {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .breed-picker__grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
  }
}

.royal-portrait-builder__header {
  text-align: center;
  margin-bottom: 3rem;
}

.royal-portrait-builder__heading {
  margin-bottom: 1rem;
}

.royal-portrait-builder__description {
  font-size: 1.6rem;
  color: rgba(var(--color-foreground), 0.75);
}

.royal-portrait-builder__container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
}

@media screen and (min-width: 990px) {
  .royal-portrait-builder__container {
    grid-template-columns: 1fr 1fr;
    grid-template-areas: 
      "breed-picker preview"
      "photo-upload preview"
      "frame-selector preview"
      "size-selector preview"
      "actions actions";
  }
  
  .royal-portrait-builder__section:nth-child(1) { grid-area: breed-picker; }
  .royal-portrait-builder__section:nth-child(2) { grid-area: photo-upload; }
  .royal-portrait-builder__preview { grid-area: preview; }
  .royal-portrait-builder__section:nth-child(4) { grid-area: frame-selector; }
  .royal-portrait-builder__section:nth-child(5) { grid-area: size-selector; }
  .royal-portrait-builder__actions { grid-area: actions; }
}

.royal-portrait-builder__section {
  background: rgba(var(--color-background), 1);
  border: 1px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: var(--border-radius);
  padding: 2rem;
}

.royal-portrait-builder__section-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: rgb(var(--color-foreground));
}

/* Breed Picker Styles */
.breed-picker__search {
  margin-bottom: 1.5rem;
}

.breed-picker__search-input {
  width: 100%;
}

.breed-picker__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
}

.breed-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.breed-option:hover {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
}

.breed-option.selected {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.1);
}

.breed-option__image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 0.5rem;
  background: rgba(var(--color-foreground), 0.1);
}

.breed-option__name {
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.2;
}

.breed-picker__or {
  text-align: center;
  margin: 2rem 0;
  font-style: italic;
  color: rgba(var(--color-foreground), 0.6);
}

/* Photo Upload Styles */
.photo-upload__dropzone {
  border: 2px dashed rgba(var(--color-border), var(--alpha-border));
  border-radius: var(--border-radius);
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.photo-upload__dropzone:hover,
.photo-upload__dropzone.dragover {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
}

.photo-upload__icon {
  margin-bottom: 1rem;
}

.photo-upload__icon svg {
  width: 4rem;
  height: 4rem;
  color: rgba(var(--color-foreground), 0.4);
}

.photo-upload__primary {
  font-size: 1.6rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.photo-upload__secondary {
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 0.6);
  margin-bottom: 1.5rem;
}

.photo-upload__browse {
  margin-top: 1rem;
}

.photo-upload__preview {
  position: relative;
  display: inline-block;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.photo-upload__preview-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: var(--border-radius);
}

.photo-upload__remove {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(var(--color-background), 0.9);
}

.photo-upload__status {
  margin-top: 1rem;
  font-size: 1.4rem;
}

.photo-upload__status.error {
  color: rgb(var(--color-error, 220, 53, 69));
}

.photo-upload__status.success {
  color: rgb(var(--color-success, 40, 167, 69));
}

/* Progressive Preview Styles */
.progressive-preview {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(var(--color-foreground), 0.02);
  border-radius: var(--border-radius);
  border: 1px solid rgba(var(--color-border), 0.2);
}

.progressive-preview__header {
  text-align: center;
  margin-bottom: 2rem;
}

.progressive-preview__title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: rgba(var(--color-foreground), 0.9);
}

.progressive-preview__description {
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 0.7);
  margin: 0;
}

.progressive-preview__canvas-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: rgba(var(--color-background), 1);
  border-radius: var(--border-radius);
  border: 2px solid rgba(var(--color-border), 0.3);
  overflow: hidden;
}

.progressive-preview__canvas {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  display: block;
}

.progressive-preview__placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(var(--color-foreground), 0.5);
  font-size: 1.4rem;
  text-align: center;
  background: rgba(var(--color-background), 0.8);
}

.progressive-preview__icon {
  margin-bottom: 1rem;
  opacity: 0.6;
}

/* Canvas Preview Styles */
.portrait-canvas-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background: rgba(var(--color-foreground), 0.05);
  border-radius: var(--border-radius);
}

.portrait-canvas {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(var(--color-shadow), 0.15);
}

.portrait-canvas__loading,
.portrait-canvas__error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

/* Background Selector Styles */
.background-selector {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.background-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1.5rem;
  margin: 0;
  padding: 0;
}

/* Background Option Card Styles */
.background-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  border: 2px solid rgba(var(--color-border), 0.2);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  background: rgba(var(--color-background), 1);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(var(--color-shadow), 0.1);
}

/* Background Preview Styles */
.background-option__preview {
  width: 80px;
  height: 80px;
  margin-bottom: 1rem;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid rgba(var(--color-border), 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Special handling for light colors that need visible borders */
.background-option__preview[style*="#ffffff"],
.background-option__preview[style*="#f8f4e6"],
.background-option__preview[style*="white"] {
  border-color: rgba(var(--color-border), 0.5);
}

/* Background Option Interactive States */
.background-option:hover {
  border-color: rgba(var(--color-button), 0.4);
  background: rgba(var(--color-button), 0.02);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(var(--color-button), 0.15);
}

.background-option:hover .background-option__preview {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.background-option--selected {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(var(--color-button), 0.2);
}

.background-option--selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, rgb(var(--color-button)), rgba(var(--color-button), 0.7));
  animation: selectedGlow 2s ease-in-out infinite alternate;
}

.background-option--selected .background-option__preview {
  border-color: rgb(var(--color-button));
  box-shadow: 0 4px 12px rgba(var(--color-button), 0.3);
}

/* Focus states for accessibility */
.background-option:focus {
  outline: 2px solid rgb(var(--color-button));
  outline-offset: 2px;
}

.background-option:focus:not(:focus-visible) {
  outline: none;
}

/* Background Option Typography */
.background-option__name {
  font-size: 1.2rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
  margin: 0;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.background-option:hover .background-option__name {
  color: rgba(var(--color-foreground), 0.9);
}

.background-option--selected .background-option__name {
  color: rgb(var(--color-button));
  font-weight: 700;
}

/* Accessibility Enhancements */
/* High contrast mode support */
@media (prefers-contrast: high) {
  .background-option {
    border-width: 3px;
    border-color: rgb(var(--color-foreground));
  }

  .background-option--selected {
    border-color: rgb(var(--color-button));
    background: rgba(var(--color-button), 0.2);
  }

  .background-option__preview {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .background-option,
  .background-option__preview,
  .background-option__name {
    transition: none;
    animation: none;
  }

  .background-option--selected::before {
    animation: none;
  }

  .background-option:hover {
    transform: none;
  }
}

/* Enhanced keyboard navigation */
.background-option:focus-visible {
  outline: 3px solid rgb(var(--color-button));
  outline-offset: 3px;
  border-color: rgb(var(--color-button));
}

/* Screen reader support */
.background-option[aria-selected="true"] {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
}

/* Issue 10: Background Category Organization Styles */
.background-category {
  margin-bottom: 2rem;
}

.background-category__title {
  font-size: 1.4rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(var(--color-button), 0.2);
}

.background-category__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1.5rem;
}

/* Issue 11: Background Search and Filter Styles */
.background-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(var(--color-background), 0.5);
  border-radius: 8px;
  border: 1px solid rgba(var(--color-border), 0.3);
}

.background-search {
  position: relative;
  display: flex;
  align-items: center;
}

.background-search__input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 6px;
  font-size: 1rem;
  background: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.background-search__input:focus {
  outline: none;
  border-color: rgb(var(--color-button));
  box-shadow: 0 0 0 3px rgba(var(--color-button), 0.1);
}

.background-search__clear {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 1.5rem;
  color: rgba(var(--color-foreground), 0.6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: none;
  transition: background-color 0.2s ease;
}

.background-search__clear:hover {
  background: rgba(var(--color-foreground), 0.1);
}

.background-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.background-filter {
  padding: 0.5rem 1rem;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 20px;
  background: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.background-filter:hover {
  border-color: rgba(var(--color-button), 0.5);
  background: rgba(var(--color-button), 0.05);
}

.background-filter--active {
  border-color: rgb(var(--color-button));
  background: rgb(var(--color-button));
  color: rgb(var(--color-background));
}

.background-no-results {
  text-align: center;
  padding: 2rem;
  color: rgba(var(--color-foreground), 0.7);
}

.background-no-results__clear {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: rgb(var(--color-button));
  color: rgb(var(--color-background));
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.background-no-results__clear:hover {
  opacity: 0.9;
}

/* Issue 15: Advanced Background Customization Styles */
.background-advanced-panel {
  margin-top: 1rem;
  border: 1px solid rgba(var(--color-border), 0.3);
  border-radius: 8px;
  background: rgba(var(--color-background), 0.8);
}

.background-advanced-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(var(--color-border), 0.2);
}

.background-advanced-header h5 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
}

.background-advanced-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: rgb(var(--color-button));
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.background-advanced-toggle:hover {
  background: rgba(var(--color-button), 0.1);
}

.background-advanced-toggle__icon {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.background-advanced-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.background-custom-color,
.background-opacity-control,
.background-blend-mode {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.background-custom-color label,
.background-opacity-control label,
.background-blend-mode label {
  font-weight: 500;
  color: rgb(var(--color-foreground));
  font-size: 0.9rem;
}

.background-color-input-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.background-color-picker {
  width: 50px;
  height: 40px;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 6px;
  cursor: pointer;
  background: none;
}

.background-color-hex {
  flex: 1;
  padding: 0.5rem;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 6px;
  font-family: monospace;
  font-size: 0.9rem;
  background: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
}

.background-color-hex:focus {
  outline: none;
  border-color: rgb(var(--color-button));
}

.background-opacity-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.background-opacity-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: rgba(var(--color-border), 0.3);
  outline: none;
  cursor: pointer;
}

.background-opacity-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgb(var(--color-button));
  cursor: pointer;
  border: 2px solid rgb(var(--color-background));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.background-opacity-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgb(var(--color-button));
  cursor: pointer;
  border: 2px solid rgb(var(--color-background));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.background-opacity-value {
  font-weight: 600;
  color: rgb(var(--color-button));
  min-width: 40px;
  text-align: right;
}

.background-blend-select {
  padding: 0.5rem;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 6px;
  background: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  font-size: 0.9rem;
}

.background-blend-select:focus {
  outline: none;
  border-color: rgb(var(--color-button));
}

.background-advanced-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(var(--color-border), 0.2);
}

.background-reset-advanced,
.background-apply-advanced {
  padding: 0.5rem 1rem;
  border: 2px solid rgba(var(--color-border), 0.3);
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.background-reset-advanced {
  background: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
}

.background-reset-advanced:hover {
  border-color: rgba(var(--color-foreground), 0.5);
}

.background-apply-advanced {
  background: rgb(var(--color-button));
  color: rgb(var(--color-background));
  border-color: rgb(var(--color-button));
}

.background-apply-advanced:hover {
  opacity: 0.9;
}

/* Frame Selector Styles */
.frame-selector {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Frame Toggle Styles */
.frame-toggle {
  display: flex;
  border: 2px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: var(--border-radius);
  overflow: hidden;
  background: rgba(var(--color-background), 1);
}

.frame-toggle__option {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.frame-toggle__option:hover {
  background: rgba(var(--color-button), 0.05);
}

.frame-toggle__option--active {
  background: rgba(var(--color-button), 0.1);
  color: rgb(var(--color-button));
}

.frame-toggle__checkmark {
  font-size: 1.2rem;
  font-weight: bold;
}

.frame-toggle__text {
  font-size: 1.1rem;
  font-weight: 600;
}

/* Frame Styles Section */
.frame-styles {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.frame-styles__section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.frame-styles__title {
  font-size: 1.3rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
  margin: 0;
}

.frame-selector__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1.5rem;
}

.frame-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  border: 2px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  background: rgba(var(--color-background), 1);
}

.frame-option:hover {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-button), 0.15);
}

.frame-option.frame-option--selected {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-button), 0.2);
}

/* Frame Preview Styles */
.frame-option__preview {
  width: 80px;
  height: 80px;
  margin-bottom: 1rem;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.frame-option__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.frame-option__sample {
  width: 60px;
  height: 60px;
  border: 8px solid;
  border-radius: 2px;
  background: rgba(var(--color-background), 0.8);
}

.frame-option__sample--black {
  border-color: #2c2c2c;
}

.frame-option__sample--white {
  border-color: #ffffff;
  box-shadow: inset 0 0 0 1px rgba(var(--color-border), 0.3);
}

.frame-option__sample--gold {
  border-color: #d4af37;
  background: linear-gradient(135deg, #f4e4bc 0%, #d4af37 100%);
}

.frame-option__sample--silver {
  border-color: #c0c0c0;
  background: linear-gradient(135deg, #f0f0f0 0%, #c0c0c0 100%);
}

/* Frame Info Styles */
.frame-option__info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.frame-option__name {
  font-size: 1.2rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
  margin-bottom: 0.25rem;
}

.frame-option__details {
  font-size: 0.9rem;
  color: rgb(var(--color-button));
  text-decoration: underline;
  cursor: pointer;
  margin-bottom: 0.25rem;
}

.frame-option__details:hover {
  color: rgba(var(--color-button), 0.8);
}

.frame-option__price {
  font-size: 1.1rem;
  font-weight: 500;
  color: rgba(var(--color-foreground), 0.7);
}

/* Enhanced Frame Card Styles for Interactive Features */
.frame-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  border: 2px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  background: rgba(var(--color-background), 1);
  position: relative;
  overflow: hidden;
}

.frame-card--interactive {
  transform-origin: center;
}

.frame-card--interactive:hover,
.frame-card--interactive:focus {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(var(--color-button), 0.15);
}

.frame-card--hovering {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.08);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(var(--color-button), 0.2);
}

.frame-card--selected {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--color-button), 0.25);
}

/* Frame Card Preview Container */
.frame-card__preview-container {
  position: relative;
  width: 100px;
  height: 100px;
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.frame-card__image {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.frame-card__frame-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

.frame-card__preview-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Hover Overlay */
.frame-card__hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-button), 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 3;
}

.frame-card--interactive:hover .frame-card__hover-overlay {
  opacity: 1;
}

.frame-card__hover-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
}

.frame-card__hover-icon {
  font-size: 1.5rem;
  animation: pulse 2s infinite;
}

.frame-card__hover-text {
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Frame Card Info */
.frame-card__info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  width: 100%;
}

.frame-card__name {
  font-size: 1.2rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
  margin: 0;
  transition: color 0.3s ease;
}

.frame-card--interactive:hover .frame-card__name {
  color: rgb(var(--color-button));
}

.frame-card__style {
  font-size: 0.9rem;
  color: rgba(var(--color-foreground), 0.7);
  margin: 0;
  transition: color 0.3s ease;
}

.frame-card__price {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0.25rem 0;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.frame-card__price--included {
  color: rgb(var(--color-success, 34, 197, 94));
  background: rgba(var(--color-success, 34, 197, 94), 0.1);
}

.frame-card__price--premium {
  color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.1);
}

.frame-card__description {
  font-size: 0.85rem;
  color: rgba(var(--color-foreground), 0.6);
  text-align: center;
  line-height: 1.4;
  margin-top: 0.5rem;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

/* Selection Indicator */
.frame-card__selection-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  background: rgb(var(--color-button));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  z-index: 4;
}

.frame-card--selected .frame-card__selection-indicator {
  opacity: 1;
  transform: scale(1);
}

.frame-card__checkmark {
  color: white;
  font-size: 0.9rem;
  font-weight: bold;
}

/* Frame Selection Animation */
@keyframes frameSelected {
  0% {
    transform: translateY(-4px) scale(1.02);
  }
  50% {
    transform: translateY(-6px) scale(1.05);
    box-shadow: 0 12px 30px rgba(var(--color-button), 0.3);
  }
  100% {
    transform: translateY(-2px) scale(1);
    box-shadow: 0 6px 20px rgba(var(--color-button), 0.25);
  }
}

/* Responsive adjustments for frame cards */
@media (max-width: 768px) {
  .frame-card {
    padding: 1rem 0.75rem;
  }

  .frame-card__preview-container {
    width: 80px;
    height: 80px;
  }

  .frame-card__name {
    font-size: 1rem;
  }

  .frame-card__hover-icon {
    font-size: 1.2rem;
  }

  .frame-card__hover-text {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .frame-card {
    padding: 0.75rem 0.5rem;
  }

  .frame-card__preview-container {
    width: 60px;
    height: 60px;
  }

  .frame-card__name {
    font-size: 0.9rem;
  }

  .frame-card__style {
    font-size: 0.8rem;
  }

  .frame-card__price {
    font-size: 1rem;
  }

  /* Mobile background option adjustments */
  .background-option__preview {
    width: 60px;
    height: 60px;
    margin-bottom: 0.75rem;
  }

  .background-option__name {
    font-size: 1rem;
    line-height: 1.2;
  }
}

/* Enhanced Size Selector Styles */
.size-selector__options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.size-option {
  padding: 1.5rem;
  border: 2px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
}

.size-option:hover,
.size-option.size-option--hover {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.05);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(var(--color-button), 0.15);
}

.size-option.size-option--selected {
  border-color: rgb(var(--color-button));
  background: rgba(var(--color-button), 0.1);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(var(--color-button), 0.25);
}

.size-option.size-option--clicked {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

/* Enhanced hover effects */
.size-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--color-button), 0.1), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

.size-option:hover::before {
  left: 100%;
}

/* Enhanced Size Preview Styles */
.size-option__preview {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

.size-preview {
  position: relative;
  border: 2px solid rgba(var(--color-border), var(--alpha-border));
  border-radius: 8px;
  overflow: hidden;
  background: rgba(var(--color-background), 1);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.size-preview--enhanced {
  border-color: rgba(var(--color-button), 0.3);
}

.size-option:hover .size-preview,
.size-option.size-option--hover .size-preview {
  border-color: rgb(var(--color-button));
  box-shadow: 0 4px 16px rgba(var(--color-button), 0.2);
  transform: scale(1.05);
}

.size-preview__frame {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.size-preview__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.size-option:hover .size-preview__image {
  opacity: 1;
}

.size-preview__canvas {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.size-preview__loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.size-preview__spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(var(--color-button), 0.3);
  border-top: 2px solid rgb(var(--color-button));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.size-preview__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(var(--color-foreground), 0.5);
  width: 100%;
  height: 100%;
  gap: 0.5rem;
}

.size-preview__placeholder-text {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.size-preview__overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.size-option:hover .size-preview__overlay,
.size-option.size-option--hover .size-preview__overlay {
  opacity: 1;
}

.size-preview__dimensions-label {
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Enhanced Size Info Styles */
.size-option__info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
}

.size-option__name {
  font-size: 1.1rem;
  font-weight: 700;
  color: rgb(var(--color-foreground));
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.size-option:hover .size-option__name,
.size-option.size-option--hover .size-option__name {
  color: rgb(var(--color-button));
}

.size-option__dimensions {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(var(--color-foreground), 0.8);
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.size-option:hover .size-option__dimensions,
.size-option.size-option--hover .size-option__dimensions {
  color: rgba(var(--color-foreground), 1);
}

.size-option__description {
  font-size: 0.875rem;
  color: rgba(var(--color-foreground), 0.6);
  text-align: center;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.size-option:hover .size-option__description,
.size-option.size-option--hover .size-option__description {
  color: rgba(var(--color-foreground), 0.8);
}

.size-option__price {
  font-size: 1.1rem;
  font-weight: 600;
  color: rgb(var(--color-button));
  padding: 0.25rem 0.75rem;
  background: rgba(var(--color-button), 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.size-option:hover .size-option__price,
.size-option.size-option--hover .size-option__price {
  background: rgba(var(--color-button), 0.2);
  transform: scale(1.05);
}

.size-option.size-option--selected .size-option__price {
  background: rgb(var(--color-button));
  color: white;
}

/* Actions and Pricing */
.royal-portrait-builder__actions {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media screen and (min-width: 750px) {
  .royal-portrait-builder__actions {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.royal-portrait-builder__pricing {
  text-align: center;
}

@media screen and (min-width: 750px) {
  .royal-portrait-builder__pricing {
    text-align: left;
  }
}

.pricing__total {
  font-size: 2.4rem;
  font-weight: 700;
  color: rgb(var(--color-button));
  margin-bottom: 0.5rem;
}

.pricing__breakdown {
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 0.7);
}

.royal-portrait-builder__buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

@media screen and (min-width: 750px) {
  .royal-portrait-builder__buttons {
    justify-content: flex-end;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 2px solid rgba(var(--color-foreground), 0.2);
  border-radius: 50%;
  border-top-color: rgb(var(--color-button));
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media screen and (max-width: 749px) {
  .royal-portrait-builder__section {
    padding: 1.5rem;
  }
  
  .breed-picker__grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .frame-selector__grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .size-selector__options {
    grid-template-columns: 1fr;
  }
  
  .royal-portrait-builder__buttons {
    flex-direction: column;
  }
}
