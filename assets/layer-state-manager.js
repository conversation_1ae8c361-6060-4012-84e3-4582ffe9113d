/**
 * LayerStateManager - Centralized Layer State Management
 *
 * TASK 1.1: Create LayerStateManager Class
 *
 * This class provides a single source of truth for all layer state across
 * multiple canvas instances, preventing canvas clearing due to state desynchronization.
 *
 * Key Features:
 * - Global layer state storage (breed, costume, frame, background)
 * - Canvas subscriber management system
 * - State persistence and restoration
 * - Comprehensive logging for debugging
 * - Performance monitoring (<2KB memory footprint)
 */

class LayerStateManager {
  constructor() {
    // TASK 1.1: Global layer state storage
    this.globalLayerState = {
      breed: null,
      costume: null,
      frame: null,
      background: null,
      timestamp: Date.now(),
      version: '1.0.0',
    };

    // TASK 1.1: Canvas subscriber management
    this.canvasSubscribers = new Set();
    this.subscriberMetadata = new Map(); // Store canvas IDs and metadata

    // TASK 1.1: State history for debugging
    this.stateHistory = [];
    this.maxHistorySize = 10;

    // TASK 1.1: Performance monitoring
    this.performanceMetrics = {
      updateCount: 0,
      syncCount: 0,
      averageUpdateTime: 0,
      averageSyncTime: 0,
      lastOperationTime: 0,
    };

    // TASK 1.1: Error tracking
    this.errorLog = [];
    this.maxErrorLogSize = 20;

    // TASK 1.1: State validation
    this.validLayerTypes = ['breed', 'costume', 'frame', 'background'];

    // TASK 1.3: Persistence configuration
    this.persistenceConfig = {
      storageKey: 'portraitBuilder_globalLayerState',
      backupKey: 'portraitBuilder_globalLayerState_backup',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      compressionEnabled: true,
      autoSave: true,
      saveInterval: 30000, // 30 seconds
    };

    // TASK 1.3: Auto-save timer
    this.autoSaveTimer = null;

    // TASK 1.3: Initialize persistence
    this.initializePersistence();

    // TASK 1.4: Initialize debugging tools
    this.initializeDebugging();

    console.log('🌐 LayerStateManager initialized', {
      version: this.globalLayerState.version,
      timestamp: this.globalLayerState.timestamp,
      memoryFootprint: this.getMemoryFootprint(),
      persistenceEnabled: this.persistenceConfig.autoSave,
      debuggingEnabled: typeof window !== 'undefined',
    });
  }

  /**
   * TASK 1.1: Update global layer state
   * @param {string} layerType - Type of layer (breed, costume, frame, background)
   * @param {Object|null} layerData - Layer data or null to clear
   * @returns {boolean} Success status
   */
  updateLayer(layerType, layerData) {
    const startTime = performance.now();

    try {
      // Validate layer type
      if (!this.validLayerTypes.includes(layerType)) {
        throw new Error(`Invalid layer type: ${layerType}`);
      }

      // Store previous state for history
      const previousState = { ...this.globalLayerState };

      // Update global state
      this.globalLayerState[layerType] = layerData;
      this.globalLayerState.timestamp = Date.now();

      // Add to history
      this.addToHistory('UPDATE', layerType, previousState, this.globalLayerState);

      // Notify subscribers
      this.notifySubscribers(layerType, layerData);

      // Update performance metrics
      const operationTime = performance.now() - startTime;
      this.updatePerformanceMetrics('update', operationTime);

      console.log(`🌐 GLOBAL STATE: Updated ${layerType}`, {
        previous: previousState[layerType],
        current: layerData,
        subscribers: this.canvasSubscribers.size,
        operationTime: `${operationTime.toFixed(2)}ms`,
      });

      return true;
    } catch (error) {
      this.logError('updateLayer', error, { layerType, layerData });
      return false;
    }
  }

  /**
   * TASK 1.1: Get current global layer state
   * @param {string} layerType - Optional specific layer type
   * @returns {Object|*} Global state or specific layer
   */
  getLayerState(layerType = null) {
    if (layerType) {
      if (!this.validLayerTypes.includes(layerType)) {
        this.logError('getLayerState', new Error(`Invalid layer type: ${layerType}`));
        return null;
      }
      return this.globalLayerState[layerType];
    }
    return { ...this.globalLayerState };
  }

  /**
   * TASK 1.1: Check if any important layers exist globally
   * @returns {boolean} True if any layers exist
   */
  hasImportantLayers() {
    return !!(
      this.globalLayerState.breed ||
      this.globalLayerState.costume ||
      this.globalLayerState.frame ||
      this.globalLayerState.background
    );
  }

  /**
   * TASK 1.1: Register canvas as subscriber
   * @param {Object} canvas - Canvas instance
   * @param {string} canvasId - Unique identifier for canvas
   * @returns {boolean} Success status
   */
  subscribe(canvas, canvasId) {
    try {
      if (!canvas || !canvasId) {
        throw new Error('Canvas and canvasId are required for subscription');
      }

      this.canvasSubscribers.add(canvas);
      this.subscriberMetadata.set(canvas, {
        id: canvasId,
        subscribedAt: Date.now(),
        syncCount: 0,
        lastSyncTime: null,
      });

      // Sync current state to new subscriber
      this.syncToCanvas(canvas);

      console.log(`🌐 SUBSCRIBER: Registered canvas ${canvasId}`, {
        totalSubscribers: this.canvasSubscribers.size,
        currentState: this.getLayerState(),
      });

      return true;
    } catch (error) {
      this.logError('subscribe', error, { canvasId });
      return false;
    }
  }

  /**
   * TASK 1.1: Unregister canvas subscriber
   * @param {Object} canvas - Canvas instance
   * @returns {boolean} Success status
   */
  unsubscribe(canvas) {
    try {
      const metadata = this.subscriberMetadata.get(canvas);
      const canvasId = metadata ? metadata.id : 'unknown';

      this.canvasSubscribers.delete(canvas);
      this.subscriberMetadata.delete(canvas);

      console.log(`🌐 SUBSCRIBER: Unregistered canvas ${canvasId}`, {
        totalSubscribers: this.canvasSubscribers.size,
      });

      return true;
    } catch (error) {
      this.logError('unsubscribe', error);
      return false;
    }
  }

  /**
   * TASK 1.1: Notify all subscribers of state change
   * @param {string} layerType - Type of layer that changed
   * @param {Object|null} layerData - New layer data
   */
  notifySubscribers(layerType, layerData) {
    const startTime = performance.now();
    let successCount = 0;
    let errorCount = 0;

    this.canvasSubscribers.forEach((canvas) => {
      try {
        this.syncLayerToCanvas(canvas, layerType, layerData);
        successCount++;
      } catch (error) {
        errorCount++;
        this.logError('notifySubscribers', error, { layerType, canvasId: this.getCanvasId(canvas) });
      }
    });

    const operationTime = performance.now() - startTime;
    this.updatePerformanceMetrics('sync', operationTime);

    console.log(`🌐 NOTIFICATION: Synced ${layerType} to subscribers`, {
      successCount,
      errorCount,
      totalSubscribers: this.canvasSubscribers.size,
      operationTime: `${operationTime.toFixed(2)}ms`,
    });
  }

  /**
   * TASK 1.1: Sync specific layer to canvas
   * @param {Object} canvas - Canvas instance
   * @param {string} layerType - Type of layer
   * @param {Object|null} layerData - Layer data
   */
  syncLayerToCanvas(canvas, layerType, layerData) {
    if (!canvas || !canvas.layers) {
      throw new Error('Invalid canvas for layer synchronization');
    }

    canvas.layers[layerType] = layerData;

    // Update subscriber metadata
    const metadata = this.subscriberMetadata.get(canvas);
    if (metadata) {
      metadata.syncCount++;
      metadata.lastSyncTime = Date.now();
    }
  }

  /**
   * TASK 1.1: Sync all current state to specific canvas
   * @param {Object} canvas - Canvas instance
   */
  syncToCanvas(canvas) {
    if (!canvas || !canvas.layers) {
      throw new Error('Invalid canvas for full synchronization');
    }

    this.validLayerTypes.forEach((layerType) => {
      const layerData = this.globalLayerState[layerType];
      if (layerData) {
        this.syncLayerToCanvas(canvas, layerType, layerData);
      }
    });

    console.log(`🌐 SYNC: Full state synced to canvas ${this.getCanvasId(canvas)}`);
  }

  /**
   * TASK 1.1: Add operation to state history
   * @param {string} operation - Operation type
   * @param {string} layerType - Layer type
   * @param {Object} previousState - Previous state
   * @param {Object} newState - New state
   */
  addToHistory(operation, layerType, previousState, newState) {
    const historyEntry = {
      timestamp: Date.now(),
      operation,
      layerType,
      previousState: { ...previousState },
      newState: { ...newState },
    };

    this.stateHistory.unshift(historyEntry);

    // Maintain history size limit
    if (this.stateHistory.length > this.maxHistorySize) {
      this.stateHistory = this.stateHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * TASK 1.1: Update performance metrics
   * @param {string} operationType - Type of operation (update, sync)
   * @param {number} operationTime - Time taken in milliseconds
   */
  updatePerformanceMetrics(operationType, operationTime) {
    this.performanceMetrics.lastOperationTime = operationTime;

    if (operationType === 'update') {
      this.performanceMetrics.updateCount++;
      this.performanceMetrics.averageUpdateTime =
        (this.performanceMetrics.averageUpdateTime * (this.performanceMetrics.updateCount - 1) + operationTime) /
        this.performanceMetrics.updateCount;
    } else if (operationType === 'sync') {
      this.performanceMetrics.syncCount++;
      this.performanceMetrics.averageSyncTime =
        (this.performanceMetrics.averageSyncTime * (this.performanceMetrics.syncCount - 1) + operationTime) /
        this.performanceMetrics.syncCount;
    }
  }

  /**
   * TASK 1.1: Log error with context
   * @param {string} method - Method where error occurred
   * @param {Error} error - Error object
   * @param {Object} context - Additional context
   */
  logError(method, error, context = {}) {
    const errorEntry = {
      timestamp: Date.now(),
      method,
      error: error.message,
      stack: error.stack,
      context,
    };

    this.errorLog.unshift(errorEntry);

    // Maintain error log size limit
    if (this.errorLog.length > this.maxErrorLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxErrorLogSize);
    }

    console.error(`🌐 ERROR in ${method}:`, error.message, context);
  }

  /**
   * TASK 1.1: Get canvas ID from metadata
   * @param {Object} canvas - Canvas instance
   * @returns {string} Canvas ID
   */
  getCanvasId(canvas) {
    const metadata = this.subscriberMetadata.get(canvas);
    return metadata ? metadata.id : 'unknown';
  }

  /**
   * TASK 1.1: Get memory footprint estimate
   * @returns {number} Estimated memory usage in bytes
   */
  getMemoryFootprint() {
    const stateSize = JSON.stringify(this.globalLayerState).length;
    const historySize = JSON.stringify(this.stateHistory).length;
    const errorLogSize = JSON.stringify(this.errorLog).length;
    const metadataSize = this.subscriberMetadata.size * 100; // Estimate

    return stateSize + historySize + errorLogSize + metadataSize;
  }

  /**
   * TASK 1.1: Get performance metrics
   * @returns {Object} Current performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      memoryFootprint: this.getMemoryFootprint(),
      subscriberCount: this.canvasSubscribers.size,
      historySize: this.stateHistory.length,
      errorCount: this.errorLog.length,
    };
  }

  /**
   * TASK 1.2: Advanced state synchronization with validation
   * @param {Object} canvas - Canvas instance to sync
   * @param {boolean} forceSync - Force sync even if state appears current
   * @returns {boolean} Success status
   */
  syncToCanvasWithValidation(canvas, forceSync = false) {
    const startTime = performance.now();

    try {
      if (!canvas || !canvas.layers) {
        throw new Error('Invalid canvas for synchronization');
      }

      const canvasId = this.getCanvasId(canvas);
      const metadata = this.subscriberMetadata.get(canvas);

      // Check if sync is needed (unless forced)
      if (!forceSync && metadata && metadata.lastSyncTime) {
        const timeSinceLastSync = Date.now() - metadata.lastSyncTime;
        const stateAge = Date.now() - this.globalLayerState.timestamp;

        if (timeSinceLastSync < stateAge) {
          console.log(`🌐 SYNC SKIP: Canvas ${canvasId} already current`);
          return true;
        }
      }

      // Validate current canvas state before sync
      const preValidation = this.validateCanvasState(canvas);

      // Perform synchronization
      this.syncToCanvas(canvas);

      // Validate post-sync state
      const postValidation = this.validateCanvasState(canvas);

      const operationTime = performance.now() - startTime;

      console.log(`🌐 SYNC VALIDATED: Canvas ${canvasId}`, {
        preValidation,
        postValidation,
        operationTime: `${operationTime.toFixed(2)}ms`,
        forced: forceSync,
      });

      return postValidation.isValid;
    } catch (error) {
      this.logError('syncToCanvasWithValidation', error, { canvasId: this.getCanvasId(canvas) });
      return false;
    }
  }

  /**
   * TASK 1.2: Validate canvas state integrity
   * @param {Object} canvas - Canvas instance to validate
   * @returns {Object} Validation result
   */
  validateCanvasState(canvas) {
    try {
      if (!canvas || !canvas.layers) {
        return { isValid: false, errors: ['Canvas or layers missing'] };
      }

      const errors = [];
      const warnings = [];

      // Check each layer type
      this.validLayerTypes.forEach((layerType) => {
        const globalLayer = this.globalLayerState[layerType];
        const canvasLayer = canvas.layers[layerType];

        // If global state has layer but canvas doesn't
        if (globalLayer && !canvasLayer) {
          errors.push(`Canvas missing ${layerType} layer`);
        }

        // If canvas has layer but global state doesn't
        if (!globalLayer && canvasLayer) {
          warnings.push(`Canvas has ${layerType} layer not in global state`);
        }

        // If both exist, check for basic consistency
        if (globalLayer && canvasLayer) {
          if (globalLayer.id !== canvasLayer.id) {
            errors.push(`${layerType} layer ID mismatch: global=${globalLayer.id}, canvas=${canvasLayer.id}`);
          }
        }
      });

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: [],
        timestamp: Date.now(),
      };
    }
  }

  /**
   * TASK 1.2: Batch update multiple layers atomically
   * @param {Object} layerUpdates - Object with layer types as keys and layer data as values
   * @returns {boolean} Success status
   */
  batchUpdateLayers(layerUpdates) {
    const startTime = performance.now();

    try {
      // Validate all updates first
      const validationErrors = [];
      Object.keys(layerUpdates).forEach((layerType) => {
        if (!this.validLayerTypes.includes(layerType)) {
          validationErrors.push(`Invalid layer type: ${layerType}`);
        }
      });

      if (validationErrors.length > 0) {
        throw new Error(`Batch validation failed: ${validationErrors.join(', ')}`);
      }

      // Store previous state for rollback
      const previousState = { ...this.globalLayerState };

      // Apply all updates
      Object.entries(layerUpdates).forEach(([layerType, layerData]) => {
        this.globalLayerState[layerType] = layerData;
      });

      this.globalLayerState.timestamp = Date.now();

      // Add to history
      this.addToHistory('BATCH_UPDATE', 'multiple', previousState, this.globalLayerState);

      // Notify subscribers of all changes
      Object.entries(layerUpdates).forEach(([layerType, layerData]) => {
        this.notifySubscribers(layerType, layerData);
      });

      const operationTime = performance.now() - startTime;
      this.updatePerformanceMetrics('update', operationTime);

      console.log('🌐 BATCH UPDATE: Multiple layers updated', {
        layerTypes: Object.keys(layerUpdates),
        subscribers: this.canvasSubscribers.size,
        operationTime: `${operationTime.toFixed(2)}ms`,
      });

      return true;
    } catch (error) {
      this.logError('batchUpdateLayers', error, { layerUpdates });
      return false;
    }
  }

  /**
   * TASK 1.2: Force synchronization of all subscribers
   * @returns {Object} Synchronization results
   */
  forceGlobalSync() {
    const startTime = performance.now();
    const results = {
      totalSubscribers: this.canvasSubscribers.size,
      successCount: 0,
      errorCount: 0,
      errors: [],
    };

    this.canvasSubscribers.forEach((canvas) => {
      try {
        const success = this.syncToCanvasWithValidation(canvas, true);
        if (success) {
          results.successCount++;
        } else {
          results.errorCount++;
          results.errors.push(`Failed to sync canvas ${this.getCanvasId(canvas)}`);
        }
      } catch (error) {
        results.errorCount++;
        results.errors.push(`Error syncing canvas ${this.getCanvasId(canvas)}: ${error.message}`);
      }
    });

    const operationTime = performance.now() - startTime;

    console.log('🌐 FORCE SYNC: Global synchronization completed', {
      ...results,
      operationTime: `${operationTime.toFixed(2)}ms`,
    });

    return results;
  }

  /**
   * TASK 1.2: Get synchronization status for all subscribers
   * @returns {Array} Status array for each subscriber
   */
  getSyncStatus() {
    const status = [];

    this.canvasSubscribers.forEach((canvas) => {
      const metadata = this.subscriberMetadata.get(canvas);
      const validation = this.validateCanvasState(canvas);

      status.push({
        canvasId: this.getCanvasId(canvas),
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        syncCount: metadata ? metadata.syncCount : 0,
        lastSyncTime: metadata ? metadata.lastSyncTime : null,
        subscribedAt: metadata ? metadata.subscribedAt : null,
      });
    });

    return status;
  }

  /**
   * TASK 1.1: Clear all state (for testing/debugging)
   */
  clearState() {
    this.globalLayerState = {
      breed: null,
      costume: null,
      frame: null,
      background: null,
      timestamp: Date.now(),
      version: '1.0.0',
    };

    this.stateHistory = [];
    this.errorLog = [];

    // Notify subscribers of cleared state
    this.canvasSubscribers.forEach((canvas) => {
      try {
        this.syncToCanvas(canvas);
      } catch (error) {
        this.logError('clearState', error);
      }
    });

    console.log('🌐 STATE CLEARED: All global state reset');
  }

  /**
   * TASK 1.3: Initialize persistence system
   */
  initializePersistence() {
    try {
      // Try to restore from localStorage
      const restored = this.restoreFromStorage();

      if (restored) {
        console.log('🌐 PERSISTENCE: State restored from localStorage');
      } else {
        // Save initial state
        this.saveToStorage();
        console.log('🌐 PERSISTENCE: Initial state saved to localStorage');
      }

      // Start auto-save if enabled
      if (this.persistenceConfig.autoSave) {
        this.startAutoSave();
      }
    } catch (error) {
      this.logError('initializePersistence', error);
    }
  }

  /**
   * TASK 1.3: Save current state to localStorage
   * @returns {boolean} Success status
   */
  saveToStorage() {
    const startTime = performance.now();

    try {
      if (typeof localStorage === 'undefined') {
        console.warn('🌐 PERSISTENCE: localStorage not available');
        return false;
      }

      const stateToSave = {
        ...this.globalLayerState,
        savedAt: Date.now(),
        version: this.globalLayerState.version,
      };

      // Create backup of current stored state
      const currentStored = localStorage.getItem(this.persistenceConfig.storageKey);
      if (currentStored) {
        localStorage.setItem(this.persistenceConfig.backupKey, currentStored);
      }

      // Save new state
      const serializedState = this.persistenceConfig.compressionEnabled
        ? this.compressState(stateToSave)
        : JSON.stringify(stateToSave);

      localStorage.setItem(this.persistenceConfig.storageKey, serializedState);

      const operationTime = performance.now() - startTime;

      console.log('🌐 PERSISTENCE: State saved to localStorage', {
        operationTime: `${operationTime.toFixed(2)}ms`,
        compressed: this.persistenceConfig.compressionEnabled,
        size: serializedState.length,
      });

      return operationTime < 5; // Meet 5ms performance target
    } catch (error) {
      this.logError('saveToStorage', error);
      return false;
    }
  }

  /**
   * TASK 1.3: Restore state from localStorage
   * @returns {boolean} Success status
   */
  restoreFromStorage() {
    const startTime = performance.now();

    try {
      if (typeof localStorage === 'undefined') {
        return false;
      }

      const storedState = localStorage.getItem(this.persistenceConfig.storageKey);
      if (!storedState) {
        return false;
      }

      // Parse stored state
      const parsedState = this.persistenceConfig.compressionEnabled
        ? this.decompressState(storedState)
        : JSON.parse(storedState);

      // Validate stored state
      const validation = this.validateStoredState(parsedState);
      if (!validation.isValid) {
        console.warn('🌐 PERSISTENCE: Stored state invalid, attempting backup restore', validation.errors);
        return this.restoreFromBackup();
      }

      // Check if state is not too old
      const age = Date.now() - parsedState.savedAt;
      if (age > this.persistenceConfig.maxAge) {
        console.warn('🌐 PERSISTENCE: Stored state too old, using fresh state');
        return false;
      }

      // Restore state
      this.globalLayerState = {
        breed: parsedState.breed,
        costume: parsedState.costume,
        frame: parsedState.frame,
        background: parsedState.background,
        timestamp: parsedState.timestamp,
        version: parsedState.version,
      };

      // Sync to all subscribers
      this.forceGlobalSync();

      const operationTime = performance.now() - startTime;

      console.log('🌐 PERSISTENCE: State restored from localStorage', {
        operationTime: `${operationTime.toFixed(2)}ms`,
        age: `${Math.round(age / 1000)}s`,
        hasLayers: this.hasImportantLayers(),
      });

      return true;
    } catch (error) {
      this.logError('restoreFromStorage', error);
      return this.restoreFromBackup();
    }
  }

  /**
   * TASK 1.3: Restore from backup storage
   * @returns {boolean} Success status
   */
  restoreFromBackup() {
    try {
      const backupState = localStorage.getItem(this.persistenceConfig.backupKey);
      if (!backupState) {
        return false;
      }

      const parsedBackup = this.persistenceConfig.compressionEnabled
        ? this.decompressState(backupState)
        : JSON.parse(backupState);

      const validation = this.validateStoredState(parsedBackup);
      if (!validation.isValid) {
        console.error('🌐 PERSISTENCE: Backup state also invalid', validation.errors);
        return false;
      }

      // Restore from backup
      this.globalLayerState = {
        breed: parsedBackup.breed,
        costume: parsedBackup.costume,
        frame: parsedBackup.frame,
        background: parsedBackup.background,
        timestamp: parsedBackup.timestamp,
        version: parsedBackup.version,
      };

      this.forceGlobalSync();

      console.log('🌐 PERSISTENCE: State restored from backup');
      return true;
    } catch (error) {
      this.logError('restoreFromBackup', error);
      return false;
    }
  }

  /**
   * TASK 1.3: Validate stored state structure
   * @param {Object} state - State to validate
   * @returns {Object} Validation result
   */
  validateStoredState(state) {
    const errors = [];

    if (!state || typeof state !== 'object') {
      errors.push('State is not an object');
      return { isValid: false, errors };
    }

    // Check required properties
    const requiredProps = ['breed', 'costume', 'frame', 'background', 'timestamp', 'version'];
    requiredProps.forEach((prop) => {
      if (!(prop in state)) {
        errors.push(`Missing property: ${prop}`);
      }
    });

    // Check timestamp validity
    if (state.timestamp && (typeof state.timestamp !== 'number' || state.timestamp <= 0)) {
      errors.push('Invalid timestamp');
    }

    // Check version compatibility
    if (state.version && state.version !== this.globalLayerState.version) {
      console.warn(
        `🌐 PERSISTENCE: Version mismatch - stored: ${state.version}, current: ${this.globalLayerState.version}`
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * TASK 1.3: Compress state for storage
   * @param {Object} state - State to compress
   * @returns {string} Compressed state
   */
  compressState(state) {
    // Simple compression - remove null values and minimize JSON
    const compressed = {};
    Object.entries(state).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        compressed[key] = value;
      }
    });
    return JSON.stringify(compressed);
  }

  /**
   * TASK 1.3: Decompress state from storage
   * @param {string} compressedState - Compressed state string
   * @returns {Object} Decompressed state
   */
  decompressState(compressedState) {
    const parsed = JSON.parse(compressedState);

    // Ensure all required properties exist
    return {
      breed: parsed.breed || null,
      costume: parsed.costume || null,
      frame: parsed.frame || null,
      background: parsed.background || null,
      timestamp: parsed.timestamp || Date.now(),
      version: parsed.version || '1.0.0',
      savedAt: parsed.savedAt || Date.now(),
    };
  }

  /**
   * TASK 1.3: Start auto-save timer
   */
  startAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    this.autoSaveTimer = setInterval(() => {
      this.saveToStorage();
    }, this.persistenceConfig.saveInterval);

    console.log(`🌐 PERSISTENCE: Auto-save started (${this.persistenceConfig.saveInterval}ms interval)`);
  }

  /**
   * TASK 1.3: Stop auto-save timer
   */
  stopAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
      console.log('🌐 PERSISTENCE: Auto-save stopped');
    }
  }

  /**
   * TASK 1.3: Clear all persistent storage
   */
  clearPersistentStorage() {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(this.persistenceConfig.storageKey);
        localStorage.removeItem(this.persistenceConfig.backupKey);
        console.log('🌐 PERSISTENCE: All persistent storage cleared');
      }
    } catch (error) {
      this.logError('clearPersistentStorage', error);
    }
  }

  /**
   * TASK 1.4: Initialize debugging tools and console commands
   */
  initializeDebugging() {
    if (typeof window !== 'undefined') {
      // Add global debugging interface
      window.LayerStateDebug = {
        getState: () => this.getDebugState(),
        getHistory: () => this.getStateHistory(),
        getPerformance: () => this.getPerformanceMetrics(),
        getErrors: () => this.getErrorLog(),
        getSyncStatus: () => this.getSyncStatus(),
        forceSync: () => this.forceGlobalSync(),
        validateAll: () => this.validateAllCanvases(),
        clearState: () => this.clearState(),
        exportState: () => this.exportStateForDebugging(),
        importState: (state) => this.importStateForDebugging(state),
      };

      console.log('🌐 DEBUG: Debugging tools available at window.LayerStateDebug');
    }
  }

  /**
   * TASK 1.4: Get comprehensive debug state
   * @returns {Object} Complete debug information
   */
  getDebugState() {
    return {
      globalState: { ...this.globalLayerState },
      subscribers: this.canvasSubscribers.size,
      subscriberDetails: Array.from(this.canvasSubscribers).map((canvas) => ({
        id: this.getCanvasId(canvas),
        metadata: this.subscriberMetadata.get(canvas),
        validation: this.validateCanvasState(canvas),
      })),
      performance: this.getPerformanceMetrics(),
      persistence: {
        config: this.persistenceConfig,
        autoSaveActive: !!this.autoSaveTimer,
      },
      history: this.stateHistory.slice(0, 5), // Last 5 entries
      errors: this.errorLog.slice(0, 5), // Last 5 errors
      timestamp: Date.now(),
    };
  }

  /**
   * TASK 1.4: Get state history with filtering
   * @param {number} limit - Maximum number of entries
   * @param {string} operation - Filter by operation type
   * @returns {Array} Filtered state history
   */
  getStateHistory(limit = 10, operation = null) {
    let history = [...this.stateHistory];

    if (operation) {
      history = history.filter((entry) => entry.operation === operation);
    }

    return history.slice(0, limit).map((entry) => ({
      ...entry,
      timeSince: Date.now() - entry.timestamp,
      formattedTime: new Date(entry.timestamp).toISOString(),
    }));
  }

  /**
   * TASK 1.4: Get error log with analysis
   * @param {number} limit - Maximum number of entries
   * @returns {Array} Error log with analysis
   */
  getErrorLog(limit = 20) {
    const errors = this.errorLog.slice(0, limit);

    // Analyze error patterns
    const errorAnalysis = {
      totalErrors: this.errorLog.length,
      recentErrors: errors.length,
      errorsByMethod: {},
      errorsByType: {},
      criticalErrors: 0,
    };

    errors.forEach((error) => {
      // Count by method
      errorAnalysis.errorsByMethod[error.method] = (errorAnalysis.errorsByMethod[error.method] || 0) + 1;

      // Count by error type
      const errorType = error.error.split(':')[0];
      errorAnalysis.errorsByType[errorType] = (errorAnalysis.errorsByType[errorType] || 0) + 1;

      // Count critical errors (those affecting synchronization)
      if (error.method.includes('sync') || error.method.includes('update')) {
        errorAnalysis.criticalErrors++;
      }
    });

    return {
      errors: errors.map((error) => ({
        ...error,
        timeSince: Date.now() - error.timestamp,
        formattedTime: new Date(error.timestamp).toISOString(),
      })),
      analysis: errorAnalysis,
    };
  }

  /**
   * TASK 1.4: Validate all canvas instances
   * @returns {Object} Comprehensive validation results
   */
  validateAllCanvases() {
    const results = {
      totalCanvases: this.canvasSubscribers.size,
      validCanvases: 0,
      invalidCanvases: 0,
      warnings: 0,
      details: [],
      globalStateValid: true,
      recommendations: [],
    };

    // Validate global state
    if (!this.hasImportantLayers()) {
      results.recommendations.push('No important layers in global state - consider adding layers');
    }

    // Validate each canvas
    this.canvasSubscribers.forEach((canvas) => {
      const validation = this.validateCanvasState(canvas);
      const canvasId = this.getCanvasId(canvas);
      const metadata = this.subscriberMetadata.get(canvas);

      const detail = {
        canvasId,
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        syncCount: metadata ? metadata.syncCount : 0,
        lastSyncAge: metadata && metadata.lastSyncTime ? Date.now() - metadata.lastSyncTime : null,
      };

      results.details.push(detail);

      if (validation.isValid) {
        results.validCanvases++;
      } else {
        results.invalidCanvases++;
      }

      results.warnings += validation.warnings.length;

      // Add recommendations
      if (!validation.isValid) {
        results.recommendations.push(`Canvas ${canvasId} needs synchronization`);
      }

      if (detail.lastSyncAge && detail.lastSyncAge > 60000) {
        results.recommendations.push(`Canvas ${canvasId} hasn't synced in over 1 minute`);
      }
    });

    console.log('🌐 VALIDATION: Canvas validation completed', results);
    return results;
  }

  /**
   * TASK 1.4: Export state for debugging/support
   * @returns {string} JSON string of complete state
   */
  exportStateForDebugging() {
    const exportData = {
      version: '1.0.0',
      exportedAt: Date.now(),
      globalState: this.globalLayerState,
      history: this.stateHistory,
      errors: this.errorLog,
      performance: this.getPerformanceMetrics(),
      subscriberCount: this.canvasSubscribers.size,
      persistenceConfig: this.persistenceConfig,
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    console.log('🌐 EXPORT: State exported for debugging', {
      size: jsonString.length,
      timestamp: exportData.exportedAt,
    });

    return jsonString;
  }

  /**
   * TASK 1.4: Import state for debugging/testing
   * @param {string|Object} stateData - State data to import
   * @returns {boolean} Success status
   */
  importStateForDebugging(stateData) {
    try {
      const data = typeof stateData === 'string' ? JSON.parse(stateData) : stateData;

      if (!data.globalState) {
        throw new Error('Invalid state data - missing globalState');
      }

      // Backup current state
      const backup = this.exportStateForDebugging();

      // Import global state
      this.globalLayerState = {
        breed: data.globalState.breed,
        costume: data.globalState.costume,
        frame: data.globalState.frame,
        background: data.globalState.background,
        timestamp: data.globalState.timestamp || Date.now(),
        version: data.globalState.version || '1.0.0',
      };

      // Import history if available
      if (data.history && Array.isArray(data.history)) {
        this.stateHistory = data.history.slice(0, this.maxHistorySize);
      }

      // Sync to all subscribers
      this.forceGlobalSync();

      console.log('🌐 IMPORT: State imported successfully', {
        hasLayers: this.hasImportantLayers(),
        subscribers: this.canvasSubscribers.size,
        backup: backup.length,
      });

      return true;
    } catch (error) {
      this.logError('importStateForDebugging', error);
      return false;
    }
  }

  /**
   * TASK 1.4: Generate performance report
   * @returns {Object} Detailed performance analysis
   */
  generatePerformanceReport() {
    const metrics = this.getPerformanceMetrics();
    const now = Date.now();

    const report = {
      timestamp: now,
      summary: {
        totalOperations: metrics.updateCount + metrics.syncCount,
        averageOperationTime: (metrics.averageUpdateTime + metrics.averageSyncTime) / 2,
        memoryFootprint: metrics.memoryFootprint,
        subscriberCount: metrics.subscriberCount,
      },
      performance: {
        updates: {
          count: metrics.updateCount,
          averageTime: metrics.averageUpdateTime,
          status: metrics.averageUpdateTime < 10 ? 'GOOD' : 'NEEDS_ATTENTION',
        },
        synchronization: {
          count: metrics.syncCount,
          averageTime: metrics.averageSyncTime,
          status: metrics.averageSyncTime < 10 ? 'GOOD' : 'NEEDS_ATTENTION',
        },
        memory: {
          footprint: metrics.memoryFootprint,
          status: metrics.memoryFootprint < 2048 ? 'GOOD' : 'HIGH',
        },
      },
      recommendations: [],
    };

    // Add recommendations
    if (report.performance.updates.status === 'NEEDS_ATTENTION') {
      report.recommendations.push('Update operations are slow - consider optimizing layer data');
    }

    if (report.performance.synchronization.status === 'NEEDS_ATTENTION') {
      report.recommendations.push('Synchronization is slow - consider reducing subscriber count');
    }

    if (report.performance.memory.status === 'HIGH') {
      report.recommendations.push('Memory usage is high - consider clearing history or reducing state size');
    }

    if (metrics.errorCount > 0) {
      report.recommendations.push(`${metrics.errorCount} errors logged - review error log`);
    }

    console.log('🌐 PERFORMANCE: Report generated', report.summary);
    return report;
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LayerStateManager;
} else if (typeof window !== 'undefined') {
  window.LayerStateManager = LayerStateManager;
}
