/**
 * Universal Costume Selector Tests
 * Tests for the new CostumeSelector class with categories and enhanced functionality
 */

// Mock DOM environment
const { JSDOM } = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.document = dom.window.document;
global.window = dom.window;
global.HTMLElement = dom.window.HTMLElement;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Load the Royal Portrait Builder
const fs = require('fs');
const path = require('path');
const portraitBuilderCode = fs.readFileSync(path.join(__dirname, '../assets/royal-portrait-complete.js'), 'utf8');

// Execute the code in the test environment
eval(portraitBuilderCode);

describe('CostumeSelector Universal System', () => {
  let costumeSelector;
  let mockPortraitApp;
  let mockContainer;
  let CostumeSelector;

  beforeEach(() => {
    // Reset DOM
    document.body.innerHTML = '';

    // Get CostumeSelector class from global scope
    CostumeSelector = global.window.CostumeSelector || global.CostumeSelector;
    if (!CostumeSelector) {
      // Try to find it in the evaluated code
      const classMatch = portraitBuilderCode.match(/class CostumeSelector\s*{[\s\S]*?^}/m);
      if (classMatch) {
        eval(`global.CostumeSelector = ${classMatch[0]}`);
        CostumeSelector = global.CostumeSelector;
      }
    }

    // Create mock container with costume selector element
    mockContainer = document.createElement('div');
    mockContainer.innerHTML = `
      <div data-costume-selector>
        <!-- CostumeSelector will render here -->
      </div>
    `;
    document.body.appendChild(mockContainer);

    // Create mock portrait app
    mockPortraitApp = {
      container: mockContainer,
      canvas: {
        setCostumeLayer: jest.fn(),
        drawLayers: jest.fn(),
      },
      dynamicPricing: {
        updatePricing: jest.fn(),
      },
      selectCostume: jest.fn(),
      showCostumeRealTimePreview: jest.fn(),
    };

    // Mock analytics
    global.window.PortraitAnalyticsInstance = {
      trackCostumeSelected: jest.fn(),
      trackCostumeCategorySelected: jest.fn(),
    };

    // Create CostumeSelector instance
    if (CostumeSelector) {
      costumeSelector = new CostumeSelector(mockPortraitApp);
    }
  });

  afterEach(() => {
    document.body.removeChild(mockContainer);
    delete global.window.PortraitAnalyticsInstance;
  });

  describe('Initialization', () => {
    test('should initialize with correct costume data', () => {
      if (!CostumeSelector) {
        console.warn('CostumeSelector class not found, skipping test');
        return;
      }

      expect(costumeSelector.costumes).toBeDefined();
      expect(costumeSelector.costumes.length).toBeGreaterThan(0);
      expect(costumeSelector.categories).toEqual(['royal', 'casual', 'holiday', 'professional', 'fantasy']);
      expect(costumeSelector.activeCategory).toBe('royal');
    });

    test('should load costumes with proper structure', () => {
      const costume = costumeSelector.costumes[0];
      expect(costume).toHaveProperty('id');
      expect(costume).toHaveProperty('name');
      expect(costume).toHaveProperty('category');
      expect(costume).toHaveProperty('price');
      expect(costume).toHaveProperty('thumbnail');
      expect(costume).toHaveProperty('overlay');
      expect(costume).toHaveProperty('description');
      expect(costume).toHaveProperty('icon');
    });

    test('should initialize interface when init() is called', () => {
      costumeSelector.init();

      expect(costumeSelector.isInitialized).toBe(true);
      const selectorElement = mockContainer.querySelector('.costume-selector');
      expect(selectorElement).toBeTruthy();
    });
  });

  describe('Category Management', () => {
    beforeEach(() => {
      costumeSelector.init();
    });

    test('should render category buttons', () => {
      const categoryButtons = mockContainer.querySelectorAll('[data-costume-category]');
      expect(categoryButtons.length).toBe(5);

      const activeButton = mockContainer.querySelector('.costume-category--active');
      expect(activeButton.dataset.costumeCategory).toBe('royal');
    });

    test('should switch categories when button is clicked', () => {
      const casualButton = mockContainer.querySelector('[data-costume-category="casual"]');
      casualButton.click();

      expect(costumeSelector.activeCategory).toBe('casual');
      expect(casualButton.classList.contains('costume-category--active')).toBe(true);
    });

    test('should update costume grid when category changes', () => {
      const initialGrid = mockContainer.querySelector('[data-costume-grid]').innerHTML;

      costumeSelector.setActiveCategory('casual');

      const updatedGrid = mockContainer.querySelector('[data-costume-grid]').innerHTML;
      expect(updatedGrid).not.toBe(initialGrid);
    });

    test('should track category selection analytics', () => {
      costumeSelector.setActiveCategory('fantasy');

      expect(window.PortraitAnalyticsInstance.trackCostumeCategorySelected).toHaveBeenCalledWith('fantasy');
    });
  });

  describe('Costume Selection', () => {
    beforeEach(() => {
      costumeSelector.init();
    });

    test('should select costume when clicked', async () => {
      const costumeOption = mockContainer.querySelector('[data-costume-id]');
      const costumeId = costumeOption.dataset.costumeId;

      await costumeSelector.selectCostume(costumeId);

      expect(mockPortraitApp.selectCostume).toHaveBeenCalledWith(costumeId);
    });

    test('should update visual selection state', async () => {
      if (!CostumeSelector) {
        console.warn('CostumeSelector class not found, skipping test');
        return;
      }

      // Initialize the costume selector interface
      costumeSelector.init();

      const costumeId = 'crown'; // Use correct ID from costume data
      await costumeSelector.selectCostume(costumeId);

      const selectedOption = mockContainer.querySelector(`[data-costume-id="${costumeId}"]`);
      expect(selectedOption).not.toBeNull();
      expect(selectedOption.classList.contains('costume-option--selected')).toBe(true);
    });

    test('should handle "none" costume selection', async () => {
      await costumeSelector.selectCostume('none');

      expect(costumeSelector.selectedCostume).toBeNull();
      const noneOption = mockContainer.querySelector('[data-costume-id="none"]');
      expect(noneOption.classList.contains('costume-option--selected')).toBe(true);
    });

    test('should track costume selection analytics', async () => {
      if (!CostumeSelector) {
        console.warn('CostumeSelector class not found, skipping test');
        return;
      }

      await costumeSelector.selectCostume('crown'); // Use correct costume ID

      expect(window.PortraitAnalyticsInstance.trackCostumeSelected).toHaveBeenCalledWith('crown', 'royal');
    });

    test('should update pricing when costume is selected', async () => {
      await costumeSelector.selectCostume('royal-crown');

      expect(mockPortraitApp.dynamicPricing.updatePricing).toHaveBeenCalled();
    });
  });

  describe('Hover Preview', () => {
    beforeEach(() => {
      costumeSelector.init();
    });

    test('should show hover preview', async () => {
      await costumeSelector.showHoverPreview('royal-crown');

      expect(mockPortraitApp.showCostumeRealTimePreview).toHaveBeenCalledWith('royal-crown');

      const infoElement = mockContainer.querySelector('[data-costume-info]');
      expect(infoElement.innerHTML).toContain('Royal Crown');
    });

    test('should hide hover preview and restore original', async () => {
      costumeSelector.selectedCostume = { id: 'royal-cape', name: 'Royal Cape' };

      await costumeSelector.hideHoverPreview();

      expect(mockPortraitApp.showCostumeRealTimePreview).toHaveBeenCalledWith('royal-cape');
    });

    test('should show "none" costume info in preview', async () => {
      await costumeSelector.showHoverPreview('none');

      const infoElement = mockContainer.querySelector('[data-costume-info]');
      expect(infoElement.innerHTML).toContain('No Costume');
      expect(infoElement.innerHTML).toContain('$0.00');
    });
  });

  describe('Utility Methods', () => {
    test('should format category names correctly', () => {
      expect(costumeSelector.formatCategoryName('royal')).toBe('Royal');
      expect(costumeSelector.formatCategoryName('professional')).toBe('Professional');
    });

    test('should count costumes in category', () => {
      const royalCount = costumeSelector.getCategoryCount('royal');
      const royalCostumes = costumeSelector.costumes.filter((c) => c.category === 'royal');
      expect(royalCount).toBe(royalCostumes.length);
    });

    test('should get selected costume data', () => {
      const costume = { id: 'royal-crown', name: 'Royal Crown' };
      costumeSelector.selectedCostume = costume;

      expect(costumeSelector.getSelectedCostume()).toBe(costume);
    });

    test('should set selected costume for loading saved state', () => {
      costumeSelector.isInitialized = true;
      costumeSelector.init();

      costumeSelector.setSelectedCostume('royal-crown');

      const costume = costumeSelector.costumes.find((c) => c.id === 'royal-crown');
      expect(costumeSelector.selectedCostume).toEqual(costume);
    });
  });

  describe('Error Handling', () => {
    test('should handle missing costume container gracefully', () => {
      const emptyCostumeSelector = new CostumeSelector(mockPortraitApp);
      mockContainer.innerHTML = ''; // Remove costume selector container

      expect(() => emptyCostumeSelector.init()).not.toThrow();
      expect(emptyCostumeSelector.isInitialized).toBe(false);
    });

    test('should handle costume selection errors gracefully', async () => {
      mockPortraitApp.selectCostume.mockRejectedValue(new Error('Canvas error'));

      await expect(costumeSelector.selectCostume('royal-crown')).resolves.not.toThrow();
    });
  });
});
