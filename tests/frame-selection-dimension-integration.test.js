/**
 * Frame Selection with Dimension Changes - Integration Tests
 * Tests for frame selection workflow with canvas dimension changes
 * 
 * TASK 3.2: Integration tests for frame selection workflow with dimension changes
 */

// Mock DOM elements and browser APIs
global.HTMLCanvasElement = class MockHTMLCanvasElement {
  constructor() {
    this.width = 500;
    this.height = 500;
    this.style = { display: 'block' };
    this.getContext = jest.fn(() => ({
      fillStyle: '#ffffff',
      fillRect: jest.fn(),
      clearRect: jest.fn(),
      drawImage: jest.fn(),
      save: jest.fn(),
      restore: jest.fn(),
      scale: jest.fn(),
      translate: jest.fn(),
    }));
  }
};

global.document = {
  createElement: jest.fn(() => new HTMLCanvasElement()),
  querySelector: jest.fn(),
  querySelectorAll: jest.fn(() => []),
};

global.window = {
  devicePixelRatio: 1,
  performance: { now: () => Date.now() },
  PortraitAnalyticsInstance: {
    trackFrameSelected: jest.fn(),
    trackShopifyFunnelProgression: jest.fn(),
  },
};

// Import required classes
const { RoyalPortraitBuilderApp, PortraitCanvas, FrameLibrary } = require('../assets/royal-portrait-complete.js');

describe('Frame Selection with Dimension Changes Integration', () => {
  let portraitApp;
  let mockMainCanvas;
  let mockProgressiveCanvas;
  let mockContainer;

  beforeEach(() => {
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});

    // Create mock container
    mockContainer = {
      querySelector: jest.fn((selector) => {
        if (selector === '[data-portrait-canvas]') return mockMainCanvas;
        if (selector === '[data-progressive-canvas]') return mockProgressiveCanvas;
        return null;
      }),
      querySelectorAll: jest.fn(() => []),
    };

    // Create mock canvas elements
    mockMainCanvas = new HTMLCanvasElement();
    mockProgressiveCanvas = new HTMLCanvasElement();

    // Create portrait app instance
    portraitApp = new RoyalPortraitBuilderApp(mockContainer);
    
    // Initialize canvases manually for testing
    portraitApp.canvas = new PortraitCanvas(mockMainCanvas);
    portraitApp.progressiveCanvas = new PortraitCanvas(mockProgressiveCanvas);
    
    // Mock frame library
    portraitApp.frameLibrary = new FrameLibrary();
    
    // Add some test layers to simulate real usage
    portraitApp.canvas.layers.breed = { id: 'golden-retriever', name: 'Golden Retriever' };
    portraitApp.canvas.layers.costume = { id: 'crown', name: 'Royal Crown' };
    portraitApp.progressiveCanvas.layers.breed = { id: 'golden-retriever', name: 'Golden Retriever' };
    portraitApp.progressiveCanvas.layers.costume = { id: 'crown', name: 'Royal Crown' };
  });

  afterEach(() => {
    // Restore console methods
    console.log.mockRestore();
    console.warn.mockRestore();
    console.error.mockRestore();
  });

  describe('Frame Selection with Stable Dimensions', () => {
    test('should complete frame selection without clearing layers', async () => {
      // Mock frame selector
      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          // Simulate frame selection process
          const frame = { id: frameId, name: 'Classic Frame', base_price: 25 };
          portraitApp.state.selectedFrame = frame;
          
          // Update canvas layers
          if (portraitApp.canvas) {
            portraitApp.canvas.layers.frame = frame;
          }
          if (portraitApp.progressiveCanvas) {
            portraitApp.progressiveCanvas.layers.frame = frame;
          }
          
          return frame;
        })
      };

      // Mock canvas clearing to track calls
      const mainCanvasClearSpy = jest.spyOn(portraitApp.canvas, 'clearCanvas');
      const progressiveClearSpy = jest.spyOn(portraitApp.progressiveCanvas, 'clearCanvas');

      // Execute frame selection
      await portraitApp.frameSelector.selectFrame('classic');

      // Verify frame was selected
      expect(portraitApp.state.selectedFrame).toEqual({
        id: 'classic',
        name: 'Classic Frame',
        base_price: 25
      });

      // Verify layers were preserved (canvas not cleared)
      expect(mainCanvasClearSpy).not.toHaveBeenCalled();
      expect(progressiveClearSpy).not.toHaveBeenCalled();

      // Verify frame layer was added
      expect(portraitApp.canvas.layers.frame).toBeDefined();
      expect(portraitApp.progressiveCanvas.layers.frame).toBeDefined();

      mainCanvasClearSpy.mockRestore();
      progressiveClearSpy.mockRestore();
    });
  });

  describe('Frame Selection with Dimension Changes', () => {
    test('should preserve layers when dimensions change during frame selection', async () => {
      // Mock frame selector with dimension change simulation
      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          // Simulate dimension change during frame selection
          mockMainCanvas.width = 600;
          mockMainCanvas.height = 400;
          mockProgressiveCanvas.width = 600;
          mockProgressiveCanvas.height = 400;
          
          const frame = { id: frameId, name: 'Modern Frame', base_price: 30 };
          portraitApp.state.selectedFrame = frame;
          
          // Trigger canvas hide/show cycle (simulates real workflow)
          portraitApp.canvas.hideCanvas();
          portraitApp.progressiveCanvas.hideProgressiveCanvas();
          
          // Update layers
          portraitApp.canvas.layers.frame = frame;
          portraitApp.progressiveCanvas.layers.frame = frame;
          
          return frame;
        })
      };

      // Mock canvas clearing to track calls
      const mainCanvasClearSpy = jest.spyOn(portraitApp.canvas, 'clearCanvas');
      const progressiveClearSpy = jest.spyOn(portraitApp.progressiveCanvas, 'clearCanvas');

      // Execute frame selection with dimension changes
      await portraitApp.frameSelector.selectFrame('modern');

      // Verify frame was selected
      expect(portraitApp.state.selectedFrame.id).toBe('modern');

      // Verify layers were preserved due to dimension changes
      expect(mainCanvasClearSpy).not.toHaveBeenCalled();
      expect(progressiveClearSpy).not.toHaveBeenCalled();

      // Verify dimension tracking was updated
      expect(portraitApp.canvas.lastKnownDimensions.width).toBe(600);
      expect(portraitApp.canvas.lastKnownDimensions.height).toBe(400);

      mainCanvasClearSpy.mockRestore();
      progressiveClearSpy.mockRestore();
    });

    test('should handle multiple dimension changes during frame selection', async () => {
      let dimensionChangeCount = 0;
      
      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          // Simulate multiple dimension changes
          mockMainCanvas.width = 300 + (dimensionChangeCount * 100);
          mockMainCanvas.height = 300 + (dimensionChangeCount * 100);
          dimensionChangeCount++;
          
          // Trigger hide/show multiple times
          portraitApp.canvas.hideCanvas();
          portraitApp.canvas.showCanvas();
          portraitApp.canvas.hideCanvas();
          
          const frame = { id: frameId, name: 'Ornate Frame', base_price: 35 };
          portraitApp.state.selectedFrame = frame;
          portraitApp.canvas.layers.frame = frame;
          
          return frame;
        })
      };

      const clearSpy = jest.spyOn(portraitApp.canvas, 'clearCanvas');

      // Execute frame selection
      await portraitApp.frameSelector.selectFrame('ornate');

      // Verify layers preserved through multiple dimension changes
      expect(clearSpy).not.toHaveBeenCalled();
      expect(portraitApp.canvas.layers.breed).toBeDefined();
      expect(portraitApp.canvas.layers.costume).toBeDefined();
      expect(portraitApp.canvas.layers.frame).toBeDefined();

      clearSpy.mockRestore();
    });
  });

  describe('Performance During Frame Selection', () => {
    test('should complete frame selection within performance requirements', async () => {
      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          // Simulate realistic frame selection with dimension tracking
          const startTime = performance.now();
          
          // Simulate dimension changes
          mockMainCanvas.width = 550;
          mockProgressiveCanvas.width = 550;
          
          // Simulate canvas operations
          portraitApp.canvas.hideCanvas();
          portraitApp.canvas.updateDimensionTracking();
          portraitApp.progressiveCanvas.hideProgressiveCanvas();
          portraitApp.progressiveCanvas.updateDimensionTracking();
          
          const frame = { id: frameId, name: 'Performance Frame', base_price: 25 };
          portraitApp.state.selectedFrame = frame;
          
          const endTime = performance.now();
          const executionTime = endTime - startTime;
          
          // Should complete within 50ms (performance requirement)
          expect(executionTime).toBeLessThan(50);
          
          return frame;
        })
      };

      const startTime = performance.now();
      await portraitApp.frameSelector.selectFrame('performance');
      const totalTime = performance.now() - startTime;

      // Total frame selection should complete within 500ms
      expect(totalTime).toBeLessThan(500);
    });

    test('should handle rapid dimension changes without performance degradation', async () => {
      const dimensionChanges = [];
      
      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          const startTime = performance.now();
          
          // Simulate rapid dimension changes
          for (let i = 0; i < 10; i++) {
            mockMainCanvas.width = 500 + i * 10;
            mockMainCanvas.height = 500 + i * 10;
            
            const changeStart = performance.now();
            const changed = portraitApp.canvas.dimensionsChanged();
            portraitApp.canvas.updateDimensionTracking();
            const changeEnd = performance.now();
            
            dimensionChanges.push(changeEnd - changeStart);
          }
          
          const frame = { id: frameId, name: 'Rapid Frame', base_price: 25 };
          portraitApp.state.selectedFrame = frame;
          
          const endTime = performance.now();
          return { frame, totalTime: endTime - startTime };
        })
      };

      const result = await portraitApp.frameSelector.selectFrame('rapid');

      // Each dimension change should be <5ms
      dimensionChanges.forEach(changeTime => {
        expect(changeTime).toBeLessThan(5);
      });

      // Total operation should still be fast
      expect(result.totalTime).toBeLessThan(100);
    });
  });

  describe('Canvas State Synchronization', () => {
    test('should keep main and progressive canvas synchronized during frame selection', async () => {
      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          // Change dimensions on both canvases
          mockMainCanvas.width = 400;
          mockMainCanvas.height = 400;
          mockProgressiveCanvas.width = 400;
          mockProgressiveCanvas.height = 400;
          
          // Trigger operations on both canvases
          portraitApp.canvas.hideCanvas();
          portraitApp.progressiveCanvas.hideProgressiveCanvas();
          
          const frame = { id: frameId, name: 'Sync Frame', base_price: 25 };
          portraitApp.state.selectedFrame = frame;
          
          // Add frame to both canvases
          portraitApp.canvas.layers.frame = frame;
          portraitApp.progressiveCanvas.layers.frame = frame;
          
          return frame;
        })
      };

      await portraitApp.frameSelector.selectFrame('sync');

      // Verify both canvases have synchronized state
      expect(portraitApp.canvas.lastKnownDimensions.width).toBe(400);
      expect(portraitApp.progressiveCanvas.lastKnownDimensions.width).toBe(400);
      
      expect(portraitApp.canvas.layers.frame).toEqual(portraitApp.progressiveCanvas.layers.frame);
      expect(portraitApp.canvas.layers.breed).toEqual(portraitApp.progressiveCanvas.layers.breed);
      expect(portraitApp.canvas.layers.costume).toEqual(portraitApp.progressiveCanvas.layers.costume);
    });
  });

  describe('Error Handling', () => {
    test('should handle dimension tracking errors gracefully', async () => {
      // Mock dimension tracking to throw error
      const originalDimensionsChanged = portraitApp.canvas.dimensionsChanged;
      portraitApp.canvas.dimensionsChanged = jest.fn(() => {
        throw new Error('Dimension tracking error');
      });

      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          // This should not throw despite dimension tracking error
          portraitApp.canvas.hideCanvas();
          
          const frame = { id: frameId, name: 'Error Frame', base_price: 25 };
          portraitApp.state.selectedFrame = frame;
          return frame;
        })
      };

      // Should not throw error
      await expect(portraitApp.frameSelector.selectFrame('error')).resolves.toBeDefined();

      // Restore original method
      portraitApp.canvas.dimensionsChanged = originalDimensionsChanged;
    });

    test('should fallback to layer-based preservation when dimension tracking fails', async () => {
      // Disable dimension tracking
      portraitApp.canvas.dimensionTrackingEnabled = false;
      portraitApp.progressiveCanvas.dimensionTrackingEnabled = false;

      portraitApp.frameSelector = {
        isSelectionInProgress: false,
        selectFrame: jest.fn(async (frameId) => {
          const clearSpy = jest.spyOn(portraitApp.canvas, 'clearCanvas');
          
          // Change dimensions (but tracking is disabled)
          mockMainCanvas.width = 600;
          
          // Should still preserve layers based on layer existence
          portraitApp.canvas.hideCanvas();
          
          // Should not clear because layers exist
          expect(clearSpy).not.toHaveBeenCalled();
          
          const frame = { id: frameId, name: 'Fallback Frame', base_price: 25 };
          portraitApp.state.selectedFrame = frame;
          
          clearSpy.mockRestore();
          return frame;
        })
      };

      await portraitApp.frameSelector.selectFrame('fallback');
      expect(portraitApp.state.selectedFrame.id).toBe('fallback');
    });
  });
});
