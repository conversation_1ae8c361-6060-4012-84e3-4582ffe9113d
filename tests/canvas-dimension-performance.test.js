/**
 * Canvas Dimension Tracking - Performance Validation Tests
 * 
 * TASK 3.4: Performance validation testing to ensure <5ms overhead
 * 
 * These tests validate that the dimension tracking implementation meets
 * all performance requirements and doesn't introduce significant overhead.
 */

// Mock performance API for Node.js environment
if (typeof performance === 'undefined') {
  global.performance = {
    now: () => Date.now()
  };
}

// Mock canvas element for testing
class MockCanvas {
  constructor(width = 500, height = 500) {
    this.width = width;
    this.height = height;
    this.style = { display: 'block' };
  }

  getContext() {
    return {
      fillStyle: '#ffffff',
      fillRect: jest.fn(),
      clearRect: jest.fn(),
      save: jest.fn(),
      restore: jest.fn(),
    };
  }
}

// Import the PortraitCanvas class
const { PortraitCanvas } = require('../assets/royal-portrait-complete.js');

describe('Canvas Dimension Tracking Performance', () => {
  let mockCanvas;
  let portraitCanvas;

  beforeEach(() => {
    // Create fresh mock canvas for each test
    mockCanvas = new MockCanvas(500, 500);
    
    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});

    // Create PortraitCanvas instance
    portraitCanvas = new PortraitCanvas(mockCanvas);
  });

  afterEach(() => {
    // Restore console methods
    console.log.mockRestore();
    console.warn.mockRestore();
    console.error.mockRestore();
  });

  describe('Individual Method Performance', () => {
    test('dimensionsChanged() should execute in <1ms', () => {
      const iterations = 1000;
      const measurements = [];

      // Warm up
      for (let i = 0; i < 100; i++) {
        portraitCanvas.dimensionsChanged();
      }

      // Measure performance
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        portraitCanvas.dimensionsChanged();
        const endTime = performance.now();
        measurements.push(endTime - startTime);
      }

      const averageTime = measurements.reduce((sum, time) => sum + time, 0) / measurements.length;
      const maxTime = Math.max(...measurements);
      const minTime = Math.min(...measurements);

      console.log(`dimensionsChanged() Performance:
        Average: ${averageTime.toFixed(3)}ms
        Min: ${minTime.toFixed(3)}ms
        Max: ${maxTime.toFixed(3)}ms
        Iterations: ${iterations}`);

      // Performance requirements
      expect(averageTime).toBeLessThan(1); // <1ms average
      expect(maxTime).toBeLessThan(5); // <5ms worst case
    });

    test('updateDimensionTracking() should execute in <5ms', () => {
      const iterations = 1000;
      const measurements = [];

      // Warm up
      for (let i = 0; i < 100; i++) {
        portraitCanvas.updateDimensionTracking();
      }

      // Measure performance
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        portraitCanvas.updateDimensionTracking();
        const endTime = performance.now();
        measurements.push(endTime - startTime);
      }

      const averageTime = measurements.reduce((sum, time) => sum + time, 0) / measurements.length;
      const maxTime = Math.max(...measurements);

      console.log(`updateDimensionTracking() Performance:
        Average: ${averageTime.toFixed(3)}ms
        Max: ${maxTime.toFixed(3)}ms
        Iterations: ${iterations}`);

      // Performance requirements
      expect(averageTime).toBeLessThan(5); // <5ms average
      expect(maxTime).toBeLessThan(10); // <10ms worst case
    });

    test('hideCanvas() with dimension tracking should have minimal overhead', () => {
      const iterations = 500;
      const withTrackingTimes = [];
      const withoutTrackingTimes = [];

      // Test with dimension tracking enabled
      portraitCanvas.dimensionTrackingEnabled = true;
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        portraitCanvas.hideCanvas();
        const endTime = performance.now();
        withTrackingTimes.push(endTime - startTime);
      }

      // Test with dimension tracking disabled
      portraitCanvas.dimensionTrackingEnabled = false;
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        portraitCanvas.hideCanvas();
        const endTime = performance.now();
        withoutTrackingTimes.push(endTime - startTime);
      }

      const avgWithTracking = withTrackingTimes.reduce((sum, time) => sum + time, 0) / withTrackingTimes.length;
      const avgWithoutTracking = withoutTrackingTimes.reduce((sum, time) => sum + time, 0) / withoutTrackingTimes.length;
      const overhead = avgWithTracking - avgWithoutTracking;

      console.log(`hideCanvas() Performance Comparison:
        With tracking: ${avgWithTracking.toFixed(3)}ms
        Without tracking: ${avgWithoutTracking.toFixed(3)}ms
        Overhead: ${overhead.toFixed(3)}ms`);

      // Overhead should be minimal
      expect(overhead).toBeLessThan(5); // <5ms overhead
      expect(avgWithTracking).toBeLessThan(20); // Total time should be reasonable
    });
  });

  describe('Memory Performance', () => {
    test('dimension tracking should use minimal memory', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      const canvases = [];

      // Create many canvas instances to test memory usage
      for (let i = 0; i < 1000; i++) {
        const canvas = new MockCanvas(500, 500);
        canvases.push(new PortraitCanvas(canvas));
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryPerCanvas = memoryIncrease / 1000;

      console.log(`Memory Usage:
        Initial: ${(initialMemory / 1024 / 1024).toFixed(2)}MB
        Final: ${(finalMemory / 1024 / 1024).toFixed(2)}MB
        Increase: ${(memoryIncrease / 1024).toFixed(2)}KB
        Per Canvas: ${memoryPerCanvas.toFixed(0)} bytes`);

      // Memory requirements
      expect(memoryPerCanvas).toBeLessThan(1024); // <1KB per canvas
    });

    test('dimension tracking data should not grow unbounded', () => {
      const initialSize = JSON.stringify(portraitCanvas.lastKnownDimensions).length;

      // Perform many dimension updates
      for (let i = 0; i < 1000; i++) {
        mockCanvas.width = 500 + (i % 100);
        mockCanvas.height = 500 + (i % 100);
        portraitCanvas.updateDimensionTracking();
      }

      const finalSize = JSON.stringify(portraitCanvas.lastKnownDimensions).length;

      console.log(`Dimension Data Size:
        Initial: ${initialSize} bytes
        Final: ${finalSize} bytes`);

      // Size should remain constant (not grow with updates)
      expect(finalSize).toBeLessThanOrEqual(initialSize + 50); // Allow small variance
    });
  });

  describe('Concurrent Performance', () => {
    test('multiple canvas instances should not interfere with performance', async () => {
      const canvasCount = 10;
      const canvases = [];

      // Create multiple canvas instances
      for (let i = 0; i < canvasCount; i++) {
        const canvas = new MockCanvas(500 + i, 500 + i);
        canvases.push(new PortraitCanvas(canvas));
      }

      // Test concurrent dimension checking
      const startTime = performance.now();
      
      const promises = canvases.map(async (canvas, index) => {
        return new Promise(resolve => {
          setTimeout(() => {
            const checkStart = performance.now();
            
            // Perform dimension operations
            canvas.canvas.width = 600 + index;
            const changed = canvas.dimensionsChanged();
            canvas.updateDimensionTracking();
            
            const checkEnd = performance.now();
            resolve(checkEnd - checkStart);
          }, index * 10); // Stagger operations
        });
      });

      const results = await Promise.all(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const averageOperationTime = results.reduce((sum, time) => sum + time, 0) / results.length;
      const maxOperationTime = Math.max(...results);

      console.log(`Concurrent Performance:
        Total time: ${totalTime.toFixed(3)}ms
        Average operation: ${averageOperationTime.toFixed(3)}ms
        Max operation: ${maxOperationTime.toFixed(3)}ms
        Canvas count: ${canvasCount}`);

      // Performance should not degrade significantly with multiple instances
      expect(averageOperationTime).toBeLessThan(10); // <10ms per operation
      expect(maxOperationTime).toBeLessThan(20); // <20ms worst case
    });
  });

  describe('Real-world Scenario Performance', () => {
    test('frame selection workflow should complete within performance requirements', () => {
      // Simulate real frame selection workflow
      const startTime = performance.now();

      // Add layers (simulate real usage)
      portraitCanvas.layers.breed = { id: 'golden-retriever', name: 'Golden Retriever' };
      portraitCanvas.layers.costume = { id: 'crown', name: 'Royal Crown' };

      // Simulate dimension changes during frame selection
      mockCanvas.width = 600;
      mockCanvas.height = 400;

      // Simulate canvas operations
      const dimensionCheck1 = performance.now();
      const changed1 = portraitCanvas.dimensionsChanged();
      const dimensionCheck2 = performance.now();

      portraitCanvas.hideCanvas();
      const hideComplete = performance.now();

      portraitCanvas.updateDimensionTracking();
      const trackingComplete = performance.now();

      // Add frame layer
      portraitCanvas.layers.frame = { id: 'classic', name: 'Classic Frame' };

      const endTime = performance.now();

      const totalTime = endTime - startTime;
      const dimensionCheckTime = dimensionCheck2 - dimensionCheck1;
      const hideTime = hideComplete - dimensionCheck2;
      const trackingTime = trackingComplete - hideComplete;

      console.log(`Frame Selection Workflow Performance:
        Total time: ${totalTime.toFixed(3)}ms
        Dimension check: ${dimensionCheckTime.toFixed(3)}ms
        Hide canvas: ${hideTime.toFixed(3)}ms
        Update tracking: ${trackingTime.toFixed(3)}ms`);

      // Performance requirements for frame selection
      expect(totalTime).toBeLessThan(50); // <50ms total
      expect(dimensionCheckTime).toBeLessThan(5); // <5ms for dimension check
      expect(hideTime).toBeLessThan(20); // <20ms for hide operation
      expect(trackingTime).toBeLessThan(5); // <5ms for tracking update
    });

    test('rapid dimension changes should not cause performance degradation', () => {
      const changeCount = 100;
      const measurements = [];

      // Perform rapid dimension changes
      for (let i = 0; i < changeCount; i++) {
        const startTime = performance.now();
        
        // Change dimensions
        mockCanvas.width = 500 + (i % 50);
        mockCanvas.height = 500 + (i % 50);
        
        // Check and update
        const changed = portraitCanvas.dimensionsChanged();
        portraitCanvas.updateDimensionTracking();
        
        const endTime = performance.now();
        measurements.push(endTime - startTime);
      }

      const averageTime = measurements.reduce((sum, time) => sum + time, 0) / measurements.length;
      const maxTime = Math.max(...measurements);
      const minTime = Math.min(...measurements);

      // Check for performance degradation over time
      const firstHalf = measurements.slice(0, changeCount / 2);
      const secondHalf = measurements.slice(changeCount / 2);
      const firstHalfAvg = firstHalf.reduce((sum, time) => sum + time, 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, time) => sum + time, 0) / secondHalf.length;
      const degradation = secondHalfAvg - firstHalfAvg;

      console.log(`Rapid Changes Performance:
        Average: ${averageTime.toFixed(3)}ms
        Min: ${minTime.toFixed(3)}ms
        Max: ${maxTime.toFixed(3)}ms
        First half avg: ${firstHalfAvg.toFixed(3)}ms
        Second half avg: ${secondHalfAvg.toFixed(3)}ms
        Degradation: ${degradation.toFixed(3)}ms`);

      // Performance should remain consistent
      expect(averageTime).toBeLessThan(5); // <5ms average
      expect(Math.abs(degradation)).toBeLessThan(2); // <2ms degradation
    });
  });

  describe('Performance Regression Detection', () => {
    test('should detect if performance regresses beyond acceptable limits', () => {
      // Baseline performance measurement
      const baselineIterations = 1000;
      const baselineTimes = [];

      for (let i = 0; i < baselineIterations; i++) {
        const startTime = performance.now();
        portraitCanvas.dimensionsChanged();
        const endTime = performance.now();
        baselineTimes.push(endTime - startTime);
      }

      const baselineAverage = baselineTimes.reduce((sum, time) => sum + time, 0) / baselineTimes.length;

      // Simulate potential performance regression
      const regressionTimes = [];
      for (let i = 0; i < baselineIterations; i++) {
        const startTime = performance.now();
        
        // Add some artificial complexity (simulating regression)
        for (let j = 0; j < 10; j++) {
          Math.random(); // Minimal overhead
        }
        
        portraitCanvas.dimensionsChanged();
        const endTime = performance.now();
        regressionTimes.push(endTime - startTime);
      }

      const regressionAverage = regressionTimes.reduce((sum, time) => sum + time, 0) / regressionTimes.length;
      const performanceRatio = regressionAverage / baselineAverage;

      console.log(`Performance Regression Test:
        Baseline average: ${baselineAverage.toFixed(3)}ms
        Regression average: ${regressionAverage.toFixed(3)}ms
        Performance ratio: ${performanceRatio.toFixed(2)}x`);

      // Performance should not regress significantly
      expect(performanceRatio).toBeLessThan(2); // <2x slower than baseline
      expect(regressionAverage).toBeLessThan(5); // Still <5ms absolute
    });
  });
});
