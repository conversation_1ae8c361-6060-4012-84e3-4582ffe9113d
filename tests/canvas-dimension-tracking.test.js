/**
 * Canvas Dimension Tracking Unit Tests
 * Tests for dimension change detection and layer preservation logic
 * 
 * TASK 3.1: Comprehensive unit tests for dimensionsChanged() method
 */

// Mock canvas element for testing
class MockCanvas {
  constructor(width = 500, height = 500) {
    this.width = width;
    this.height = height;
    this.style = { display: 'block' };
  }

  getContext() {
    return {
      fillStyle: '#ffffff',
      fillRect: jest.fn(),
      clearRect: jest.fn(),
      save: jest.fn(),
      restore: jest.fn(),
    };
  }
}

// Import the PortraitCanvas class
const { PortraitCanvas } = require('../assets/royal-portrait-complete.js');

describe('Canvas Dimension Tracking', () => {
  let mockCanvas;
  let portraitCanvas;

  beforeEach(() => {
    // Create fresh mock canvas for each test
    mockCanvas = new MockCanvas(500, 500);
    
    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});

    // Create PortraitCanvas instance
    portraitCanvas = new PortraitCanvas(mockCanvas);
  });

  afterEach(() => {
    // Restore console methods
    console.log.mockRestore();
    console.warn.mockRestore();
    console.error.mockRestore();
  });

  describe('Dimension Tracking Initialization', () => {
    test('should initialize dimension tracking properties', () => {
      expect(portraitCanvas.lastKnownDimensions).toBeDefined();
      expect(portraitCanvas.dimensionTrackingEnabled).toBe(true);
    });

    test('should record initial dimensions during initialization', () => {
      expect(portraitCanvas.lastKnownDimensions).toEqual({
        width: 500,
        height: 500,
        timestamp: expect.any(Number)
      });
    });

    test('should handle canvas with zero dimensions', () => {
      const zeroCanvas = new MockCanvas(0, 0);
      const zeroPortraitCanvas = new PortraitCanvas(zeroCanvas);
      
      expect(zeroPortraitCanvas.lastKnownDimensions).toEqual({
        width: 500, // Should be set to config.canvasSize
        height: 500,
        timestamp: expect.any(Number)
      });
    });
  });

  describe('dimensionsChanged() Method', () => {
    test('should return false when dimensions have not changed', () => {
      // Dimensions should be the same as initialization
      expect(portraitCanvas.dimensionsChanged()).toBe(false);
    });

    test('should return true when width changes significantly', () => {
      // Change canvas width
      mockCanvas.width = 600;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(true);
    });

    test('should return true when height changes significantly', () => {
      // Change canvas height
      mockCanvas.height = 600;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(true);
    });

    test('should return true when both dimensions change', () => {
      // Change both dimensions
      mockCanvas.width = 300;
      mockCanvas.height = 300;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(true);
    });

    test('should handle tolerance for minor changes (within 2px)', () => {
      // Change dimensions by 1px (within tolerance)
      mockCanvas.width = 501;
      mockCanvas.height = 501;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(false);
    });

    test('should detect changes beyond tolerance (>2px)', () => {
      // Change dimensions by 3px (beyond tolerance)
      mockCanvas.width = 503;
      mockCanvas.height = 503;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(true);
    });

    test('should return false when tracking is disabled', () => {
      portraitCanvas.dimensionTrackingEnabled = false;
      mockCanvas.width = 600;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(false);
    });

    test('should return false when no previous dimensions recorded', () => {
      portraitCanvas.lastKnownDimensions = null;
      mockCanvas.width = 600;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(false);
    });

    test('should handle negative dimension changes', () => {
      // Change to smaller dimensions
      mockCanvas.width = 400;
      mockCanvas.height = 400;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(true);
    });
  });

  describe('updateDimensionTracking() Method', () => {
    test('should update lastKnownDimensions with current canvas dimensions', () => {
      const beforeTimestamp = Date.now();
      
      // Change canvas dimensions
      mockCanvas.width = 600;
      mockCanvas.height = 400;
      
      // Update tracking
      portraitCanvas.updateDimensionTracking();
      
      expect(portraitCanvas.lastKnownDimensions.width).toBe(600);
      expect(portraitCanvas.lastKnownDimensions.height).toBe(400);
      expect(portraitCanvas.lastKnownDimensions.timestamp).toBeGreaterThanOrEqual(beforeTimestamp);
    });

    test('should not update when tracking is disabled', () => {
      const originalDimensions = { ...portraitCanvas.lastKnownDimensions };
      
      portraitCanvas.dimensionTrackingEnabled = false;
      mockCanvas.width = 600;
      
      portraitCanvas.updateDimensionTracking();
      
      expect(portraitCanvas.lastKnownDimensions).toEqual(originalDimensions);
    });

    test('should handle canvas being null', () => {
      portraitCanvas.canvas = null;
      
      expect(() => {
        portraitCanvas.updateDimensionTracking();
      }).not.toThrow();
    });
  });

  describe('Performance Requirements', () => {
    test('dimensionsChanged() should execute in <1ms', () => {
      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        portraitCanvas.dimensionsChanged();
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      expect(averageTime).toBeLessThan(1); // <1ms per call
    });

    test('updateDimensionTracking() should execute quickly', () => {
      const startTime = performance.now();
      
      portraitCanvas.updateDimensionTracking();
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(executionTime).toBeLessThan(5); // <5ms per call
    });
  });

  describe('Memory Usage', () => {
    test('dimension tracking should use minimal memory', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Create multiple canvas instances
      const canvases = [];
      for (let i = 0; i < 100; i++) {
        const canvas = new MockCanvas(500, 500);
        canvases.push(new PortraitCanvas(canvas));
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryPerCanvas = memoryIncrease / 100;
      
      // Should use less than 1KB per canvas instance
      expect(memoryPerCanvas).toBeLessThan(1024);
    });
  });

  describe('Edge Cases', () => {
    test('should handle extremely large dimensions', () => {
      mockCanvas.width = 10000;
      mockCanvas.height = 10000;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(true);
      
      portraitCanvas.updateDimensionTracking();
      expect(portraitCanvas.lastKnownDimensions.width).toBe(10000);
      expect(portraitCanvas.lastKnownDimensions.height).toBe(10000);
    });

    test('should handle zero dimensions', () => {
      mockCanvas.width = 0;
      mockCanvas.height = 0;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(true);
      
      portraitCanvas.updateDimensionTracking();
      expect(portraitCanvas.lastKnownDimensions.width).toBe(0);
      expect(portraitCanvas.lastKnownDimensions.height).toBe(0);
    });

    test('should handle fractional dimensions', () => {
      mockCanvas.width = 500.5;
      mockCanvas.height = 500.7;
      
      expect(portraitCanvas.dimensionsChanged()).toBe(false); // Within tolerance
      
      mockCanvas.width = 503.5;
      expect(portraitCanvas.dimensionsChanged()).toBe(true); // Beyond tolerance
    });
  });

  describe('Integration with Layer Preservation', () => {
    test('should preserve layers when dimensions change', () => {
      // Add some layers
      portraitCanvas.layers.breed = { id: 'golden-retriever' };
      portraitCanvas.layers.costume = { id: 'crown' };
      
      // Change dimensions
      mockCanvas.width = 600;
      
      // Mock clearCanvas to track if it's called
      const clearCanvasSpy = jest.spyOn(portraitCanvas, 'clearCanvas');
      
      // Hide canvas (should preserve due to dimension change)
      portraitCanvas.hideCanvas();
      
      // Canvas should not be cleared due to dimension change
      expect(clearCanvasSpy).not.toHaveBeenCalled();
      
      clearCanvasSpy.mockRestore();
    });

    test('should clear canvas when no layers and dimensions stable', () => {
      // No layers
      portraitCanvas.layers = {
        breed: null,
        costume: null,
        frame: null,
        background: null
      };
      
      // Dimensions unchanged
      expect(portraitCanvas.dimensionsChanged()).toBe(false);
      
      // Mock clearCanvas to track if it's called
      const clearCanvasSpy = jest.spyOn(portraitCanvas, 'clearCanvas');
      
      // Hide canvas (should clear due to no layers and stable dimensions)
      portraitCanvas.hideCanvas();
      
      // Canvas should be cleared
      expect(clearCanvasSpy).toHaveBeenCalled();
      
      clearCanvasSpy.mockRestore();
    });
  });
});
