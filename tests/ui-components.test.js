/**
 * Unit tests for UI components
 * Tests breed picker, photo upload, frame/size selectors
 */

// Mock DOM elements
const mockCanvasElement = {
  getContext: jest.fn(() => ({
    clearRect: jest.fn(),
    drawImage: jest.fn(),
    fillRect: jest.fn(),
  })),
  width: 500,
  height: 500,
  toDataURL: jest.fn(() => 'data:image/png;base64,test'),
};

const mockContainer = {
  querySelector: jest.fn((selector) => {
    if (selector === '[data-portrait-canvas]') {
      return mockCanvasElement;
    }
    return mockElement;
  }),
  querySelectorAll: jest.fn(),
};

const mockElement = {
  addEventListener: jest.fn(),
  classList: {
    add: jest.fn(),
    remove: jest.fn(),
  },
  style: {},
  innerHTML: '',
  textContent: '',
  dataset: {},
  disabled: false,
};

// Mock Analytics
const mockAnalytics = {
  trackPortraitBuilderStart: jest.fn(),
  trackBreedSelected: jest.fn(),
  trackPhotoUploaded: jest.fn(),
  trackCostumeSelected: jest.fn(),
  trackFrameSelected: jest.fn(),
  trackSizeSelected: jest.fn(),
  trackAddToCart: jest.fn(),
  trackShopifyFunnelProgression: jest.fn(),
  trackShopifyConversion: jest.fn(),
};

// Mock BreedLibrary and FrameLibrary
const mockBreedLibrary = {
  loadBreeds: jest.fn(() => Promise.resolve([])),
  getAllBreeds: jest.fn(() => []),
  searchBreeds: jest.fn(() => []),
  getBreedByName: jest.fn(() => null),
  getAssetUrl: jest.fn((filename) => `/assets/breed-${filename}`),
};

const mockFrameLibrary = {
  loadFrames: jest.fn(() => Promise.resolve([])),
  getAllFrames: jest.fn(() => []),
  getFrameByName: jest.fn(() => null),
  getAssetUrl: jest.fn((filename) => `/assets/frame-${filename}`),
  calculateFramePrice: jest.fn(() => 0),
};

const mockCanvas = {
  drawBreedLayer: jest.fn(),
  drawUploadLayer: jest.fn(),
  drawFrameLayer: jest.fn(),
  generateThumbnail: jest.fn(() => 'data:image/png;base64,test'),
  reset: jest.fn(),
};

// Mock the RoyalPortraitBuilderApp class
// Fix: Use correct import path (recurring issue from debugging analysis)
const { RoyalPortraitBuilderApp } = require('../assets/royal-portrait-complete.js');

describe('RoyalPortraitBuilderApp UI Components', () => {
  let app;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup global analytics mock
    global.window = global.window || {};
    global.window.PortraitAnalyticsInstance = mockAnalytics;

    // Setup mock container to return mock elements
    mockContainer.querySelector.mockReturnValue(mockElement);
    mockContainer.querySelectorAll.mockReturnValue([mockElement]);

    // Create app instance with mocked dependencies
    app = new RoyalPortraitBuilderApp(mockContainer);
    app.breedLibrary = mockBreedLibrary;
    app.frameLibrary = mockFrameLibrary;
    app.canvas = mockCanvas;
  });

  describe('initBreedPicker', () => {
    test('should initialize breed search input listener', () => {
      app.initBreedPicker();

      expect(mockContainer.querySelector).toHaveBeenCalledWith('[data-breed-search]');
      expect(mockElement.addEventListener).toHaveBeenCalledWith('input', expect.any(Function));
    });

    test('should initialize breed grid click listener', () => {
      app.initBreedPicker();

      expect(mockContainer.querySelector).toHaveBeenCalledWith('[data-breed-grid]');
      expect(mockElement.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    });
  });

  describe('initPhotoUpload', () => {
    test('should initialize drag and drop listeners', () => {
      app.initPhotoUpload();

      expect(mockElement.addEventListener).toHaveBeenCalledWith('dragover', expect.any(Function));
      expect(mockElement.addEventListener).toHaveBeenCalledWith('dragleave', expect.any(Function));
      expect(mockElement.addEventListener).toHaveBeenCalledWith('drop', expect.any(Function));
    });

    test('should initialize file input change listener', () => {
      app.initPhotoUpload();

      expect(mockElement.addEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    });
  });

  describe('handleBreedSearch', () => {
    test('should search breeds and render results', () => {
      const mockBreeds = [{ id: 'golden-retriever', name: 'Golden Retriever', filename: 'golden-retriever.png' }];

      mockBreedLibrary.searchBreeds.mockReturnValue(mockBreeds);
      app.renderBreeds = jest.fn();

      app.handleBreedSearch('golden');

      expect(mockBreedLibrary.searchBreeds).toHaveBeenCalledWith('golden');
      expect(app.renderBreeds).toHaveBeenCalledWith(mockBreeds);
    });
  });

  describe('selectBreed', () => {
    test('should select breed and update state', async () => {
      const mockBreed = {
        id: 'golden-retriever',
        name: 'Golden Retriever',
        filename: 'golden-retriever.png',
        scale_factor: 1.0,
      };

      mockBreedLibrary.getBreedByName.mockReturnValue(mockBreed);
      app.updateBreedSelection = jest.fn();
      app.clearUploadPreview = jest.fn();
      app.updateAddToCartButton = jest.fn();

      await app.selectBreed('golden-retriever');

      expect(app.state.selectedBreed).toBe(mockBreed);
      expect(app.state.uploadedPhoto).toBeNull();
      expect(app.updateBreedSelection).toHaveBeenCalled();
      expect(app.clearUploadPreview).toHaveBeenCalled();
      expect(mockCanvas.drawBreedLayer).toHaveBeenCalledWith('/assets/breed-golden-retriever.png', 1.0);
      expect(mockCanvas.drawUploadLayer).toHaveBeenCalledWith(null);
      expect(app.updateAddToCartButton).toHaveBeenCalled();
    });
  });

  describe('validateFile', () => {
    test('should validate JPEG file under size limit', () => {
      const file = {
        type: 'image/jpeg',
        size: 1024 * 1024, // 1MB
      };

      const result = app.validateFile(file);

      expect(result).toEqual({ valid: true });
    });

    test('should validate PNG file under size limit', () => {
      const file = {
        type: 'image/png',
        size: 2 * 1024 * 1024, // 2MB
      };

      const result = app.validateFile(file);

      expect(result).toEqual({ valid: true });
    });

    test('should reject file with invalid format', () => {
      const file = {
        type: 'image/gif',
        size: 1024 * 1024,
      };

      const result = app.validateFile(file);

      expect(result).toEqual({ valid: false, error: 'invalid_format' });
    });

    test('should reject file over size limit', () => {
      const file = {
        type: 'image/jpeg',
        size: 6 * 1024 * 1024, // 6MB
      };

      const result = app.validateFile(file);

      expect(result).toEqual({ valid: false, error: 'file_too_large' });
    });
  });

  describe('selectFrame', () => {
    test('should select frame and update state', async () => {
      const mockFrame = {
        id: 'classic',
        name: 'Classic Frame',
        filename: 'classic.png',
        base_price: 15.0,
      };

      mockFrameLibrary.getFrameByName.mockReturnValue(mockFrame);
      app.updateFrameSelection = jest.fn();
      app.updatePricing = jest.fn();
      app.updateAddToCartButton = jest.fn();

      await app.selectFrame('classic');

      expect(app.state.selectedFrame).toBe(mockFrame);
      expect(app.updateFrameSelection).toHaveBeenCalled();
      expect(mockCanvas.drawFrameLayer).toHaveBeenCalledWith('/assets/frame-classic.png');
      expect(app.updatePricing).toHaveBeenCalled();
      expect(app.updateAddToCartButton).toHaveBeenCalled();
    });
  });

  describe('selectSize', () => {
    test('should select size and update state', () => {
      app.updateSizeSelection = jest.fn();
      app.updatePricing = jest.fn();
      app.updateAddToCartButton = jest.fn();

      app.selectSize('medium');

      expect(app.state.selectedSize).toEqual({
        id: 'medium',
        name: 'Medium (11x14)',
        price_multiplier: 1.5,
      });
      expect(app.updateSizeSelection).toHaveBeenCalled();
      expect(app.updatePricing).toHaveBeenCalled();
      expect(app.updateAddToCartButton).toHaveBeenCalled();
    });
  });

  describe('updatePricing', () => {
    test('should calculate and display total price', () => {
      app.state.selectedFrame = { base_price: 20.0 };
      app.state.selectedSize = { price_multiplier: 1.5 };

      mockFrameLibrary.calculateFramePrice.mockReturnValue(30.0);

      const mockTotalDisplay = { textContent: '' };
      const mockBreakdown = { innerHTML: '' };

      mockContainer.querySelector.mockReturnValueOnce(mockTotalDisplay).mockReturnValueOnce(mockBreakdown);

      app.updatePricing();

      expect(mockFrameLibrary.calculateFramePrice).toHaveBeenCalledWith(
        app.state.selectedFrame,
        app.state.selectedSize
      );
      expect(app.state.currentPrice).toBe(30.0);
      expect(mockTotalDisplay.textContent).toBe('Total: $30.00');
    });
  });

  describe('isValidConfiguration', () => {
    test('should return true for valid configuration with breed', () => {
      app.state.selectedBreed = { id: 'golden-retriever' };
      app.state.selectedFrame = { id: 'classic' };
      app.state.selectedSize = { id: 'medium' };

      expect(app.isValidConfiguration()).toBe(true);
    });

    test('should return true for valid configuration with upload', () => {
      app.state.uploadedPhoto = new Image();
      app.state.selectedFrame = { id: 'classic' };
      app.state.selectedSize = { id: 'medium' };

      expect(app.isValidConfiguration()).toBe(true);
    });

    test('should return false without pet selection', () => {
      app.state.selectedFrame = { id: 'classic' };
      app.state.selectedSize = { id: 'medium' };

      expect(app.isValidConfiguration()).toBe(false);
    });

    test('should return false without frame selection', () => {
      app.state.selectedBreed = { id: 'golden-retriever' };
      app.state.selectedSize = { id: 'medium' };

      expect(app.isValidConfiguration()).toBe(false);
    });

    test('should return false without size selection', () => {
      app.state.selectedBreed = { id: 'golden-retriever' };
      app.state.selectedFrame = { id: 'classic' };

      expect(app.isValidConfiguration()).toBe(false);
    });
  });

  describe('reset', () => {
    test('should reset all state and UI', () => {
      app.state.selectedBreed = { id: 'golden-retriever' };
      app.state.selectedFrame = { id: 'classic' };

      app.clearBreedSelection = jest.fn();
      app.clearUploadPreview = jest.fn();
      app.clearFrameSelection = jest.fn();
      app.clearSizeSelection = jest.fn();
      app.updatePricing = jest.fn();
      app.updateAddToCartButton = jest.fn();
      app.renderBreeds = jest.fn();

      const mockSearchInput = { value: 'test' };
      mockContainer.querySelector.mockReturnValue(mockSearchInput);

      app.reset();

      expect(app.state.selectedBreed).toBeNull();
      expect(app.state.selectedFrame).toBeNull();
      expect(app.state.selectedSize).toBeNull();
      expect(app.state.currentPrice).toBe(0);

      expect(mockCanvas.reset).toHaveBeenCalled();
      expect(app.clearBreedSelection).toHaveBeenCalled();
      expect(app.clearUploadPreview).toHaveBeenCalled();
      expect(app.clearFrameSelection).toHaveBeenCalled();
      expect(app.clearSizeSelection).toHaveBeenCalled();
      expect(app.updatePricing).toHaveBeenCalled();
      expect(app.updateAddToCartButton).toHaveBeenCalled();

      expect(mockSearchInput.value).toBe('');
      expect(app.renderBreeds).toHaveBeenCalled();
    });
  });
});
