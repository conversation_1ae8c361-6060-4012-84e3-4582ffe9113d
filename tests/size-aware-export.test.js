/**
 * Size-Aware Canvas Export Test Suite
 * Tests the implementation of size-aware export functionality
 */

describe('Size-Aware Canvas Export', () => {
  let mockPortraitApp;
  let mockCanvas;
  let portraitCanvas;

  beforeEach(() => {
    // Create mock canvas element
    mockCanvas = {
      width: 500,
      height: 500,
      toDataURL: jest.fn(() => 'data:image/png;base64,mock-data'),
      toBlob: jest.fn((callback) => callback(new Blob(['mock-blob'], { type: 'image/png' }))),
      getContext: jest.fn(() => ({
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high',
        fillStyle: '#ffffff',
        fillRect: jest.fn(),
        drawImage: jest.fn()
      }))
    };

    // Create mock portrait app with size selection
    mockPortraitApp = {
      state: {
        selectedSize: null
      }
    };

    // Mock document.createElement for export canvas
    global.document = {
      createElement: jest.fn(() => mockCanvas)
    };

    // Create PortraitCanvas instance (assuming it's available in test environment)
    portraitCanvas = {
      canvas: mockCanvas,
      portraitApp: null,
      setPortraitApp: function(app) { this.portraitApp = app; },
      getExportDimensions: function() {
        let width = this.canvas.width;
        let height = this.canvas.height;
        
        if (this.portraitApp && this.portraitApp.state.selectedSize) {
          const selectedSize = this.portraitApp.state.selectedSize;
          const dimensions = this.calculateSizeBasedDimensions(selectedSize);
          width = dimensions.width;
          height = dimensions.height;
        }
        
        return { width, height };
      },
      calculateSizeBasedDimensions: function(size) {
        const dimensionMatch = size.dimensions.match(/(\d+)"\s*×\s*(\d+)"/);
        if (!dimensionMatch) {
          return { width: this.canvas.width, height: this.canvas.height };
        }
        
        const widthInches = parseInt(dimensionMatch[1]);
        const heightInches = parseInt(dimensionMatch[2]);
        const dpi = 150;
        
        return {
          width: Math.round(widthInches * dpi),
          height: Math.round(heightInches * dpi)
        };
      },
      createSizeAwareExportCanvas: function(dimensions) {
        const exportCanvas = document.createElement('canvas');
        exportCanvas.width = dimensions.width;
        exportCanvas.height = dimensions.height;
        return exportCanvas;
      },
      exportAsDataURL: function(format = 'image/png', quality = 0.9) {
        const exportDimensions = this.getExportDimensions();
        
        if (exportDimensions.width === this.canvas.width && exportDimensions.height === this.canvas.height) {
          return this.canvas.toDataURL(format, quality);
        }
        
        const exportCanvas = this.createSizeAwareExportCanvas(exportDimensions);
        return exportCanvas.toDataURL(format, quality);
      },
      exportAsBlob: function(format = 'image/png', quality = 0.9) {
        return new Promise((resolve) => {
          const exportDimensions = this.getExportDimensions();
          
          if (exportDimensions.width === this.canvas.width && exportDimensions.height === this.canvas.height) {
            this.canvas.toBlob(resolve, format, quality);
            return;
          }
          
          const exportCanvas = this.createSizeAwareExportCanvas(exportDimensions);
          exportCanvas.toBlob(resolve, format, quality);
        });
      }
    };

    // Connect portrait app to canvas
    portraitCanvas.setPortraitApp(mockPortraitApp);
  });

  describe('Size-Based Dimension Calculations', () => {
    test('should calculate correct dimensions for Small (8x10)', () => {
      const size = { id: 'small', name: 'Small (8x10)', dimensions: '8" × 10"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      expect(dimensions.width).toBe(1200); // 8 * 150 DPI
      expect(dimensions.height).toBe(1500); // 10 * 150 DPI
    });

    test('should calculate correct dimensions for Medium (11x14)', () => {
      const size = { id: 'medium', name: 'Medium (11x14)', dimensions: '11" × 14"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      expect(dimensions.width).toBe(1650); // 11 * 150 DPI
      expect(dimensions.height).toBe(2100); // 14 * 150 DPI
    });

    test('should calculate correct dimensions for Large (16x20)', () => {
      const size = { id: 'large', name: 'Large (16x20)', dimensions: '16" × 20"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      expect(dimensions.width).toBe(2400); // 16 * 150 DPI
      expect(dimensions.height).toBe(3000); // 20 * 150 DPI
    });

    test('should calculate correct dimensions for Extra Large (20x24)', () => {
      const size = { id: 'xlarge', name: 'Extra Large (20x24)', dimensions: '20" × 24"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      expect(dimensions.width).toBe(3000); // 20 * 150 DPI
      expect(dimensions.height).toBe(3600); // 24 * 150 DPI
    });

    test('should handle invalid dimensions gracefully', () => {
      const size = { id: 'invalid', name: 'Invalid Size', dimensions: 'invalid format' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      expect(dimensions.width).toBe(500); // Default canvas width
      expect(dimensions.height).toBe(500); // Default canvas height
    });
  });

  describe('Export Dimension Detection', () => {
    test('should return default dimensions when no size is selected', () => {
      mockPortraitApp.state.selectedSize = null;
      const dimensions = portraitCanvas.getExportDimensions();
      
      expect(dimensions.width).toBe(500); // Default canvas width
      expect(dimensions.height).toBe(500); // Default canvas height
    });

    test('should return size-based dimensions when size is selected', () => {
      mockPortraitApp.state.selectedSize = { 
        id: 'medium', 
        name: 'Medium (11x14)', 
        dimensions: '11" × 14"' 
      };
      
      const dimensions = portraitCanvas.getExportDimensions();
      
      expect(dimensions.width).toBe(1650); // 11 * 150 DPI
      expect(dimensions.height).toBe(2100); // 14 * 150 DPI
    });
  });

  describe('Size-Aware Export Methods', () => {
    test('should use current canvas when no size scaling needed', () => {
      mockPortraitApp.state.selectedSize = null;
      
      const result = portraitCanvas.exportAsDataURL();
      
      expect(mockCanvas.toDataURL).toHaveBeenCalledWith('image/png', 0.9);
      expect(result).toBe('data:image/png;base64,mock-data');
    });

    test('should create scaled export canvas when size scaling needed', () => {
      mockPortraitApp.state.selectedSize = { 
        id: 'large', 
        name: 'Large (16x20)', 
        dimensions: '16" × 20"' 
      };
      
      const result = portraitCanvas.exportAsDataURL();
      
      expect(global.document.createElement).toHaveBeenCalledWith('canvas');
      expect(result).toBe('data:image/png;base64,mock-data');
    });

    test('should handle blob export with size scaling', async () => {
      mockPortraitApp.state.selectedSize = { 
        id: 'small', 
        name: 'Small (8x10)', 
        dimensions: '8" × 10"' 
      };
      
      const blob = await portraitCanvas.exportAsBlob();
      
      expect(blob).toBeInstanceOf(Blob);
      expect(blob.type).toBe('image/png');
    });
  });

  describe('Integration with Portrait App', () => {
    test('should connect to portrait app successfully', () => {
      expect(portraitCanvas.portraitApp).toBe(mockPortraitApp);
    });

    test('should access size selection from portrait app state', () => {
      const testSize = { id: 'test', name: 'Test Size', dimensions: '12" × 16"' };
      mockPortraitApp.state.selectedSize = testSize;
      
      const dimensions = portraitCanvas.getExportDimensions();
      
      expect(dimensions.width).toBe(1800); // 12 * 150 DPI
      expect(dimensions.height).toBe(2400); // 16 * 150 DPI
    });
  });

  describe('Aspect Ratio Preservation', () => {
    test('should preserve 4:5 aspect ratio for 8x10 size', () => {
      const size = { id: 'small', name: 'Small (8x10)', dimensions: '8" × 10"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      const aspectRatio = dimensions.width / dimensions.height;
      expect(aspectRatio).toBeCloseTo(0.8, 2); // 8/10 = 0.8
    });

    test('should preserve 11:14 aspect ratio for 11x14 size', () => {
      const size = { id: 'medium', name: 'Medium (11x14)', dimensions: '11" × 14"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      const aspectRatio = dimensions.width / dimensions.height;
      expect(aspectRatio).toBeCloseTo(0.786, 2); // 11/14 ≈ 0.786
    });

    test('should preserve 4:5 aspect ratio for 16x20 size', () => {
      const size = { id: 'large', name: 'Large (16x20)', dimensions: '16" × 20"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      const aspectRatio = dimensions.width / dimensions.height;
      expect(aspectRatio).toBeCloseTo(0.8, 2); // 16/20 = 0.8
    });

    test('should preserve 5:6 aspect ratio for 20x24 size', () => {
      const size = { id: 'xlarge', name: 'Extra Large (20x24)', dimensions: '20" × 24"' };
      const dimensions = portraitCanvas.calculateSizeBasedDimensions(size);
      
      const aspectRatio = dimensions.width / dimensions.height;
      expect(aspectRatio).toBeCloseTo(0.833, 2); // 20/24 ≈ 0.833
    });
  });
});
