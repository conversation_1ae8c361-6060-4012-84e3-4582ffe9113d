/**
 * Unit tests for Google Analytics integration in Royal Portrait Builder
 * Tests P3-T4: Add Google Analytics tracking for portrait customizations
 */

// Mock Google Analytics
global.gtag = jest.fn();
global.ga = jest.fn();
global.ShopifyAnalytics = {
  lib: {
    track: jest.fn(),
  },
};

// Mock DOM
global.document = global.document || {};
global.document.dispatchEvent = jest.fn();

// Mock CustomEvent for JSDOM compatibility
global.CustomEvent = jest.fn().mockImplementation((type, options) => {
  const event = new Event(type);
  if (options && options.detail) {
    event.detail = options.detail;
  }
  return event;
});

// Import the analytics class
// Fix: Use correct import path (recurring issue from debugging analysis)
const { PortraitAnalytics } = require('../assets/royal-portrait-complete.js');

describe('PortraitAnalytics - P3-T4 Implementation', () => {
  let analytics;

  beforeEach(() => {
    jest.clearAllMocks();
    analytics = new PortraitAnalytics();
  });

  describe('trackBreedSelected', () => {
    test('should track breed selection with proper GA4 parameters', () => {
      const breed = { id: 'golden-retriever', name: '<PERSON> Retriever', category: 'large' };
      const searchQuery = 'golden';

      analytics.trackBreedSelected(breed, searchQuery);

      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_breed_selected',
        expect.objectContaining({
          event_category: 'Portrait Builder',
          breed_id: 'golden-retriever',
          breed_name: 'Golden Retriever',
          search_query: 'golden',
          step_number: 1,
          step_name: 'breed_selection',
        })
      );
    });

    test('should track browse vs search method', () => {
      const breed = { id: 'labrador', name: 'Labrador' };

      analytics.trackBreedSelected(breed, null);

      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_breed_selected',
        expect.objectContaining({
          search_method: 'browse',
        })
      );
    });
  });

  describe('trackPhotoUploaded', () => {
    test('should track photo upload with file metadata', () => {
      const uploadInfo = {
        size: 1024000, // 1MB
        type: 'image/jpeg',
        name: 'my-pet.jpg',
      };

      analytics.trackPhotoUploaded(uploadInfo);

      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_photo_uploaded',
        expect.objectContaining({
          event_category: 'Portrait Builder',
          file_size: 1024000,
          file_size_kb: 1000,
          file_size_category: 'medium',
          file_type: 'image/jpeg',
          step_number: 1,
          step_name: 'pet_selection_photo',
        })
      );
    });

    test('should categorize file sizes correctly', () => {
      const smallFile = { size: 100000, type: 'image/png', name: 'small.png' }; // 100KB
      const largeFile = { size: 3000000, type: 'image/jpeg', name: 'large.jpg' }; // 3MB

      analytics.trackPhotoUploaded(smallFile);
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_photo_uploaded',
        expect.objectContaining({ file_size_category: 'small' })
      );

      analytics.trackPhotoUploaded(largeFile);
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_photo_uploaded',
        expect.objectContaining({ file_size_category: 'large' })
      );
    });
  });

  describe('trackFrameSelected', () => {
    test('should track frame selection with pricing data', () => {
      const frame = {
        id: 'ornate-gold',
        name: 'Ornate Gold',
        base_price: 25,
        style: 'ornate',
      };

      analytics.trackFrameSelected(frame);

      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_frame_selected',
        expect.objectContaining({
          event_category: 'Portrait Builder',
          frame_id: 'ornate-gold',
          frame_name: 'Ornate Gold',
          frame_price: 25,
          price_category: 'premium',
          frame_style: 'ornate',
          step_number: 3,
          step_name: 'frame_selection',
          value: 25,
          currency: 'USD',
        })
      );
    });

    test('should categorize frame prices correctly', () => {
      const freeFrame = { id: 'basic', name: 'Basic', base_price: 0 };
      const budgetFrame = { id: 'simple', name: 'Simple', base_price: 15 };
      const premiumFrame = { id: 'luxury', name: 'Luxury', base_price: 50 };

      analytics.trackFrameSelected(freeFrame);
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_frame_selected',
        expect.objectContaining({ price_category: 'free' })
      );

      analytics.trackFrameSelected(budgetFrame);
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_frame_selected',
        expect.objectContaining({ price_category: 'budget' })
      );

      analytics.trackFrameSelected(premiumFrame);
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_frame_selected',
        expect.objectContaining({ price_category: 'premium' })
      );
    });
  });

  describe('trackSizeSelected', () => {
    test('should track size selection with price impact', () => {
      const size = {
        id: 'large',
        name: 'Large',
        price: 20,
      };

      analytics.trackSizeSelected(size, 20);

      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_size_selected',
        expect.objectContaining({
          event_category: 'Portrait Builder',
          size_id: 'large',
          size_name: 'Large',
          size_price: 20,
          size_category: 'large',
          price_impact: 20,
          step_number: 4,
          step_name: 'size_selection',
          value: 20,
          currency: 'USD',
        })
      );
    });

    test('should categorize sizes correctly', () => {
      const sizes = [
        { id: 'small', name: 'Small', price: 0 },
        { id: 'medium', name: 'Medium', price: 10 },
        { id: 'large', name: 'Large', price: 20 },
        { id: 'xlarge', name: 'Extra Large', price: 35 },
        { id: 'custom', name: 'Custom Size', price: 50 },
      ];

      sizes.forEach((size) => {
        analytics.trackSizeSelected(size, size.price);
        expect(gtag).toHaveBeenCalledWith(
          'event',
          'portrait_size_selected',
          expect.objectContaining({
            size_category: size.id === 'custom' ? 'custom' : size.id,
          })
        );
      });
    });
  });

  describe('trackAddToCart', () => {
    test('should track complete portrait configuration', () => {
      const portraitConfig = {
        breed: { name: 'Golden Retriever' },
        frame: { name: 'Ornate Gold', style: 'ornate' },
        size: { name: 'Large' },
        photo: 'custom_upload',
      };
      const totalPrice = 75;

      analytics.trackAddToCart(portraitConfig, totalPrice);

      // Should track standard e-commerce event
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'add_to_cart',
        expect.objectContaining({
          currency: 'USD',
          value: 75,
          items: expect.arrayContaining([
            expect.objectContaining({
              item_id: 'portrait_custom',
              item_name: 'Custom Pet Portrait',
              item_category: 'Portrait',
              quantity: 1,
              price: 75,
            }),
          ]),
        })
      );

      // Should track custom portrait event
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_add_to_cart',
        expect.objectContaining({
          event_category: 'Portrait Builder',
          value: 75,
          currency: 'USD',
          step_number: 5,
          step_name: 'add_to_cart',
          breed_name: 'Golden Retriever',
          frame_name: 'Ornate Gold',
          size_name: 'Large',
          has_custom_photo: true,
          photo_type: 'custom',
          configuration_complete: true,
        })
      );
    });

    test('should distinguish between custom photo and breed illustration', () => {
      const configWithPhoto = {
        breed: { name: 'Labrador' },
        frame: { name: 'Classic' },
        size: { name: 'Medium' },
        photo: 'custom_upload',
      };

      const configWithoutPhoto = {
        breed: { name: 'Labrador' },
        frame: { name: 'Classic' },
        size: { name: 'Medium' },
        photo: 'breed_only',
      };

      analytics.trackAddToCart(configWithPhoto, 50);
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_add_to_cart',
        expect.objectContaining({
          has_custom_photo: true,
          photo_type: 'custom',
        })
      );

      analytics.trackAddToCart(configWithoutPhoto, 50);
      expect(gtag).toHaveBeenCalledWith(
        'event',
        'portrait_add_to_cart',
        expect.objectContaining({
          has_custom_photo: false,
          photo_type: 'breed_illustration',
        })
      );
    });
  });

  describe('Shopify Analytics Integration - P3-T5', () => {
    test('should track events in Shopify Analytics', () => {
      const breed = { id: 'poodle', name: 'Poodle' };
      analytics.trackBreedSelected(breed);

      expect(ShopifyAnalytics.lib.track).toHaveBeenCalledWith(
        'Portrait Breed Selected',
        expect.objectContaining({
          breedId: 'poodle',
          breedName: 'Poodle',
          sessionId: analytics.sessionId,
          stepNumber: 1,
        })
      );
    });

    test('should track enhanced add_to_cart events with portrait metadata', () => {
      const portraitConfig = {
        breed: { id: 'golden-retriever', name: 'Golden Retriever', category: 'large' },
        frame: { id: 'ornate-gold', name: 'Ornate Gold', style: 'ornate', base_price: 25 },
        size: { id: 'large', name: 'Large', price: 20 },
        photo: 'custom_upload',
      };
      const totalPrice = 75;

      analytics.trackAddToCart(portraitConfig, totalPrice);

      // Should track enhanced Shopify e-commerce event
      expect(ShopifyAnalytics.lib.track).toHaveBeenCalledWith(
        'Added Product',
        expect.objectContaining({
          productId: 'portrait_custom',
          productName: 'Custom Pet Portrait',
          productType: 'Custom Portrait',
          productVendor: 'Royal Pet Portraits',
          price: 75,
          currency: 'USD',
          portraitConfiguration: expect.objectContaining({
            breed: expect.objectContaining({
              id: 'golden-retriever',
              name: 'Golden Retriever',
              category: 'large',
            }),
            frame: expect.objectContaining({
              id: 'ornate-gold',
              name: 'Ornate Gold',
              style: 'ornate',
              price: 25,
            }),
            size: expect.objectContaining({
              id: 'large',
              name: 'Large',
              price: 20,
            }),
            photo: expect.objectContaining({
              type: 'custom',
              hasCustomPhoto: true,
              source: 'customer_upload',
            }),
          }),
          customAttributes: expect.objectContaining({
            portrait_breed: 'Golden Retriever',
            portrait_frame: 'Ornate Gold',
            portrait_size: 'Large',
            portrait_photo_type: 'custom',
            portrait_total_price: 75,
            portrait_configuration_complete: true,
          }),
        })
      );

      // Should track portrait configuration completed event
      expect(ShopifyAnalytics.lib.track).toHaveBeenCalledWith(
        'Portrait Configuration Completed',
        expect.objectContaining({
          eventCategory: 'Portrait Builder',
          eventAction: 'Configuration Completed',
          eventLabel: 'Golden Retriever - Ornate Gold - Large',
          value: 75,
          currency: 'USD',
          configurationSteps: 5,
          hasCustomPhoto: true,
          portraitType: 'custom_photo',
        })
      );
    });

    test('should track portrait builder session start', () => {
      analytics.trackPortraitBuilderStart();

      expect(ShopifyAnalytics.lib.track).toHaveBeenCalledWith(
        'Portrait Builder Started',
        expect.objectContaining({
          eventCategory: 'Portrait Builder',
          eventAction: 'Session Started',
          sessionId: analytics.sessionId,
        })
      );
    });

    test('should track funnel progression', () => {
      analytics.trackShopifyFunnelProgression(2, 'photo_upload', {
        fileSize: 1024000,
        fileType: 'image/jpeg',
      });

      expect(ShopifyAnalytics.lib.track).toHaveBeenCalledWith(
        'Portrait Builder Funnel',
        expect.objectContaining({
          eventCategory: 'Portrait Builder',
          eventAction: 'Funnel Progression',
          eventLabel: 'Step 2: photo_upload',
          stepNumber: 2,
          stepName: 'photo_upload',
          funnelPosition: '2/5',
          fileSize: 1024000,
          fileType: 'image/jpeg',
        })
      );
    });

    test('should track conversion events', () => {
      const conversionData = {
        totalPrice: 50,
        portraitConfig: { breed: { name: 'Labrador' } },
        conversionType: 'portrait_purchase',
        funnelCompleted: true,
      };

      analytics.trackShopifyConversion(conversionData);

      expect(ShopifyAnalytics.lib.track).toHaveBeenCalledWith(
        'Portrait Builder Conversion',
        expect.objectContaining({
          eventCategory: 'Portrait Builder',
          eventAction: 'Conversion Completed',
          eventLabel: 'Portrait Added to Cart',
          conversionValue: 50,
          currency: 'USD',
          ...conversionData,
        })
      );
    });

    test('should track abandonment events', () => {
      analytics.trackPortraitBuilderAbandonment(2, 'user_left_page');

      expect(ShopifyAnalytics.lib.track).toHaveBeenCalledWith(
        'Portrait Builder Abandoned',
        expect.objectContaining({
          eventCategory: 'Portrait Builder',
          eventAction: 'Session Abandoned',
          eventLabel: 'Abandoned at Step 2',
          lastCompletedStep: 2,
          abandonmentReason: 'user_left_page',
          completionRate: 40, // 2/5 * 100
        })
      );
    });
  });

  describe('Custom Events', () => {
    test('should dispatch custom DOM events', () => {
      const breed = { id: 'beagle', name: 'Beagle' };
      analytics.trackBreedSelected(breed);

      expect(CustomEvent).toHaveBeenCalledWith(
        'portrait:breed_selected',
        expect.objectContaining({
          detail: expect.objectContaining({
            breed: breed,
            sessionId: analytics.sessionId,
          }),
          bubbles: true,
          cancelable: true,
        })
      );

      expect(document.dispatchEvent).toHaveBeenCalled();
    });
  });
});
